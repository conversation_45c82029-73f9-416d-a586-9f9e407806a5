{"cli": {"version": ">= 16.17.4", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"buildType": "apk"}}, "development-simple": {"developmentClient": true, "distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}, "env": {"EXPO_PUBLIC_FORCE_REAL_STREAMING": "false", "EXPO_PUBLIC_USE_ENHANCED_MOCK": "true"}}, "preview": {"distribution": "internal"}, "production": {"autoIncrement": true}, "development-simulator": {"developmentClient": true, "distribution": "internal", "ios": {"simulator": true}, "environment": "development"}}, "submit": {"production": {}}}