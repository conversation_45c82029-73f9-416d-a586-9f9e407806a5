{"version": 3, "names": ["_reactNative", "require", "_EventEmitter", "_<PERSON>gger", "_interopRequireDefault", "_MediaDevices", "_MediaStream", "_MediaStreamTrack", "_MediaStreamTrackEvent", "_Permissions", "_RTCAudioSession", "_RTCErrorEvent", "_RTCIceCandidate", "_RTCPIPView", "_interopRequireWildcard", "_RTCPeerConnection", "_RTCRtpReceiver", "_RTCRtpSender", "_RTCRtpTransceiver", "_RTCSessionDescription", "_RTC<PERSON>iew", "_ScreenCapturePickerView", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "WebRTCModule", "NativeModules", "Error", "Platform", "OS", "<PERSON><PERSON>", "enable", "ROOT_PREFIX", "setupNativeEvents", "registerGlobals", "global", "navigator", "mediaDevices", "getUserMedia", "bind", "getDisplayMedia", "enumerateDevices", "RTCIceCandidate", "RTCPeerConnection", "RTCRtpReceiver", "RTCRtpSender", "RTCSessionDescription", "MediaStream", "MediaStreamTrack", "MediaStreamTrackEvent", "RTCRtpTransceiver", "RTCErrorEvent"], "sources": ["index.ts"], "sourcesContent": ["import { NativeModules, Platform } from 'react-native';\nconst { WebRTCModule } = NativeModules;\n\nif (WebRTCModule === null) {\n    throw new Error(`WebRTC native module not found.\\n${Platform.OS === 'ios' ?\n        'Try executing the \"pod install\" command inside your projects ios folder.' :\n        'Try executing the \"npm install\" command inside your projects folder.'\n    }`);\n}\n\nimport { setupNativeEvents } from './EventEmitter';\nimport Logger from './Logger';\nimport mediaDevices from './MediaDevices';\nimport MediaStream from './MediaStream';\nimport MediaStreamTrack, { type MediaTrackSettings } from './MediaStreamTrack';\nimport MediaStreamTrackEvent from './MediaStreamTrackEvent';\nimport permissions from './Permissions';\nimport RTCAudioSession from './RTCAudioSession';\nimport RTCErrorEvent from './RTCErrorEvent';\nimport RTCIceCandidate from './RTCIceCandidate';\nimport RTCPIPView, { startIOSPIP, stopIOSPIP } from './RTCPIPView';\nimport RTCPeerConnection from './RTCPeerConnection';\nimport RTCRtpReceiver from './RTCRtpReceiver';\nimport RTCRtpSender from './RTCRtpSender';\nimport RTCRtpTransceiver from './RTCRtpTransceiver';\nimport RTCSessionDescription from './RTCSessionDescription';\nimport RTCView, { type RTCVideoViewProps, type RTCIOSPIPOptions } from './RTCView';\nimport ScreenCapturePickerView from './ScreenCapturePickerView';\n\nLogger.enable(`${Logger.ROOT_PREFIX}:*`);\n\n// Add listeners for the native events early, since they are added asynchronously.\nsetupNativeEvents();\n\nexport {\n    RTCIceCandidate,\n    RTCPeerConnection,\n    RTCSessionDescription,\n    RTCView,\n    RTCPIPView,\n    ScreenCapturePickerView,\n    RTCRtpTransceiver,\n    RTCRtpReceiver,\n    RTCRtpSender,\n    RTCErrorEvent,\n    RTCAudioSession,\n    MediaStream,\n    MediaStreamTrack,\n    type MediaTrackSettings,\n    type RTCVideoViewProps,\n    type RTCIOSPIPOptions,\n    mediaDevices,\n    permissions,\n    registerGlobals,\n    startIOSPIP,\n    stopIOSPIP,\n};\n\ndeclare const global: any;\n\nfunction registerGlobals(): void {\n    // Should not happen. React Native has a global navigator object.\n    if (typeof global.navigator !== 'object') {\n        throw new Error('navigator is not an object');\n    }\n\n    if (!global.navigator.mediaDevices) {\n        global.navigator.mediaDevices = {};\n    }\n\n    global.navigator.mediaDevices.getUserMedia = mediaDevices.getUserMedia.bind(mediaDevices);\n    global.navigator.mediaDevices.getDisplayMedia = mediaDevices.getDisplayMedia.bind(mediaDevices);\n    global.navigator.mediaDevices.enumerateDevices = mediaDevices.enumerateDevices.bind(mediaDevices);\n\n    global.RTCIceCandidate = RTCIceCandidate;\n    global.RTCPeerConnection = RTCPeerConnection;\n    global.RTCRtpReceiver = RTCRtpReceiver;\n    global.RTCRtpSender = RTCRtpReceiver;\n    global.RTCSessionDescription = RTCSessionDescription;\n    global.MediaStream = MediaStream;\n    global.MediaStreamTrack = MediaStreamTrack;\n    global.MediaStreamTrackEvent = MediaStreamTrackEvent;\n    global.RTCRtpTransceiver = RTCRtpTransceiver;\n    global.RTCRtpReceiver = RTCRtpReceiver;\n    global.RTCRtpSender = RTCRtpSender;\n    global.RTCErrorEvent = RTCErrorEvent;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAUA,IAAAC,aAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,aAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,YAAA,GAAAF,sBAAA,CAAAH,OAAA;AACA,IAAAM,iBAAA,GAAAH,sBAAA,CAAAH,OAAA;AACA,IAAAO,sBAAA,GAAAJ,sBAAA,CAAAH,OAAA;AACA,IAAAQ,YAAA,GAAAL,sBAAA,CAAAH,OAAA;AACA,IAAAS,gBAAA,GAAAN,sBAAA,CAAAH,OAAA;AACA,IAAAU,cAAA,GAAAP,sBAAA,CAAAH,OAAA;AACA,IAAAW,gBAAA,GAAAR,sBAAA,CAAAH,OAAA;AACA,IAAAY,WAAA,GAAAC,uBAAA,CAAAb,OAAA;AACA,IAAAc,kBAAA,GAAAX,sBAAA,CAAAH,OAAA;AACA,IAAAe,eAAA,GAAAZ,sBAAA,CAAAH,OAAA;AACA,IAAAgB,aAAA,GAAAb,sBAAA,CAAAH,OAAA;AACA,IAAAiB,kBAAA,GAAAd,sBAAA,CAAAH,OAAA;AACA,IAAAkB,sBAAA,GAAAf,sBAAA,CAAAH,OAAA;AACA,IAAAmB,QAAA,GAAAhB,sBAAA,CAAAH,OAAA;AACA,IAAAoB,wBAAA,GAAAjB,sBAAA,CAAAH,OAAA;AAAgE,SAAAqB,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAT,wBAAAa,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAAA,SAAA7B,uBAAAuB,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AA1BhE,MAAM;EAAEiB;AAAa,CAAC,GAAGC,0BAAa;AAEtC,IAAID,YAAY,KAAK,IAAI,EAAE;EACvB,MAAM,IAAIE,KAAK,CAAE,oCAAmCC,qBAAQ,CAACC,EAAE,KAAK,KAAK,GACrE,0EAA0E,GAC1E,sEACH,EAAC,CAAC;AACP;AAqBAC,eAAM,CAACC,MAAM,CAAE,GAAED,eAAM,CAACE,WAAY,IAAG,CAAC;;AAExC;AACA,IAAAC,+BAAiB,EAAC,CAAC;AA4BnB,SAASC,eAAeA,CAAA,EAAS;EAC7B;EACA,IAAI,OAAOC,MAAM,CAACC,SAAS,KAAK,QAAQ,EAAE;IACtC,MAAM,IAAIT,KAAK,CAAC,4BAA4B,CAAC;EACjD;EAEA,IAAI,CAACQ,MAAM,CAACC,SAAS,CAACC,YAAY,EAAE;IAChCF,MAAM,CAACC,SAAS,CAACC,YAAY,GAAG,CAAC,CAAC;EACtC;EAEAF,MAAM,CAACC,SAAS,CAACC,YAAY,CAACC,YAAY,GAAGD,qBAAY,CAACC,YAAY,CAACC,IAAI,CAACF,qBAAY,CAAC;EACzFF,MAAM,CAACC,SAAS,CAACC,YAAY,CAACG,eAAe,GAAGH,qBAAY,CAACG,eAAe,CAACD,IAAI,CAACF,qBAAY,CAAC;EAC/FF,MAAM,CAACC,SAAS,CAACC,YAAY,CAACI,gBAAgB,GAAGJ,qBAAY,CAACI,gBAAgB,CAACF,IAAI,CAACF,qBAAY,CAAC;EAEjGF,MAAM,CAACO,eAAe,GAAGA,wBAAe;EACxCP,MAAM,CAACQ,iBAAiB,GAAGA,0BAAiB;EAC5CR,MAAM,CAACS,cAAc,GAAGA,uBAAc;EACtCT,MAAM,CAACU,YAAY,GAAGD,uBAAc;EACpCT,MAAM,CAACW,qBAAqB,GAAGA,8BAAqB;EACpDX,MAAM,CAACY,WAAW,GAAGA,oBAAW;EAChCZ,MAAM,CAACa,gBAAgB,GAAGA,yBAAgB;EAC1Cb,MAAM,CAACc,qBAAqB,GAAGA,8BAAqB;EACpDd,MAAM,CAACe,iBAAiB,GAAGA,0BAAiB;EAC5Cf,MAAM,CAACS,cAAc,GAAGA,uBAAc;EACtCT,MAAM,CAACU,YAAY,GAAGA,qBAAY;EAClCV,MAAM,CAACgB,aAAa,GAAGA,sBAAa;AACxC"}