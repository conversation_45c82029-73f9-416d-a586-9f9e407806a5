{"version": 3, "names": ["RTCRtcpParameters", "RTCRtpCodecParameters", "RTCRtpHeaderExtension", "deepClone", "RTCRtpParameters", "constructor", "init", "_defineProperty", "codec", "codecs", "push", "ext", "headerExtensions", "rtcp", "toJSON", "map", "c", "he"], "sources": ["RTCRtpParameters.ts"], "sourcesContent": ["import RTCRtcpParameters, { RTCRtcpParametersInit } from './RTCRtcpParameters';\nimport RTCRtpCodecParameters, { RTCRtpCodecParametersInit } from './RTCRtpCodecParameters';\nimport RTCRtpHeaderExtension, { RTCRtpHeaderExtensionInit } from './RTCRtpHeaderExtension';\nimport { deepClone } from './RTCUtil';\n\n\nexport interface RTCRtpParametersInit {\n    codecs: RTCRtpCodecParametersInit[],\n    headerExtensions: RTCRtpHeaderExtensionInit[],\n    rtcp: RTCRtcpParametersInit\n}\n\nexport default class RTCRtpParameters {\n    codecs: (RTCRtpCodecParameters | RTCRtpCodecParametersInit)[] = [];\n    readonly headerExtensions: RTCRtpHeaderExtension[] = [];\n    readonly rtcp: RTCRtcpParameters;\n\n    constructor(init: RTCRtpParametersInit) {\n        for (const codec of init.codecs) {\n            this.codecs.push(new RTCRtpCodecParameters(codec));\n        }\n\n        for (const ext of init.headerExtensions) {\n            this.headerExtensions.push(new RTCRtpHeaderExtension(ext));\n        }\n\n        this.rtcp = new RTCRtcpParameters(init.rtcp);\n    }\n\n    toJSON() {\n        return {\n            codecs: this.codecs.map(c => deepClone(c)),\n            headerExtensions: this.headerExtensions.map(he => deepClone(he)),\n            rtcp: deepClone(this.rtcp)\n        };\n    }\n}\n"], "mappings": ";AAAA,OAAOA,iBAAiB,MAAiC,qBAAqB;AAC9E,OAAOC,qBAAqB,MAAqC,yBAAyB;AAC1F,OAAOC,qBAAqB,MAAqC,yBAAyB;AAC1F,SAASC,SAAS,QAAQ,WAAW;AASrC,eAAe,MAAMC,gBAAgB,CAAC;EAKlCC,WAAWA,CAACC,IAA0B,EAAE;IAAAC,eAAA,iBAJwB,EAAE;IAAAA,eAAA,2BACb,EAAE;IAAAA,eAAA;IAInD,KAAK,MAAMC,KAAK,IAAIF,IAAI,CAACG,MAAM,EAAE;MAC7B,IAAI,CAACA,MAAM,CAACC,IAAI,CAAC,IAAIT,qBAAqB,CAACO,KAAK,CAAC,CAAC;IACtD;IAEA,KAAK,MAAMG,GAAG,IAAIL,IAAI,CAACM,gBAAgB,EAAE;MACrC,IAAI,CAACA,gBAAgB,CAACF,IAAI,CAAC,IAAIR,qBAAqB,CAACS,GAAG,CAAC,CAAC;IAC9D;IAEA,IAAI,CAACE,IAAI,GAAG,IAAIb,iBAAiB,CAACM,IAAI,CAACO,IAAI,CAAC;EAChD;EAEAC,MAAMA,CAAA,EAAG;IACL,OAAO;MACHL,MAAM,EAAE,IAAI,CAACA,MAAM,CAACM,GAAG,CAACC,CAAC,IAAIb,SAAS,CAACa,CAAC,CAAC,CAAC;MAC1CJ,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAACG,GAAG,CAACE,EAAE,IAAId,SAAS,CAACc,EAAE,CAAC,CAAC;MAChEJ,IAAI,EAAEV,SAAS,CAAC,IAAI,CAACU,IAAI;IAC7B,CAAC;EACL;AACJ"}