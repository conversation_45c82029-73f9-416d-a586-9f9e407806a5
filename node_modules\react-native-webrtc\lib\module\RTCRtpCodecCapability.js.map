{"version": 3, "names": ["RTCRtpCodecCapability", "constructor", "init", "_defineProperty", "_mimeType", "mimeType", "Object", "freeze"], "sources": ["RTCRtpCodecCapability.ts"], "sourcesContent": ["\nexport default class RTCRtpCodecCapability {\n    _mimeType: string;\n\n    constructor(init: { mimeType: string }) {\n        this._mimeType = init.mimeType;\n        Object.freeze(this);\n    }\n\n    get mimeType() {\n        return this._mimeType;\n    }\n}"], "mappings": ";AACA,eAAe,MAAMA,qBAAqB,CAAC;EAGvCC,WAAWA,CAACC,IAA0B,EAAE;IAAAC,eAAA;IACpC,IAAI,CAACC,SAAS,GAAGF,IAAI,CAACG,QAAQ;IAC9BC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACvB;EAEA,IAAIF,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACD,SAAS;EACzB;AACJ"}