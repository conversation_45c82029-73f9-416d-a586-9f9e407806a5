{"version": 3, "names": ["_reactNative", "require", "_EventEmitter", "_interopRequireDefault", "obj", "__esModule", "default", "WebRTCModule", "NativeModules", "nativeEmitter", "NativeEventEmitter", "NATIVE_EVENTS", "eventEmitter", "EventEmitter", "setupNativeEvents", "eventName", "addListener", "_len", "arguments", "length", "args", "Array", "_key", "emit", "_subscriptions", "Map", "listener", "<PERSON><PERSON><PERSON><PERSON>", "_subscriptions$get", "includes", "Error", "has", "set", "get", "push", "removeListener", "_subscriptions$get2", "for<PERSON>ach", "sub", "remove", "delete"], "sources": ["EventEmitter.ts"], "sourcesContent": ["import { NativeModules, NativeEventEmitter, EmitterSubscription } from 'react-native';\n// @ts-ignore\nimport EventEmitter from 'react-native/Libraries/vendor/emitter/EventEmitter';\n\nconst { WebRTCModule } = NativeModules;\n\n// This emitter is going to be used to listen to all the native events (once) and then\n// re-emit them on a JS-only emitter.\nconst nativeEmitter = new NativeEventEmitter(WebRTCModule);\n\nconst NATIVE_EVENTS = [\n    'peerConnectionSignalingStateChanged',\n    'peerConnectionStateChanged',\n    'peerConnectionOnRenegotiationNeeded',\n    'peerConnectionIceConnectionChanged',\n    'peerConnectionIceGatheringChanged',\n    'peerConnectionGotICECandidate',\n    'peerConnectionDidOpenDataChannel',\n    'peerConnectionOnRemoveTrack',\n    'peerConnectionOnTrack',\n    'dataChannelStateChanged',\n    'dataChannelReceiveMessage',\n    'dataChannelDidChangeBufferedAmount',\n    'mediaStreamTrackMuteChanged',\n    'mediaStreamTrackEnded',\n];\n\nconst eventEmitter = new EventEmitter();\n\nexport function setupNativeEvents() {\n    for (const eventName of NATIVE_EVENTS) {\n        nativeEmitter.addListener(eventName, (...args) => {\n            eventEmitter.emit(eventName, ...args);\n        });\n    }\n}\n\ntype EventHandler = (event: unknown) => void;\ntype Listener = unknown;\n\nconst _subscriptions: Map<Listener, EmitterSubscription[]> = new Map();\n\nexport function addListener(listener: Listener, eventName: string, eventHandler: EventHandler): void {\n    if (!NATIVE_EVENTS.includes(eventName)) {\n        throw new Error(`Invalid event: ${eventName}`);\n    }\n\n    if (!_subscriptions.has(listener)) {\n        _subscriptions.set(listener, []);\n    }\n\n    _subscriptions.get(listener)?.push(eventEmitter.addListener(eventName, eventHandler));\n}\n\nexport function removeListener(listener: Listener): void {\n    _subscriptions.get(listener)?.forEach(sub => {\n        sub.remove();\n    });\n\n    _subscriptions.delete(listener);\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEA,IAAAC,aAAA,GAAAC,sBAAA,CAAAF,OAAA;AAA8E,SAAAE,uBAAAC,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAD9E;;AAGA,MAAM;EAAEG;AAAa,CAAC,GAAGC,0BAAa;;AAEtC;AACA;AACA,MAAMC,aAAa,GAAG,IAAIC,+BAAkB,CAACH,YAAY,CAAC;AAE1D,MAAMI,aAAa,GAAG,CAClB,qCAAqC,EACrC,4BAA4B,EAC5B,qCAAqC,EACrC,oCAAoC,EACpC,mCAAmC,EACnC,+BAA+B,EAC/B,kCAAkC,EAClC,6BAA6B,EAC7B,uBAAuB,EACvB,yBAAyB,EACzB,2BAA2B,EAC3B,oCAAoC,EACpC,6BAA6B,EAC7B,uBAAuB,CAC1B;AAED,MAAMC,YAAY,GAAG,IAAIC,qBAAY,CAAC,CAAC;AAEhC,SAASC,iBAAiBA,CAAA,EAAG;EAChC,KAAK,MAAMC,SAAS,IAAIJ,aAAa,EAAE;IACnCF,aAAa,CAACO,WAAW,CAACD,SAAS,EAAE,YAAa;MAAA,SAAAE,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAATC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;MAAA;MACzCV,YAAY,CAACW,IAAI,CAACR,SAAS,EAAE,GAAGK,IAAI,CAAC;IACzC,CAAC,CAAC;EACN;AACJ;AAKA,MAAMI,cAAoD,GAAG,IAAIC,GAAG,CAAC,CAAC;AAE/D,SAAST,WAAWA,CAACU,QAAkB,EAAEX,SAAiB,EAAEY,YAA0B,EAAQ;EAAA,IAAAC,kBAAA;EACjG,IAAI,CAACjB,aAAa,CAACkB,QAAQ,CAACd,SAAS,CAAC,EAAE;IACpC,MAAM,IAAIe,KAAK,CAAE,kBAAiBf,SAAU,EAAC,CAAC;EAClD;EAEA,IAAI,CAACS,cAAc,CAACO,GAAG,CAACL,QAAQ,CAAC,EAAE;IAC/BF,cAAc,CAACQ,GAAG,CAACN,QAAQ,EAAE,EAAE,CAAC;EACpC;EAEA,CAAAE,kBAAA,GAAAJ,cAAc,CAACS,GAAG,CAACP,QAAQ,CAAC,cAAAE,kBAAA,uBAA5BA,kBAAA,CAA8BM,IAAI,CAACtB,YAAY,CAACI,WAAW,CAACD,SAAS,EAAEY,YAAY,CAAC,CAAC;AACzF;AAEO,SAASQ,cAAcA,CAACT,QAAkB,EAAQ;EAAA,IAAAU,mBAAA;EACrD,CAAAA,mBAAA,GAAAZ,cAAc,CAACS,GAAG,CAACP,QAAQ,CAAC,cAAAU,mBAAA,uBAA5BA,mBAAA,CAA8BC,OAAO,CAACC,GAAG,IAAI;IACzCA,GAAG,CAACC,MAAM,CAAC,CAAC;EAChB,CAAC,CAAC;EAEFf,cAAc,CAACgB,MAAM,CAACd,QAAQ,CAAC;AACnC"}