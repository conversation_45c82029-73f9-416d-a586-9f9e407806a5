{"version": 3, "file": "ExponentConstants.web.js", "sourceRoot": "", "sources": ["../src/ExponentConstants.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,MAAM,CAAC;AAEpC,OAAO,EACL,oBAAoB,GAIrB,MAAM,mBAAmB,CAAC;AAE3B,MAAM,MAAM,GAAG,gCAAgC,CAAC;AAQhD,MAAM,UAAU,GAAG,MAAM,EAAE,CAAC;AAE5B,SAAS,cAAc;IACrB,IAAI,QAAQ,CAAC,cAAc,EAAE;QAC3B,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;QAChD,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC1B,OAAO,MAAM,CAAC;SACf;aAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAChC,OAAO,eAAe,CAAC;SACxB;aAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACnD,OAAO,OAAO,CAAC;SAChB;aAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YACzD,OAAO,QAAQ,CAAC;SACjB;aAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;YACpC,OAAO,IAAI,CAAC;SACb;aAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;YACpC,OAAO,SAAS,CAAC;SAClB;aAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACnC,OAAO,QAAQ,CAAC;SACjB;KACF;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,eAAe;IACb,IAAI,IAAI;QACN,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IACD,IAAI,YAAY;QACd,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,oBAAoB;QACtB,OAAO,oBAAoB,CAAC,IAAI,CAAC;IACnC,CAAC;IACD,IAAI,cAAc;QAChB,IAAI,cAAc,CAAC;QACnB,IAAI;YACF,cAAc,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC9C,IAAI,cAAc,IAAI,IAAI,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE;gBAChE,cAAc,GAAG,MAAM,EAAE,CAAC;gBAC1B,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,cAAwB,CAAC,CAAC;aACxD;SACF;QAAC,MAAM;YACN,cAAc,GAAG,UAAU,CAAC;SAC7B;gBAAS;YACR,OAAO,cAAc,CAAC;SACvB;IACH,CAAC;IACD,IAAI,SAAS;QACX,OAAO,UAAU,CAAC;IACpB,CAAC;IACD,IAAI,QAAQ;QACV,OAAO,EAAE,GAAG,EAAE,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;IACpF,CAAC;IACD,IAAI,UAAU;QACZ,IAAI,CAAC,QAAQ,CAAC,cAAc;YAAE,OAAO,IAAI,CAAC;QAE1C,OAAO,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IACxD,CAAC;IACD,IAAI,QAAQ;QACV,qEAAqE;QACrE,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,QAAS,CAAC,UAAU,IAAI,IAAI,CAAC;IAC3C,CAAC;IACD,IAAI,UAAU;QACZ,IAAI,QAAQ,CAAC,cAAc,EAAE;YAC3B,6BAA6B;YAC7B,mEAAmE;YACnE,OAAO,QAAQ,CAAC,MAAM,CAAC;SACxB;aAAM;YACL,OAAO,EAAE,CAAC;SACX;IACH,CAAC;IACD,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IACD,IAAI,UAAU;QACZ,OAAO,cAAc,EAAE,CAAC;IAC1B,CAAC;IACD,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,WAAW;QACb,+BAA+B;QAC/B,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAI,eAAe;QACjB,OAAO,CAAC,CAAC;IACX,CAAC;IACD,IAAI,eAAe;QACjB,wGAAwG;QACxG,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,QAAQ;QACV,2CAA2C;QAC3C,8GAA8G;QAC9G,OAAO,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE,CAAC;IACxC,CAAC;IACD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,aAAa;QACf,IAAI,QAAQ,CAAC,cAAc,EAAE;YAC3B,OAAO,QAAQ,CAAC,MAAM,CAAC;SACxB;aAAM;YACL,OAAO,EAAE,CAAC;SACX;IACH,CAAC;IACD,IAAI,SAAS;QACX,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,KAAK,CAAC,wBAAwB;QAC5B,IAAI,QAAQ,CAAC,cAAc,EAAE;YAC3B,OAAO,SAAS,CAAC,SAAS,CAAC;SAC5B;aAAM;YACL,OAAO,IAAI,CAAC;SACb;IACH,CAAC;CACiB,CAAC", "sourcesContent": ["import { Platform } from 'expo-modules-core';\nimport { v4 as uuidv4 } from 'uuid';\n\nimport {\n  ExecutionEnvironment,\n  NativeConstants,\n  PlatformManifest,\n  WebManifest,\n} from './Constants.types';\n\nconst ID_KEY = 'EXPO_CONSTANTS_INSTALLATION_ID';\n\ndeclare let __DEV__: boolean;\ndeclare let process: { env: any };\ndeclare let navigator: Navigator;\ndeclare let location: Location;\ndeclare let localStorage: Storage;\n\nconst _sessionId = uuidv4();\n\nfunction getBrowserName(): string | undefined {\n  if (Platform.isDOMAvailable) {\n    const agent = navigator.userAgent.toLowerCase();\n    if (agent.includes('edge')) {\n      return 'Edge';\n    } else if (agent.includes('edg')) {\n      return 'Chromium Edge';\n    } else if (agent.includes('opr') && !!window['opr']) {\n      return 'Opera';\n    } else if (agent.includes('chrome') && !!window['chrome']) {\n      return 'Chrome';\n    } else if (agent.includes('trident')) {\n      return 'IE';\n    } else if (agent.includes('firefox')) {\n      return 'Firefox';\n    } else if (agent.includes('safari')) {\n      return 'Safari';\n    }\n  }\n\n  return undefined;\n}\n\nexport default {\n  get name(): string {\n    return 'ExponentConstants';\n  },\n  get appOwnership() {\n    return null;\n  },\n  get executionEnvironment() {\n    return ExecutionEnvironment.Bare;\n  },\n  get installationId(): string {\n    let installationId;\n    try {\n      installationId = localStorage.getItem(ID_KEY);\n      if (installationId == null || typeof installationId !== 'string') {\n        installationId = uuidv4();\n        localStorage.setItem(ID_KEY, installationId as string);\n      }\n    } catch {\n      installationId = _sessionId;\n    } finally {\n      return installationId;\n    }\n  },\n  get sessionId(): string {\n    return _sessionId;\n  },\n  get platform(): PlatformManifest {\n    return { web: Platform.isDOMAvailable ? { ua: navigator.userAgent } : undefined };\n  },\n  get isHeadless(): boolean {\n    if (!Platform.isDOMAvailable) return true;\n\n    return /\\bHeadlessChrome\\//.test(navigator.userAgent);\n  },\n  get isDevice(): true {\n    // TODO: Bacon: Possibly want to add information regarding simulators\n    return true;\n  },\n  get expoVersion(): string | null {\n    return this.manifest!.sdkVersion || null;\n  },\n  get linkingUri(): string {\n    if (Platform.isDOMAvailable) {\n      // On native this is `exp://`\n      // On web we should use the protocol and hostname (location.origin)\n      return location.origin;\n    } else {\n      return '';\n    }\n  },\n  get expoRuntimeVersion(): string | null {\n    return this.expoVersion;\n  },\n  get deviceName(): string | undefined {\n    return getBrowserName();\n  },\n  get nativeAppVersion(): null {\n    return null;\n  },\n  get nativeBuildVersion(): null {\n    return null;\n  },\n  get systemFonts(): string[] {\n    // TODO: Bacon: Maybe possible.\n    return [];\n  },\n  get statusBarHeight(): number {\n    return 0;\n  },\n  get deviceYearClass(): number | null {\n    // TODO: Bacon: The android version isn't very accurate either, maybe we could try and guess this value.\n    return null;\n  },\n  get manifest(): WebManifest {\n    // This is defined by @expo/webpack-config.\n    // If your site is bundled with a different config then you may not have access to the app.json automatically.\n    return process.env.APP_MANIFEST || {};\n  },\n  get manifest2(): null {\n    return null;\n  },\n  get experienceUrl(): string {\n    if (Platform.isDOMAvailable) {\n      return location.origin;\n    } else {\n      return '';\n    }\n  },\n  get debugMode(): boolean {\n    return __DEV__;\n  },\n  async getWebViewUserAgentAsync(): Promise<string | null> {\n    if (Platform.isDOMAvailable) {\n      return navigator.userAgent;\n    } else {\n      return null;\n    }\n  },\n} as NativeConstants;\n"]}