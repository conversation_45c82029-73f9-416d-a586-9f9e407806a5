{"version": 3, "names": ["requireNativeComponent"], "sources": ["RTCView.ts"], "sourcesContent": ["import { requireNativeComponent, ViewProps } from 'react-native';\n\n/**\n * Native prop validation was removed from RN in:\n * https://github.com/facebook/react-native/commit/8dc3ba0444c94d9bbb66295b5af885bff9b9cd34\n *\n * So we list them here for documentation purposes.\n */\nexport interface RTCVideoViewProps extends ViewProps {\n  /**\n   * Indicates whether the video specified by {@link #streamURL} should be\n   * mirrored during rendering. Commonly, applications choose to mirror the\n   * user-facing camera.\n   *\n   * mirror: boolean\n   */\n  mirror?: boolean;\n\n  /**\n   * In the fashion of\n   * https://www.w3.org/TR/html5/embedded-content-0.html#dom-video-videowidth\n   * and https://www.w3.org/TR/html5/rendering.html#video-object-fit,\n   * resembles the CSS style object-fit.\n   *\n   * objectFit: 'contain' | 'cover'\n   *\n   * Defaults to 'cover'.\n   */\n  objectFit?: 'contain' | 'cover';\n\n  /**\n   * URL / id of the stream that should be rendered.\n   *\n   * streamURL: string\n   */\n  streamURL?: string;\n  /**\n   * Similarly to the CSS property z-index, specifies the z-order of this\n   * RTCView in the stacking space of all RTCViews. When RTCViews overlap,\n   * zOrder determines which one covers the other. An RTCView with a larger\n   * zOrder generally covers an RTCView with a lower one.\n   *\n   * Non-overlapping RTCViews may safely share a z-order (because one does not\n   * have to cover the other).\n   *\n   * The support for zOrder is platform-dependent and/or\n   * implementation-specific. Thus, specifying a value for zOrder is to be\n   * thought of as giving a hint rather than as imposing a requirement. For\n   * example, video renderers such as RTCView are commonly implemented using\n   * OpenGL and OpenGL views may have different numbers of layers in their\n   * stacking space. Android has three: a layer bellow the window (aka\n   * default), a layer bellow the window again but above the previous layer\n   * (aka media overlay), and above the window. Consequently, it is advisable\n   * to limit the number of utilized layers in the stacking space to the\n   * minimum sufficient for the desired display. For example, a video call\n   * application usually needs a maximum of two zOrder values: 0 for the\n   * remote video(s) which appear in the background, and 1 for the local\n   * video(s) which appear above the remote video(s).\n   *\n   * zOrder: number\n   */\n  zOrder?: number;\n\n\n  /**\n   * Picture in picture options for this view. Disabled if not supplied.\n   *\n   * Note: this should only be generally only used with remote video tracks,\n   * as the local camera may stop while in the background.\n   *\n   * iOS only. Requires iOS 15.0 or above, and the PIP background mode capability.\n   */\n  iosPIP?: RTCIOSPIPOptions;\n\n  /**\n   * Callback function that is called when the dimensions of the video change.\n   *\n   * @param {Object} event - The event object containing the new dimensions.\n   * @param {Object} event.nativeEvent - The native event data.\n   * @param {number} event.nativeEvent.width - The width of the video.\n   * @param {number} event.nativeEvent.height - The height of the video.\n   */\n  onDimensionsChange?: (event: { nativeEvent: { width: number; height: number } }) => void;\n}\n\nexport interface RTCIOSPIPOptions {\n\n  /**\n   * Whether PIP can be launched from this view.\n   *\n   * Defaults to true.\n   */\n  enabled?: boolean;\n\n  /**\n   * The preferred size of the PIP window.\n   */\n  preferredSize?: {\n    width: number;\n    height: number;\n  },\n\n  /**\n   * Indicates whether Picture in Picture starts automatically\n   * when the controller embeds its content inline and the app\n   * transitions to the background.\n   *\n   * Defaults to true.\n   *\n   * See: AVPictureInPictureController.canStartPictureInPictureAutomaticallyFromInline\n   */\n  startAutomatically?: boolean;\n\n  /**\n   * Indicates whether Picture in Picture should stop automatically\n   * when the app returns to the foreground.\n   *\n   * Defaults to true.\n   */\n  stopAutomatically?: boolean;\n}\nexport default requireNativeComponent<RTCVideoViewProps>('RTCVideoView');\n"], "mappings": "AAAA,SAASA,sBAAsB,QAAmB,cAAc;;AAEhE;AACA;AACA;AACA;AACA;AACA;;AAkHA,eAAeA,sBAAsB,CAAoB,cAAc,CAAC"}