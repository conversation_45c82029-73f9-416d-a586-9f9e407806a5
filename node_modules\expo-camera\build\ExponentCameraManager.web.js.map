{"version": 3, "file": "ExponentCameraManager.web.js", "sourceRoot": "", "sources": ["../src/ExponentCameraManager.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,EAGL,UAAU,EAEV,gBAAgB,GACjB,MAAM,gBAAgB,CAAC;AAExB,OAAO,EACL,eAAe,EACf,0BAA0B,EAC1B,2BAA2B,GAC5B,MAAM,uBAAuB,CAAC;AAE/B,SAAS,YAAY,CAAC,WAAmC;IACvD,IAAI,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,YAAY,CAAC,YAAY,EAAE;QACjE,OAAO,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;KACzD;IAED,iFAAiF;IACjF,+DAA+D;IAC/D,oEAAoE;IAEpE,yDAAyD;IACzD,MAAM,YAAY;IAChB,yHAAyH;IACzH,SAAS,CAAC,YAAY;QACtB,SAAS,CAAC,kBAAkB;QAC5B,SAAS,CAAC,eAAe;QACzB;YACE,MAAM,KAAK,GAAQ,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YACzD,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;YACf,KAAK,CAAC,IAAI,GAAG,iBAAiB,CAAC;YAC/B,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;IAEJ,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,uBAAuB,CAAC,EAAE,OAAO,EAAuB;IAC/D,wBAAwB;IACxB,UAAU;IACV,IAAI,OAAO,KAAK,sBAAsB,EAAE;QACtC,OAAO;YACL,MAAM,EAAE,gBAAgB,CAAC,YAAY;YACrC,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,KAAK;SACf,CAAC;KACH;SAAM;QACL,6DAA6D;QAC7D,gDAAgD;QAChD,OAAO;YACL,MAAM,EAAE,gBAAgB,CAAC,MAAM;YAC/B,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,KAAK;SACf,CAAC;KACH;AACH,CAAC;AAED,KAAK,UAAU,6BAA6B;IAC1C,IAAI;QACF,MAAM,YAAY,CAAC;YACjB,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;QACH,OAAO;YACL,MAAM,EAAE,gBAAgB,CAAC,OAAO;YAChC,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,IAAI;SACd,CAAC;KACH;IAAC,OAAO,EAAE,OAAO,EAAE,EAAE;QACpB,OAAO,uBAAuB,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;KAC7C;AACH,CAAC;AAED,KAAK,UAAU,2BAA2B,CACxC,KAA8B;IAE9B,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE;QAClC,MAAM,IAAI,mBAAmB,CAAC,aAAa,EAAE,4CAA4C,CAAC,CAAC;KAC5F;IAED,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;IACrE,QAAQ,KAAK,EAAE;QACb,KAAK,QAAQ;YACX,OAAO;gBACL,MAAM,EAAE,gBAAgB,CAAC,YAAY;gBACrC,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,IAAI;gBACjB,OAAO,EAAE,KAAK;aACf,CAAC;QACJ,KAAK,SAAS;YACZ,OAAO;gBACL,MAAM,EAAE,gBAAgB,CAAC,OAAO;gBAChC,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,IAAI;gBACjB,OAAO,EAAE,IAAI;aACd,CAAC;QACJ,KAAK,QAAQ;YACX,OAAO;gBACL,MAAM,EAAE,gBAAgB,CAAC,MAAM;gBAC/B,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,IAAI;gBACjB,OAAO,EAAE,KAAK;aACf,CAAC;KACL;AACH,CAAC;AAED,eAAe;IACb,IAAI,IAAI;QACN,OAAO,uBAAuB,CAAC;IACjC,CAAC;IACD,IAAI,IAAI;QACN,OAAO;YACL,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,OAAO;SACf,CAAC;IACJ,CAAC;IACD,IAAI,SAAS;QACX,OAAO;YACL,EAAE,EAAE,IAAI;YACR,GAAG,EAAE,KAAK;YACV,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,OAAO;SACf,CAAC;IACJ,CAAC;IACD,IAAI,SAAS;QACX,OAAO;YACL,EAAE,EAAE,IAAI;YACR,GAAG,EAAE,KAAK;YACV,IAAI,EAAE,MAAM;YACZ,UAAU,EAAE,YAAY;SACzB,CAAC;IACJ,CAAC;IACD,IAAI,YAAY;QACd,OAAO;YACL,IAAI,EAAE,MAAM;YACZ,UAAU,EAAE,YAAY;YACxB,MAAM,EAAE,QAAQ;SACjB,CAAC;IACJ,CAAC;IACD,IAAI,YAAY;QACd,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAI,kBAAkB;QACpB,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,KAAK,CAAC,gBAAgB;QACpB,OAAO,eAAe,EAAE,CAAC;IAC3B,CAAC;IACD,KAAK,CAAC,WAAW,CACf,OAA6B,EAC7B,MAAyB;QAEzB,OAAO,MAAM,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC3C,CAAC;IACD,KAAK,CAAC,YAAY,CAAC,MAAyB;QAC1C,MAAM,MAAM,CAAC,YAAY,EAAE,CAAC;IAC9B,CAAC;IACD,KAAK,CAAC,aAAa,CAAC,MAAyB;QAC3C,OAAO,MAAM,MAAM,CAAC,aAAa,EAAE,CAAC;IACtC,CAAC;IACD,KAAK,CAAC,4BAA4B;QAChC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,gBAAgB;YAAE,OAAO,EAAE,CAAC;QAE9E,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;QAEhE,MAAM,KAAK,GAAsB,MAAM,OAAO,CAAC,GAAG,CAAC;YACjD,CAAC,MAAM,2BAA2B,CAAC,OAAO,CAAC,CAAC,IAAI,UAAU,CAAC,KAAK;YAChE,CAAC,MAAM,0BAA0B,EAAE,CAAC,IAAI,UAAU,CAAC,IAAI;SACxD,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC,MAAM,CAAC,OAAO,CAAa,CAAC;IAC3C,CAAC;IACD,KAAK,CAAC,wBAAwB,CAAC,KAAa,EAAE,MAAyB;QACrE,OAAO,MAAM,MAAM,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;IACtD,CAAC;IACD;;;;;;;;;;;SAWK;IACL,KAAK,CAAC,mBAAmB;QACvB,OAAO,2BAA2B,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IACD,KAAK,CAAC,uBAAuB;QAC3B,OAAO,6BAA6B,EAAE,CAAC;IACzC,CAAC;IACD,KAAK,CAAC,yBAAyB;QAC7B,OAAO,2BAA2B,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IACD,KAAK,CAAC,6BAA6B;QACjC,OAAO,6BAA6B,EAAE,CAAC;IACzC,CAAC;IACD,KAAK,CAAC,6BAA6B;QACjC,OAAO,2BAA2B,CAAC,YAAY,CAAC,CAAC;IACnD,CAAC;IACD,KAAK,CAAC,iCAAiC;QACrC,IAAI;YACF,MAAM,YAAY,CAAC;gBACjB,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;YACH,OAAO;gBACL,MAAM,EAAE,gBAAgB,CAAC,OAAO;gBAChC,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,IAAI;gBACjB,OAAO,EAAE,IAAI;aACd,CAAC;SACH;QAAC,OAAO,EAAE,OAAO,EAAE,EAAE;YACpB,OAAO,uBAAuB,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;SAC7C;IACH,CAAC;CACF,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport {\n  CameraCapturedPicture,\n  CameraPictureOptions,\n  CameraType,\n  PermissionResponse,\n  PermissionStatus,\n} from './Camera.types';\nimport { ExponentCameraRef } from './ExponentCamera.web';\nimport {\n  canGetUserMedia,\n  isBackCameraAvailableAsync,\n  isFrontCameraAvailableAsync,\n} from './WebUserMediaManager';\n\nfunction getUserMedia(constraints: MediaStreamConstraints): Promise<MediaStream> {\n  if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\n    return navigator.mediaDevices.getUserMedia(constraints);\n  }\n\n  // Some browsers partially implement mediaDevices. We can't just assign an object\n  // with getUserMedia as it would overwrite existing properties.\n  // Here, we will just add the getUserMedia property if it's missing.\n\n  // First get ahold of the legacy getUserMedia, if present\n  const getUserMedia =\n    // TODO: this method is deprecated, migrate to https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia\n    navigator.getUserMedia ||\n    navigator.webkitGetUserMedia ||\n    navigator.mozGetUserMedia ||\n    function () {\n      const error: any = new Error('Permission unimplemented');\n      error.code = 0;\n      error.name = 'NotAllowedError';\n      throw error;\n    };\n\n  return new Promise((resolve, reject) => {\n    getUserMedia.call(navigator, constraints, resolve, reject);\n  });\n}\n\nfunction handleGetUserMediaError({ message }: { message: string }): PermissionResponse {\n  // name: NotAllowedError\n  // code: 0\n  if (message === 'Permission dismissed') {\n    return {\n      status: PermissionStatus.UNDETERMINED,\n      expires: 'never',\n      canAskAgain: true,\n      granted: false,\n    };\n  } else {\n    // TODO: Bacon: [OSX] The system could deny access to chrome.\n    // TODO: Bacon: add: { status: 'unimplemented' }\n    return {\n      status: PermissionStatus.DENIED,\n      expires: 'never',\n      canAskAgain: true,\n      granted: false,\n    };\n  }\n}\n\nasync function handleRequestPermissionsAsync(): Promise<PermissionResponse> {\n  try {\n    await getUserMedia({\n      video: true,\n    });\n    return {\n      status: PermissionStatus.GRANTED,\n      expires: 'never',\n      canAskAgain: true,\n      granted: true,\n    };\n  } catch ({ message }) {\n    return handleGetUserMediaError({ message });\n  }\n}\n\nasync function handlePermissionsQueryAsync(\n  query: 'camera' | 'microphone'\n): Promise<PermissionResponse> {\n  if (!navigator?.permissions?.query) {\n    throw new UnavailabilityError('expo-camera', 'navigator.permissions API is not available');\n  }\n\n  const { state } = await navigator.permissions.query({ name: query });\n  switch (state) {\n    case 'prompt':\n      return {\n        status: PermissionStatus.UNDETERMINED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: false,\n      };\n    case 'granted':\n      return {\n        status: PermissionStatus.GRANTED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: true,\n      };\n    case 'denied':\n      return {\n        status: PermissionStatus.DENIED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: false,\n      };\n  }\n}\n\nexport default {\n  get name(): string {\n    return 'ExponentCameraManager';\n  },\n  get Type() {\n    return {\n      back: 'back',\n      front: 'front',\n    };\n  },\n  get FlashMode() {\n    return {\n      on: 'on',\n      off: 'off',\n      auto: 'auto',\n      torch: 'torch',\n    };\n  },\n  get AutoFocus() {\n    return {\n      on: 'on',\n      off: 'off',\n      auto: 'auto',\n      singleShot: 'singleShot',\n    };\n  },\n  get WhiteBalance() {\n    return {\n      auto: 'auto',\n      continuous: 'continuous',\n      manual: 'manual',\n    };\n  },\n  get VideoQuality() {\n    return {};\n  },\n  get VideoStabilization() {\n    return {};\n  },\n  async isAvailableAsync(): Promise<boolean> {\n    return canGetUserMedia();\n  },\n  async takePicture(\n    options: CameraPictureOptions,\n    camera: ExponentCameraRef\n  ): Promise<CameraCapturedPicture> {\n    return await camera.takePicture(options);\n  },\n  async pausePreview(camera: ExponentCameraRef): Promise<void> {\n    await camera.pausePreview();\n  },\n  async resumePreview(camera: ExponentCameraRef): Promise<void> {\n    return await camera.resumePreview();\n  },\n  async getAvailableCameraTypesAsync(): Promise<string[]> {\n    if (!canGetUserMedia() || !navigator.mediaDevices.enumerateDevices) return [];\n\n    const devices = await navigator.mediaDevices.enumerateDevices();\n\n    const types: (string | null)[] = await Promise.all([\n      (await isFrontCameraAvailableAsync(devices)) && CameraType.front,\n      (await isBackCameraAvailableAsync()) && CameraType.back,\n    ]);\n\n    return types.filter(Boolean) as string[];\n  },\n  async getAvailablePictureSizes(ratio: string, camera: ExponentCameraRef): Promise<string[]> {\n    return await camera.getAvailablePictureSizes(ratio);\n  },\n  /* async getSupportedRatios(camera: ExponentCameraRef): Promise<string[]> {\n    // TODO: Support on web\n  },\n  async record(\n    options?: CameraRecordingOptions,\n    camera: ExponentCameraRef\n  ): Promise<{ uri: string }> {\n    // TODO: Support on web\n  },\n  async stopRecording(camera: ExponentCameraRef): Promise<void> {\n    // TODO: Support on web\n  }, */\n  async getPermissionsAsync(): Promise<PermissionResponse> {\n    return handlePermissionsQueryAsync('camera');\n  },\n  async requestPermissionsAsync(): Promise<PermissionResponse> {\n    return handleRequestPermissionsAsync();\n  },\n  async getCameraPermissionsAsync(): Promise<PermissionResponse> {\n    return handlePermissionsQueryAsync('camera');\n  },\n  async requestCameraPermissionsAsync(): Promise<PermissionResponse> {\n    return handleRequestPermissionsAsync();\n  },\n  async getMicrophonePermissionsAsync(): Promise<PermissionResponse> {\n    return handlePermissionsQueryAsync('microphone');\n  },\n  async requestMicrophonePermissionsAsync(): Promise<PermissionResponse> {\n    try {\n      await getUserMedia({\n        audio: true,\n      });\n      return {\n        status: PermissionStatus.GRANTED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: true,\n      };\n    } catch ({ message }) {\n      return handleGetUserMediaError({ message });\n    }\n  },\n};\n"]}