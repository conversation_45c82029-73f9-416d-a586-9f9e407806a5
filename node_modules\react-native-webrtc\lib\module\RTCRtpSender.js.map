{"version": 3, "names": ["NativeModules", "RTCRtpSendParameters", "WebRTCModule", "RTCRtpSender", "constructor", "info", "_defineProperty", "_peerConnectionId", "peerConnectionId", "_id", "id", "_rtpParameters", "rtpParameters", "track", "_track", "replaceTrack", "senderReplaceTrack", "e", "getCapabilities", "kind", "senderGetCapabilities", "getParameters", "setParameters", "parameters", "_params", "JSON", "parse", "stringify", "newParameters", "senderSetParameters", "getStats", "senderGetStats", "then", "data", "Map"], "sources": ["RTCRtpSender.ts"], "sourcesContent": ["import { NativeModules } from 'react-native';\n\nimport MediaStreamTrack from './MediaStreamTrack';\nimport RTCRtpCapabilities from './RTCRtpCapabilities';\nimport RTCRtpSendParameters, { RTCRtpSendParametersInit } from './RTCRtpSendParameters';\n\nconst { WebRTCModule } = NativeModules;\n\n\nexport default class RTCRtpSender {\n    _id: string;\n    _track: MediaStreamTrack | null = null;\n    _peerConnectionId: number;\n    _rtpParameters: RTCRtpSendParameters;\n\n    constructor(info: {\n        peerConnectionId: number,\n        id: string,\n        track?: MediaStreamTrack,\n        rtpParameters: RTCRtpSendParametersInit\n    }) {\n        this._peerConnectionId = info.peerConnectionId;\n        this._id = info.id;\n        this._rtpParameters = new RTCRtpSendParameters(info.rtpParameters);\n\n        if (info.track) {\n            this._track = info.track;\n        }\n    }\n\n    async replaceTrack(track: MediaStreamTrack | null): Promise<void> {\n        try {\n            await WebRTCModule.senderReplaceTrack(this._peerConnectionId, this._id, track ? track.id : null);\n        } catch (e) {\n            return;\n        }\n\n        this._track = track;\n    }\n\n    static getCapabilities(kind: 'audio' | 'video'): RTCRtpCapabilities {\n        return WebRTCModule.senderGetCapabilities(kind);\n    }\n\n    getParameters(): RTCRtpSendParameters {\n        return this._rtpParameters;\n    }\n\n    async setParameters(parameters: RTCRtpSendParameters): Promise<void> {\n        // This allows us to get rid of private \"underscore properties\"\n        const _params = JSON.parse(JSON.stringify(parameters));\n        const newParameters = await WebRTCModule.senderSetParameters(this._peerConnectionId, this._id, _params);\n\n        this._rtpParameters = new RTCRtpSendParameters(newParameters);\n    }\n\n    getStats() {\n        return WebRTCModule.senderGetStats(this._peerConnectionId, this._id).then(data =>\n            /* On both Android and iOS it is faster to construct a single\n            JSON string representing the Map of StatsReports and have it\n            pass through the React Native bridge rather than the Map of\n            StatsReports. While the implementations do try to be faster in\n            general, the stress is on being faster to pass through the React\n            Native bridge which is a bottleneck that tends to be visible in\n            the UI when there is congestion involving UI-related passing.\n            */\n            new Map(JSON.parse(data))\n        );\n    }\n\n    get track() {\n        return this._track;\n    }\n\n    get id() {\n        return this._id;\n    }\n}\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,cAAc;AAI5C,OAAOC,oBAAoB,MAAoC,wBAAwB;AAEvF,MAAM;EAAEC;AAAa,CAAC,GAAGF,aAAa;AAGtC,eAAe,MAAMG,YAAY,CAAC;EAM9BC,WAAWA,CAACC,IAKX,EAAE;IAAAC,eAAA;IAAAA,eAAA,iBAT+B,IAAI;IAAAA,eAAA;IAAAA,eAAA;IAUlC,IAAI,CAACC,iBAAiB,GAAGF,IAAI,CAACG,gBAAgB;IAC9C,IAAI,CAACC,GAAG,GAAGJ,IAAI,CAACK,EAAE;IAClB,IAAI,CAACC,cAAc,GAAG,IAAIV,oBAAoB,CAACI,IAAI,CAACO,aAAa,CAAC;IAElE,IAAIP,IAAI,CAACQ,KAAK,EAAE;MACZ,IAAI,CAACC,MAAM,GAAGT,IAAI,CAACQ,KAAK;IAC5B;EACJ;EAEA,MAAME,YAAYA,CAACF,KAA8B,EAAiB;IAC9D,IAAI;MACA,MAAMX,YAAY,CAACc,kBAAkB,CAAC,IAAI,CAACT,iBAAiB,EAAE,IAAI,CAACE,GAAG,EAAEI,KAAK,GAAGA,KAAK,CAACH,EAAE,GAAG,IAAI,CAAC;IACpG,CAAC,CAAC,OAAOO,CAAC,EAAE;MACR;IACJ;IAEA,IAAI,CAACH,MAAM,GAAGD,KAAK;EACvB;EAEA,OAAOK,eAAeA,CAACC,IAAuB,EAAsB;IAChE,OAAOjB,YAAY,CAACkB,qBAAqB,CAACD,IAAI,CAAC;EACnD;EAEAE,aAAaA,CAAA,EAAyB;IAClC,OAAO,IAAI,CAACV,cAAc;EAC9B;EAEA,MAAMW,aAAaA,CAACC,UAAgC,EAAiB;IACjE;IACA,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACJ,UAAU,CAAC,CAAC;IACtD,MAAMK,aAAa,GAAG,MAAM1B,YAAY,CAAC2B,mBAAmB,CAAC,IAAI,CAACtB,iBAAiB,EAAE,IAAI,CAACE,GAAG,EAAEe,OAAO,CAAC;IAEvG,IAAI,CAACb,cAAc,GAAG,IAAIV,oBAAoB,CAAC2B,aAAa,CAAC;EACjE;EAEAE,QAAQA,CAAA,EAAG;IACP,OAAO5B,YAAY,CAAC6B,cAAc,CAAC,IAAI,CAACxB,iBAAiB,EAAE,IAAI,CAACE,GAAG,CAAC,CAACuB,IAAI,CAACC,IAAI;IAC1E;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;IACY,IAAIC,GAAG,CAACT,IAAI,CAACC,KAAK,CAACO,IAAI,CAAC,CAC5B,CAAC;EACL;EAEA,IAAIpB,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EAEA,IAAIJ,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACD,GAAG;EACnB;AACJ"}