{"version": 3, "names": ["_index", "require", "_defineProperty", "obj", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "RTCDataChannelEvent", "Event", "constructor", "type", "eventInitDict", "channel", "exports", "default"], "sources": ["RTCDataChannelEvent.ts"], "sourcesContent": ["import { Event } from 'event-target-shim/index';\n\nimport type RTCDataChannel from './RTCDataChannel';\n\ntype DATA_CHANNEL_EVENTS =  'open'| 'message'| 'bufferedamountlow'| 'closing'| 'close'| 'error' | 'datachannel';\n\ninterface IRTCDataChannelEventInitDict extends Event.EventInit {\n    channel: RTCDataChannel;\n}\n\n\n/**\n * @eventClass\n * This event is fired whenever the RTCDataChannel has changed in any way.\n * @param {DATA_CHANNEL_EVENTS} type - The type of event.\n * @param {IRTCDataChannelEventInitDict} eventInitDict - The event init properties.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/RTCDataChannel#events MDN} for details.\n */\nexport default class RTCDataChannelEvent<\nTEventType extends DATA_CHANNEL_EVENTS\n> extends Event<TEventType> {\n    /** @eventProperty */\n    channel: RTCDataChannel;\n    constructor(type: TEventType, eventInitDict: IRTCDataChannelEventInitDict) {\n        super(type, eventInitDict);\n        this.channel = eventInitDict.channel;\n    }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAAgD,SAAAC,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,QAAAD,GAAA,IAAAD,GAAA,IAAAG,MAAA,CAAAC,cAAA,CAAAJ,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAG,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAP,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAWhD;AACA;AACA;AACA;AACA;AACA;AACA;AACe,MAAMQ,mBAAmB,SAE9BC,YAAK,CAAa;EACxB;;EAEAC,WAAWA,CAACC,IAAgB,EAAEC,aAA2C,EAAE;IACvE,KAAK,CAACD,IAAI,EAAEC,aAAa,CAAC;IAACb,eAAA;IAC3B,IAAI,CAACc,OAAO,GAAGD,aAAa,CAACC,OAAO;EACxC;AACJ;AAACC,OAAA,CAAAC,OAAA,GAAAP,mBAAA"}