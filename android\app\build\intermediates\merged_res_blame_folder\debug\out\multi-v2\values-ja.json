{"logs": [{"outputFile": "com.streamingapp-mergeDebugResources-61:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7c5e03387f63f3788dbd5294d085d57\\transformed\\appcompat-1.4.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,41", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,3991", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,4065"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\01b98716bd18db7c75d98a3148ec3aaa\\transformed\\core-1.8.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "42", "startColumns": "4", "startOffsets": "4070", "endColumns": "100", "endOffsets": "4166"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\808043cb4cf2ad49d6936bcf600e8cf8\\transformed\\biometric-1.1.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,157,242,345,458,572,691,797,913,1009,1128,1247", "endColumns": "101,84,102,112,113,118,105,115,95,118,118,107", "endOffsets": "152,237,340,453,567,686,792,908,1004,1123,1242,1350"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2691,2793,2878,2981,3094,3208,3327,3433,3549,3645,3764,3883", "endColumns": "101,84,102,112,113,118,105,115,95,118,118,107", "endOffsets": "2788,2873,2976,3089,3203,3322,3428,3544,3640,3759,3878,3986"}}]}]}