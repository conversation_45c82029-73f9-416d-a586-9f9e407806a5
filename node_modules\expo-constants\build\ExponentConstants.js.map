{"version": 3, "file": "ExponentConstants.js", "sourceRoot": "", "sources": ["../src/ExponentConstants.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,IAAI,iBAAiB,CAAC;AACtB,IAAI;IACF,iBAAiB,GAAG,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;CAC9D;AAAC,MAAM,GAAE;AACV,eAAe,iBAAiB,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nlet ExponentConstants;\ntry {\n  ExponentConstants = requireNativeModule('ExponentConstants');\n} catch {}\nexport default ExponentConstants;\n"]}