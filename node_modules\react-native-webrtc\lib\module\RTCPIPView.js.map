{"version": 3, "names": ["forwardRef", "ReactNative", "UIManager", "RTCView", "RTCPIPView", "props", "ref", "_rtcViewProps$iosPIP", "_rtcViewProps$iosPIP2", "rtcViewProps", "fallback<PERSON><PERSON><PERSON>", "iosPIP", "React", "createElement", "_extends", "startIOSPIP", "dispatchViewManagerCommand", "findNodeHandle", "current", "getViewManagerConfig", "Commands", "stopIOSPIP"], "sources": ["RTCPIPView.tsx"], "sourcesContent": ["import { Component, forwardRef } from 'react';\nimport ReactNative, { UIManager } from 'react-native';\n\nimport RTCView, { RTCIOSPIPOptions, RTCVideoViewProps } from './RTCView';\n\nexport interface RTCPIPViewProps extends RTCVideoViewProps {\n  iosPIP?: RTCIOSPIPOptions & {\n    fallbackView?: Component;\n  };\n}\n\ntype RTCViewInstance = InstanceType<typeof RTCView>;\n\n/**\n * A convenience wrapper around RTCView to handle the fallback view as a prop.\n */\nconst RTCPIPView = forwardRef<RTCViewInstance, RTCPIPViewProps>((props, ref) => {\n    const rtcViewProps = { ...props };\n    const fallbackView = rtcViewProps.iosPIP?.fallbackView;\n\n    delete rtcViewProps.iosPIP?.fallbackView;\n\n    return (\n        <RTCView ref={ref}\n            {...rtcViewProps}>\n            {fallbackView}\n        </RTCView>\n    );\n});\n\nexport function startIOSPIP(ref) {\n    UIManager.dispatchViewManagerCommand(\n        ReactNative.findNodeHandle(ref.current),\n        UIManager.getViewManagerConfig('RTCVideoView').Commands.startIOSPIP,\n        []\n    );\n}\n\nexport function stopIOSPIP(ref) {\n    UIManager.dispatchViewManagerCommand(\n        ReactNative.findNodeHandle(ref.current),\n        UIManager.getViewManagerConfig('RTCVideoView').Commands.stopIOSPIP,\n        []\n    );\n}\n\nexport default RTCPIPView;"], "mappings": ";AAAA,SAAoBA,UAAU,QAAQ,OAAO;AAC7C,OAAOC,WAAW,IAAIC,SAAS,QAAQ,cAAc;AAErD,OAAOC,OAAO,MAA+C,WAAW;AAUxE;AACA;AACA;AACA,MAAMC,UAAU,gBAAGJ,UAAU,CAAmC,CAACK,KAAK,EAAEC,GAAG,KAAK;EAAA,IAAAC,oBAAA,EAAAC,qBAAA;EAC5E,MAAMC,YAAY,GAAG;IAAE,GAAGJ;EAAM,CAAC;EACjC,MAAMK,YAAY,IAAAH,oBAAA,GAAGE,YAAY,CAACE,MAAM,cAAAJ,oBAAA,uBAAnBA,oBAAA,CAAqBG,YAAY;EAEtD,CAAAF,qBAAA,GAAOC,YAAY,CAACE,MAAM,cAAAH,qBAAA,qBAA1B,OAAOA,qBAAA,CAAqBE,YAAY;EAExC,oBACIE,KAAA,CAAAC,aAAA,CAACV,OAAO,EAAAW,QAAA;IAACR,GAAG,EAAEA;EAAI,GACVG,YAAY,GACfC,YACI,CAAC;AAElB,CAAC,CAAC;AAEF,OAAO,SAASK,WAAWA,CAACT,GAAG,EAAE;EAC7BJ,SAAS,CAACc,0BAA0B,CAChCf,WAAW,CAACgB,cAAc,CAACX,GAAG,CAACY,OAAO,CAAC,EACvChB,SAAS,CAACiB,oBAAoB,CAAC,cAAc,CAAC,CAACC,QAAQ,CAACL,WAAW,EACnE,EACJ,CAAC;AACL;AAEA,OAAO,SAASM,UAAUA,CAACf,GAAG,EAAE;EAC5BJ,SAAS,CAACc,0BAA0B,CAChCf,WAAW,CAACgB,cAAc,CAACX,GAAG,CAACY,OAAO,CAAC,EACvChB,SAAS,CAACiB,oBAAoB,CAAC,cAAc,CAAC,CAACC,QAAQ,CAACC,UAAU,EAClE,EACJ,CAAC;AACL;AAEA,eAAejB,UAAU"}