{"version": 3, "names": ["_index", "require", "_defineProperty", "obj", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "RTCErrorEvent", "Event", "constructor", "type", "func", "message", "exports", "default"], "sources": ["RTCErrorEvent.ts"], "sourcesContent": ["import { Event } from 'event-target-shim/index';\n\ntype RTCPeerConnectionErrorFunc =\n    | 'addTransceiver'\n    | 'getTransceivers'\n    | 'addTrack'\n    | 'removeTrack';\n\n/**\n * @brief This class Represents internal error happening on the native side as\n * part of asynchronous invocations to synchronous web APIs.\n */\nexport default class RTCErrorEvent<TEventType extends RTCPeerConnectionErrorFunc> extends Event<TEventType> {\n    readonly func: RTCPeerConnectionErrorFunc;\n    readonly message: string;\n    constructor(type: TEventType, func: RTCPeerConnectionErrorFunc, message: string) {\n        super(type);\n        this.func = func;\n        this.message = message;\n    }\n}"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAAgD,SAAAC,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,QAAAD,GAAA,IAAAD,GAAA,IAAAG,MAAA,CAAAC,cAAA,CAAAJ,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAG,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAP,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAQhD;AACA;AACA;AACA;AACe,MAAMQ,aAAa,SAAwDC,YAAK,CAAa;EAGxGC,WAAWA,CAACC,IAAgB,EAAEC,IAAgC,EAAEC,OAAe,EAAE;IAC7E,KAAK,CAACF,IAAI,CAAC;IAACZ,eAAA;IAAAA,eAAA;IACZ,IAAI,CAACa,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;AACJ;AAACC,OAAA,CAAAC,OAAA,GAAAP,aAAA"}