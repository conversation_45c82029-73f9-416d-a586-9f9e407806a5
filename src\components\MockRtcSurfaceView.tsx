import React, { useEffect, useRef, useState } from 'react';
import { View, Text, StyleSheet, ViewStyle, Animated, Dimensions, TouchableOpacity } from 'react-native';
import { Camera } from 'expo-camera';

interface MockRtcSurfaceViewProps {
  style?: ViewStyle;
  canvas?: {
    uid: number | null;
  };
  isLocal?: boolean;
}

// Mock RtcSurfaceView for development in Expo Go
const MockRtcSurfaceView: React.FC<MockRtcSurfaceViewProps> = ({ style, canvas, isLocal = false }) => {
  const uid = canvas?.uid;
  const animatedValue = useRef(new Animated.Value(0)).current;
  const pulseValue = useRef(new Animated.Value(1)).current;
  const { width, height } = Dimensions.get('window');

  // Camera state
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [cameraType, setCameraType] = useState(Camera.Constants.Type.front);
  const [cameraReady, setCameraReady] = useState(false);
  const [cameraError, setCameraError] = useState<string | null>(null);
  const [showFallback, setShowFallback] = useState(false);

  useEffect(() => {
    // Request camera permissions for local streams
    const requestCameraPermission = async () => {
      if (isLocal) {
        try {
          console.log('MockRtcSurfaceView: Requesting camera permission for local stream');
          const { status } = await Camera.requestCameraPermissionsAsync();
          setHasPermission(status === 'granted');
          console.log(`MockRtcSurfaceView: Camera permission ${status}`);

          // Set a timeout for camera initialization
          if (status === 'granted') {
            setTimeout(() => {
              if (!cameraReady) {
                console.log('MockRtcSurfaceView: Camera timeout - falling back to mock');
                setShowFallback(true);
              }
            }, 5000); // 5 second timeout
          }
        } catch (error) {
          console.error('MockRtcSurfaceView: Camera permission error:', error);
          setCameraError('Camera permission failed');
          setShowFallback(true);
        }
      }
    };

    requestCameraPermission();

    // Continuous gradient animation
    const gradientAnimation = Animated.loop(
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 3000,
        useNativeDriver: false,
      })
    );

    // Pulse animation for live indicator
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseValue, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );

    gradientAnimation.start();
    pulseAnimation.start();

    return () => {
      gradientAnimation.stop();
      pulseAnimation.stop();
    };
  }, [isLocal]);

  const interpolatedColor = animatedValue.interpolate({
    inputRange: [0, 0.25, 0.5, 0.75, 1],
    outputRange: ['#1a1a2e', '#16213e', '#0f3460', '#533483', '#1a1a2e'],
  });

  const switchCamera = () => {
    console.log('MockRtcSurfaceView: Switching camera');
    setCameraType(
      cameraType === Camera.Constants.Type.back
        ? Camera.Constants.Type.front
        : Camera.Constants.Type.back
    );
  };

  const renderVideoContent = () => {
    if (isLocal) {
      // For local streams, show real camera if permission granted
      if (hasPermission === null) {
        return (
          <View style={styles.localVideoContent}>
            <View style={styles.permissionContainer}>
              <Text style={styles.permissionText}>Requesting camera permission...</Text>
            </View>
          </View>
        );
      }

      if (hasPermission === false || showFallback) {
        return (
          <View style={styles.localVideoContent}>
            <Animated.View
              style={[
                styles.animatedBackground,
                { backgroundColor: interpolatedColor }
              ]}
            />
            <View style={styles.overlay}>
              <View style={styles.topLeftControls}>
                <Animated.View
                  style={[
                    styles.liveIndicator,
                    { transform: [{ scale: pulseValue }] }
                  ]}
                >
                  <Text style={styles.liveText}>● LIVE</Text>
                </Animated.View>
              </View>
              <Text style={styles.overlayText}>You (Broadcasting)</Text>
            </View>
            <View style={styles.centerContent}>
              <Text style={styles.cameraIcon}>📹</Text>
              <Text style={styles.statusText}>
                {hasPermission === false
                  ? 'Camera Permission Required'
                  : showFallback
                  ? 'Camera Simulation'
                  : 'Camera Active'}
              </Text>
              <Text style={styles.permissionText}>
                {hasPermission === false
                  ? 'Grant camera access to show video'
                  : showFallback
                  ? 'Real camera unavailable in Expo Go'
                  : 'Professional streaming mode'}
              </Text>
            </View>
          </View>
        );
      }

      // Show real camera
      return (
        <View style={styles.localVideoContent}>
          <Camera
            style={styles.camera}
            type={cameraType}
            ratio="16:9"
            onCameraReady={() => {
              console.log('MockRtcSurfaceView: Camera ready');
              setCameraReady(true);
            }}
            onMountError={(error) => {
              console.error('MockRtcSurfaceView: Camera mount error:', error);
              setCameraError('Camera failed to initialize');
              setShowFallback(true);
            }}
          >
            <View style={styles.overlay}>
              <View style={styles.topLeftControls}>
                <Animated.View
                  style={[
                    styles.liveIndicator,
                    { transform: [{ scale: pulseValue }] }
                  ]}
                >
                  <Text style={styles.liveText}>● LIVE</Text>
                </Animated.View>
              </View>
              <Text style={styles.overlayText}>You (Broadcasting)</Text>
            </View>

            {/* Camera controls */}
            <View style={styles.cameraControls}>
              <View style={styles.cameraStatus}>
                <Text style={styles.cameraStatusText}>
                  📹 {cameraReady ? 'Camera Active' : 'Initializing...'}
                </Text>
              </View>

              {cameraReady && (
                <TouchableOpacity style={styles.switchCameraButton} onPress={switchCamera}>
                  <Text style={styles.switchCameraText}>🔄</Text>
                </TouchableOpacity>
              )}
            </View>
          </Camera>
        </View>
      );
    } else {
      return (
        <View style={styles.remoteVideoContent}>
          <Animated.View
            style={[
              styles.animatedBackground,
              { backgroundColor: interpolatedColor }
            ]}
          />
          <View style={styles.videoPattern}>
            {Array.from({ length: 15 }).map((_, i) => (
              <View
                key={i}
                style={[
                  styles.patternLine,
                  {
                    left: i * (width / 16),
                    opacity: 0.2 + (i % 2) * 0.3,
                  }
                ]}
              />
            ))}
          </View>
          <View style={styles.overlay}>
            <Animated.View
              style={[
                styles.liveIndicator,
                { transform: [{ scale: pulseValue }] }
              ]}
            >
              <Text style={styles.liveText}>● LIVE</Text>
            </Animated.View>
            <Text style={styles.overlayText}>
              {uid ? `User ${uid}` : 'Remote Stream'}
            </Text>
          </View>
          <View style={styles.centerContent}>
            <Text style={styles.streamIcon}>📺</Text>
            <Text style={styles.statusText}>Live Stream</Text>
            <Text style={styles.qualityText}>HD • Real-time</Text>
          </View>
        </View>
      );
    }
  };

  return (
    <View style={[styles.container, style]}>
      {renderVideoContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  localVideoContent: {
    flex: 1,
    width: '100%',
    position: 'relative',
  },
  remoteVideoContent: {
    flex: 1,
    width: '100%',
    position: 'relative',
  },
  animatedBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  videoPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  patternDot: {
    position: 'absolute',
    width: 4,
    height: 4,
    backgroundColor: '#fff',
    borderRadius: 2,
  },
  patternLine: {
    position: 'absolute',
    width: 2,
    height: '100%',
    backgroundColor: '#fff',
  },
  overlay: {
    position: 'absolute',
    top: 20,
    left: 20,
    right: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    zIndex: 10,
  },
  topLeftControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  liveIndicator: {
    backgroundColor: 'rgba(255, 0, 0, 0.9)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  liveText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  overlayText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  centerContent: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -50 }, { translateY: -50 }],
    alignItems: 'center',
    zIndex: 5,
  },
  cameraIcon: {
    fontSize: 48,
    marginBottom: 8,
  },
  streamIcon: {
    fontSize: 48,
    marginBottom: 8,
  },
  statusText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
    textAlign: 'center',
  },
  qualityText: {
    color: '#ccc',
    fontSize: 14,
    textAlign: 'center',
  },
  camera: {
    flex: 1,
    width: '100%',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  permissionText: {
    color: '#ff6b6b',
    fontSize: 16,
    textAlign: 'center',
    marginTop: 8,
  },
  cameraControls: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cameraStatus: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  cameraStatusText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  switchCameraButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  switchCameraText: {
    fontSize: 20,
  },
});

export default MockRtcSurfaceView;
