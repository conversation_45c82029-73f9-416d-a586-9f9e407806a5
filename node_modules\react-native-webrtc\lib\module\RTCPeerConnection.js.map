{"version": 3, "names": ["EventTarget", "Event", "defineEventAttribute", "NativeModules", "addListener", "removeListener", "<PERSON><PERSON>", "MediaStream", "MediaStreamTrack", "MediaStreamTrackEvent", "RTCDataChannel", "RTCDataChannelEvent", "RTCIceCandidate", "RTCIceCandidateEvent", "RTCRtpReceiveParameters", "RTCRtpReceiver", "RTCRtpSendParameters", "RTCRtpSender", "RTCRtpTransceiver", "RTCSessionDescription", "RTCTrackEvent", "RTCUtil", "log", "WebRTCModule", "nextPeerConnectionId", "RTCPeerConnection", "constructor", "configuration", "_defineProperty", "_pcId", "_configuration$iceSer", "servers", "iceServers", "server", "urls", "url", "Array", "isArray", "map", "toLowerCase", "filter", "s", "peerConnectionInit", "Error", "_transceivers", "_remoteStreams", "Map", "_pendingTrackEvents", "_registerEvents", "debug", "createOffer", "options", "sdpInfo", "newTransceivers", "transceiversInfo", "peerConnectionCreateOffer", "normalizeOfferOptions", "for<PERSON>ach", "t", "transceiverOrder", "transceiver", "newSender", "sender", "track", "remoteTrack", "receiver", "newReceiver", "newTransceiver", "_insertTransceiverSorted", "_updateTransceivers", "createAnswer", "peerConnectionCreateAnswer", "setConfiguration", "peerConnectionSetConfiguration", "setLocalDescription", "sessionDescription", "_desc", "desc", "_sessionDescription$s", "type", "sdp", "isSdpTypeValid", "peerConnectionSetLocalDescription", "localDescription", "setRemoteDescription", "_sessionDescription$s2", "_desc$type", "Promise", "reject", "peerConnectionSetRemoteDescription", "remoteDescription", "pendingTrackEvents", "ev", "getTransceivers", "id", "_mid", "mid", "_currentDirection", "currentDirection", "_direction", "direction", "streams", "streamInfo", "has", "streamId", "stream", "streamReactTag", "tracks", "set", "get", "_tracks", "includes", "push", "eventData", "dispatchEvent", "_setMutedInternal", "addIceCandidate", "candidate", "sdpMLineIndex", "undefined", "sdpMid", "TypeError", "newSdp", "peerConnectionAddICECandidate", "deepClone", "addTrack", "connectionState", "_trackExists", "_len", "arguments", "length", "_key", "streamIds", "result", "peerConnectionAddTrack", "existingSender", "getSenders", "_track", "existingTransceiver", "addTransceiver", "source", "init", "src", "trackId", "peerConnectionAddTransceiver", "removeTrack", "_peerConnectionId", "find", "peerConnectionRemoveTrack", "getStats", "selector", "data", "peerConnectionGetStats", "JSON", "parse", "senders", "receivers", "getReceivers", "r", "matches", "sr", "e", "stopped", "close", "peerConnectionClose", "_ref", "_setStopped", "restartIce", "peerConnectionRestartIce", "pcId", "iceConnectionState", "peerConnectionDispose", "signalingState", "receiverId", "values", "trackIdx", "indexOf", "splice", "iceGatheringState", "channel", "dataChannel", "muted", "createDataChannel", "label", "dataChannelDict", "channelInfo", "String", "_sender$track", "transceiverUpdates", "removeStopped", "update", "transceiverId", "_stopped", "Boolean", "isStopped", "_sender", "_rtpParameters", "senderRtpParameters", "_receiver", "receiverRtpParameters", "order", "sort", "a", "b", "proto", "prototype"], "sources": ["RTCPeerConnection.ts"], "sourcesContent": ["import { EventTarget, Event, defineEventAttribute } from 'event-target-shim/index';\nimport { NativeModules } from 'react-native';\n\nimport { addListener, removeListener } from './EventEmitter';\nimport Logger from './Logger';\nimport MediaStream from './MediaStream';\nimport MediaStreamTrack from './MediaStreamTrack';\nimport MediaStreamTrackEvent from './MediaStreamTrackEvent';\nimport RTCDataChannel from './RTCDataChannel';\nimport RTCDataChannelEvent from './RTCDataChannelEvent';\nimport RTCIceCandidate from './RTCIceCandidate';\nimport RTCIceCandidateEvent from './RTCIceCandidateEvent';\nimport RTCRtpReceiveParameters from './RTCRtpReceiveParameters';\nimport RTCRtpReceiver from './RTCRtpReceiver';\nimport RTCRtpSendParameters from './RTCRtpSendParameters';\nimport RTCRtpSender from './RTCRtpSender';\nimport RTCRtpTransceiver from './RTCRtpTransceiver';\nimport RTCSessionDescription, { RTCSessionDescriptionInit } from './RTCSessionDescription';\nimport RTCTrackEvent from './RTCTrackEvent';\nimport * as RTCUtil from './RTCUtil';\nimport { RTCOfferOptions } from './RTCUtil';\n\nconst log = new Logger('pc');\nconst { WebRTCModule } = NativeModules;\n\ntype RTCSignalingState =\n    | 'stable'\n    | 'have-local-offer'\n    | 'have-remote-offer'\n    | 'have-local-pranswer'\n    | 'have-remote-pranswer'\n    | 'closed';\n\ntype RTCIceGatheringState = 'new' | 'gathering' | 'complete';\n\ntype RTCPeerConnectionState = 'new' | 'connecting' | 'connected' | 'disconnected' | 'failed' | 'closed';\n\ntype RTCIceConnectionState = 'new' | 'checking' | 'connected' | 'completed' | 'failed' | 'disconnected' | 'closed';\n\ntype RTCDataChannelInit = {\n    ordered?: boolean,\n    maxPacketLifeTime?: number,\n    maxRetransmits?: number,\n    protocol?: string,\n    negotiated?: boolean,\n    id?: number\n};\n\ntype RTCIceServer = {\n    credential?: string,\n    url?: string, // Deprecated.\n    urls?: string | string[],\n    username?: string\n};\n\ntype RTCConfiguration = {\n    bundlePolicy?: 'balanced' | 'max-compat' | 'max-bundle',\n    iceCandidatePoolSize?: number,\n    iceServers?: RTCIceServer[],\n    iceTransportPolicy?: 'all' | 'relay',\n    rtcpMuxPolicy?: 'negotiate' | 'require'\n};\n\ntype RTCPeerConnectionEventMap = {\n    connectionstatechange: Event<'connectionstatechange'>\n    icecandidate: RTCIceCandidateEvent<'icecandidate'>\n    icecandidateerror: RTCIceCandidateEvent<'icecandidateerror'>\n    iceconnectionstatechange: Event<'iceconnectionstatechange'>\n    icegatheringstatechange: Event<'icegatheringstatechange'>\n    negotiationneeded: Event<'negotiationneeded'>\n    signalingstatechange: Event<'signalingstatechange'>\n    datachannel: RTCDataChannelEvent<'datachannel'>\n    track: RTCTrackEvent<'track'>\n    error: Event<'error'>\n}\n\nlet nextPeerConnectionId = 0;\n\nexport default class RTCPeerConnection extends EventTarget<RTCPeerConnectionEventMap> {\n    localDescription: RTCSessionDescription | null = null;\n    remoteDescription: RTCSessionDescription | null = null;\n    signalingState: RTCSignalingState = 'stable';\n    iceGatheringState: RTCIceGatheringState = 'new';\n    connectionState: RTCPeerConnectionState = 'new';\n    iceConnectionState: RTCIceConnectionState = 'new';\n\n    _pcId: number;\n    _transceivers: { order: number, transceiver: RTCRtpTransceiver }[];\n    _remoteStreams: Map<string, MediaStream>;\n    _pendingTrackEvents: any[];\n\n    constructor(configuration?: RTCConfiguration) {\n        super();\n\n        this._pcId = nextPeerConnectionId++;\n\n        // Sanitize ICE servers.\n        if (configuration) {\n            const servers = configuration?.iceServers ?? [];\n\n            for (const server of servers) {\n                let urls = server.url || server.urls;\n\n                delete server.url;\n                delete server.urls;\n\n                if (!urls) {\n                    continue;\n                }\n\n                if (!Array.isArray(urls)) {\n                    urls = [ urls ];\n                }\n\n                // Native WebRTC does case sensitive parsing.\n                server.urls = urls.map(url => url.toLowerCase());\n            }\n\n            // Filter out bogus servers.\n            configuration.iceServers = servers.filter(s => s.urls);\n        }\n\n        if (!WebRTCModule.peerConnectionInit(configuration, this._pcId)) {\n            throw new Error('Failed to initialize PeerConnection, check the native logs!');\n        }\n\n        this._transceivers = [];\n        this._remoteStreams = new Map();\n        this._pendingTrackEvents = [];\n\n        this._registerEvents();\n\n        log.debug(`${this._pcId} ctor`);\n    }\n\n    async createOffer(options?:RTCOfferOptions) {\n        log.debug(`${this._pcId} createOffer`);\n\n        const {\n            sdpInfo,\n            newTransceivers,\n            transceiversInfo\n        } = await WebRTCModule.peerConnectionCreateOffer(this._pcId, RTCUtil.normalizeOfferOptions(options));\n\n        log.debug(`${this._pcId} createOffer OK`);\n\n        newTransceivers?.forEach(t => {\n            const { transceiverOrder, transceiver } = t;\n            const newSender = new RTCRtpSender({ ...transceiver.sender, track: null });\n            const remoteTrack\n                = transceiver.receiver.track ? new MediaStreamTrack(transceiver.receiver.track) : null;\n            const newReceiver = new RTCRtpReceiver({ ...transceiver.receiver, track: remoteTrack });\n            const newTransceiver = new RTCRtpTransceiver({\n                ...transceiver,\n                sender: newSender,\n                receiver: newReceiver,\n            });\n\n            this._insertTransceiverSorted(transceiverOrder, newTransceiver);\n        });\n\n        this._updateTransceivers(transceiversInfo);\n\n        return sdpInfo;\n    }\n\n    async createAnswer() {\n        log.debug(`${this._pcId} createAnswer`);\n\n        const {\n            sdpInfo,\n            transceiversInfo\n        } = await WebRTCModule.peerConnectionCreateAnswer(this._pcId, {});\n\n        this._updateTransceivers(transceiversInfo);\n\n        return sdpInfo;\n    }\n\n    setConfiguration(configuration): void {\n        WebRTCModule.peerConnectionSetConfiguration(configuration, this._pcId);\n    }\n\n    async setLocalDescription(sessionDescription?: RTCSessionDescription | RTCSessionDescriptionInit): Promise<void> {\n        log.debug(`${this._pcId} setLocalDescription`);\n\n        let desc;\n\n        if (sessionDescription) {\n            desc = {\n                type: sessionDescription.type,\n                sdp: sessionDescription.sdp ?? ''\n            };\n\n            if (!RTCUtil.isSdpTypeValid(desc.type)) {\n                throw new Error(`Invalid session description: invalid type: ${desc.type}`);\n            }\n        } else {\n            desc = null;\n        }\n\n        const {\n            sdpInfo,\n            transceiversInfo\n        } = await WebRTCModule.peerConnectionSetLocalDescription(this._pcId, desc);\n\n        if (sdpInfo.type && sdpInfo.sdp) {\n            this.localDescription = new RTCSessionDescription(sdpInfo);\n        } else {\n            this.localDescription = null;\n        }\n\n        this._updateTransceivers(transceiversInfo, /* removeStopped */ desc?.type === 'answer');\n\n        log.debug(`${this._pcId} setLocalDescription OK`);\n    }\n\n    async setRemoteDescription(sessionDescription: RTCSessionDescription | RTCSessionDescriptionInit): Promise<void> {\n        log.debug(`${this._pcId} setRemoteDescription`);\n\n        if (!sessionDescription) {\n            return Promise.reject(new Error('No session description provided'));\n        }\n\n        const desc = {\n            type: sessionDescription.type,\n            sdp: sessionDescription.sdp ?? ''\n        };\n\n        if (!RTCUtil.isSdpTypeValid(desc.type ?? '')) {\n            throw new Error(`Invalid session description: invalid type: ${desc.type}`);\n        }\n\n        const {\n            sdpInfo,\n            newTransceivers,\n            transceiversInfo\n        } = await WebRTCModule.peerConnectionSetRemoteDescription(this._pcId, desc);\n\n        if (sdpInfo.type && sdpInfo.sdp) {\n            this.remoteDescription = new RTCSessionDescription(sdpInfo);\n        } else {\n            this.remoteDescription = null;\n        }\n\n        newTransceivers?.forEach(t => {\n            const { transceiverOrder, transceiver } = t;\n            const newSender = new RTCRtpSender({ ...transceiver.sender, track: null });\n            const remoteTrack\n                = transceiver.receiver.track ? new MediaStreamTrack(transceiver.receiver.track) : null;\n            const newReceiver = new RTCRtpReceiver({ ...transceiver.receiver, track: remoteTrack });\n            const newTransceiver = new RTCRtpTransceiver({\n                ...transceiver,\n                sender: newSender,\n                receiver: newReceiver,\n            });\n\n            this._insertTransceiverSorted(transceiverOrder, newTransceiver);\n        });\n\n        this._updateTransceivers(transceiversInfo, /* removeStopped */ desc.type === 'answer');\n\n        // Fire track events. They must fire before sRD resolves.\n        const pendingTrackEvents = this._pendingTrackEvents;\n\n        this._pendingTrackEvents = [];\n\n        for (const ev of pendingTrackEvents) {\n            const [ transceiver ] = this\n                .getTransceivers()\n                .filter(t => t.receiver.id ===  ev.receiver.id);\n\n            // We need to fire this event for an existing track sometimes, like\n            // when the transceiver direction (on the sending side) switches from\n            // sendrecv to recvonly and then back.\n\n            // @ts-ignore\n            const track: MediaStreamTrack = transceiver.receiver.track;\n\n            transceiver._mid = ev.transceiver.mid;\n            transceiver._currentDirection = ev.transceiver.currentDirection;\n            transceiver._direction = ev.transceiver.direction;\n\n            // Get the stream object from the event. Create if necessary.\n            const streams: MediaStream[] = ev.streams.map(streamInfo => {\n                // Here we are making sure that we don't create stream objects that already exist\n                // So that event listeners do get the same object if it has been created before.\n                if (!this._remoteStreams.has(streamInfo.streamId)) {\n                    const stream = new MediaStream({\n                        streamId: streamInfo.streamId,\n                        streamReactTag: streamInfo.streamReactTag,\n                        tracks: []\n                    });\n\n                    this._remoteStreams.set(streamInfo.streamId, stream);\n                }\n\n                const stream = this._remoteStreams.get(streamInfo.streamId);\n\n                if (!stream?._tracks.includes(track)) {\n                    stream?._tracks.push(track);\n                }\n\n                return stream;\n            });\n\n            const eventData = {\n                streams,\n                transceiver,\n                track,\n                receiver: transceiver.receiver\n            };\n\n\n            this.dispatchEvent(new RTCTrackEvent('track', eventData));\n\n            streams.forEach(stream => {\n                stream.dispatchEvent(new MediaStreamTrackEvent('addtrack', { track }));\n            });\n\n            // Dispatch an unmute event for the track.\n            track._setMutedInternal(false);\n        }\n\n        log.debug(`${this._pcId} setRemoteDescription OK`);\n    }\n\n    async addIceCandidate(candidate): Promise<void> {\n        log.debug(`${this._pcId} addIceCandidate`);\n\n        if (!candidate || !candidate.candidate) {\n            // XXX end-of candidates is not implemented: https://bugs.chromium.org/p/webrtc/issues/detail?id=9218\n            return;\n        }\n\n        if ((candidate.sdpMLineIndex === null ||\n             candidate.sdpMLineIndex === undefined) &&\n            (candidate.sdpMid === null ||\n             candidate.sdpMid === undefined)\n        ) {\n            throw new TypeError('`sdpMLineIndex` and `sdpMid` must not be both null or undefined');\n        }\n\n        const newSdp = await WebRTCModule.peerConnectionAddICECandidate(\n            this._pcId,\n            RTCUtil.deepClone(candidate)\n        );\n\n        this.remoteDescription = new RTCSessionDescription(newSdp);\n    }\n\n    /**\n     * @brief Adds a new track to the {@link RTCPeerConnection},\n     * and indicates that it is contained in the specified {@link MediaStream}s.\n     * This method has to be synchronous as the W3C API expects a track to be returned\n     * @param {MediaStreamTrack} track The track to be added\n     * @param {...MediaStream} streams One or more {@link MediaStream}s the track needs to be added to\n     * https://w3c.github.io/webrtc-pc/#dom-rtcpeerconnection-addtrack\n     */\n    addTrack(track: MediaStreamTrack, ...streams: MediaStream[]): RTCRtpSender {\n        log.debug(`${this._pcId} addTrack`);\n\n        if (this.connectionState === 'closed') {\n            throw new Error('Peer Connection is closed');\n        }\n\n        if (this._trackExists(track)) {\n            throw new Error('Track already exists in a sender');\n        }\n\n        const streamIds = streams.map(s => s.id);\n        const result = WebRTCModule.peerConnectionAddTrack(this._pcId, track.id, { streamIds });\n\n        if (result === null) {\n            throw new Error('Could not add sender');\n        }\n\n        const { transceiverOrder, transceiver, sender } = result;\n\n        // According to the W3C docs, the sender could have been reused, and\n        // so we check if that is the case, and update accordingly.\n        const [ existingSender ] = this\n            .getSenders()\n            .filter(s => s.id === sender.id);\n\n        if (existingSender) {\n            // Update sender\n            existingSender._track = track;\n\n            // Update the corresponding transceiver as well\n            const [ existingTransceiver ] = this\n                .getTransceivers()\n                .filter(t => t.sender.id === existingSender.id);\n\n            existingTransceiver._direction = transceiver.direction;\n            existingTransceiver._currentDirection = transceiver.currentDirection;\n\n            return existingSender;\n        }\n\n        // This is a new transceiver, should create a transceiver for it and add it\n        const newSender = new RTCRtpSender({ ...transceiver.sender, track });\n        const remoteTrack = transceiver.receiver.track ? new MediaStreamTrack(transceiver.receiver.track) : null;\n        const newReceiver = new RTCRtpReceiver({ ...transceiver.receiver, track: remoteTrack });\n        const newTransceiver = new RTCRtpTransceiver({\n            ...transceiver,\n            sender: newSender,\n            receiver: newReceiver,\n        });\n\n        this._insertTransceiverSorted(transceiverOrder, newTransceiver);\n\n        return newSender;\n    }\n\n    addTransceiver(source: 'audio' | 'video' | MediaStreamTrack, init): RTCRtpTransceiver {\n        log.debug(`${this._pcId} addTransceiver`);\n\n        let src = {};\n\n        if (source === 'audio') {\n            src = { type: 'audio' };\n        } else if (source === 'video') {\n            src = { type: 'video' };\n        } else {\n            src = { trackId: source.id };\n        }\n\n        // Extract the stream ids\n        if (init && init.streams) {\n            init.streamIds = init.streams.map(stream => stream.id);\n        }\n\n        const result = WebRTCModule.peerConnectionAddTransceiver(this._pcId, { ...src, init: { ...init } });\n\n        if (result === null) {\n            throw new Error('Transceiver could not be added');\n        }\n\n        const t = result.transceiver;\n        let track: MediaStreamTrack | null = null;\n\n        if (typeof source === 'string') {\n            if (t.sender.track) {\n                track = new MediaStreamTrack(t.sender.track);\n            }\n        } else {\n            // 'source' is a MediaStreamTrack\n            track = source;\n        }\n\n        const sender = new RTCRtpSender({ ...t.sender, track });\n        const remoteTrack = t.receiver.track ? new MediaStreamTrack(t.receiver.track) : null;\n        const receiver = new RTCRtpReceiver({ ...t.receiver, track: remoteTrack });\n        const transceiver = new RTCRtpTransceiver({\n            ...result.transceiver,\n            sender,\n            receiver\n        });\n\n        this._insertTransceiverSorted(result.transceiverOrder, transceiver);\n\n        return transceiver;\n    }\n\n    removeTrack(sender: RTCRtpSender) {\n        log.debug(`${this._pcId} removeTrack`);\n\n        if (this._pcId !== sender._peerConnectionId) {\n            throw new Error('Sender does not belong to this peer connection');\n        }\n\n        if (this.connectionState === 'closed') {\n            throw new Error('Peer Connection is closed');\n        }\n\n        const existingSender = this\n            .getSenders()\n            .find(s => s === sender);\n\n        if (!existingSender) {\n            throw new Error('Sender does not exist');\n        }\n\n        if (existingSender.track === null) {\n            return;\n        }\n\n        // Blocking!\n        WebRTCModule.peerConnectionRemoveTrack(this._pcId, sender.id);\n\n        existingSender._track = null;\n\n        const [ existingTransceiver ] = this\n            .getTransceivers()\n            .filter(t => t.sender.id === existingSender.id);\n\n        existingTransceiver._direction = existingTransceiver.direction === 'sendrecv' ? 'recvonly' : 'inactive';\n    }\n\n    async getStats(selector?: MediaStreamTrack) {\n        log.debug(`${this._pcId} getStats`);\n\n        if (!selector) {\n            const data = await WebRTCModule.peerConnectionGetStats(this._pcId);\n\n            /**\n             * On both Android and iOS it is faster to construct a single\n             * JSON string representing the Map of StatsReports and have it\n             * pass through the React Native bridge rather than the Map of\n             * StatsReports. While the implementations do try to be faster in\n             * general, the stress is on being faster to pass through the React\n             * Native bridge which is a bottleneck that tends to be visible in\n             * the UI when there is congestion involving UI-related passing.\n             */\n            return new Map(JSON.parse(data));\n        } else {\n            const senders = this.getSenders().filter(s => s.track === selector);\n            const receivers = this.getReceivers().filter(r => r.track === selector);\n            const matches = senders.length + receivers.length;\n\n            if (matches === 0) {\n                throw new Error('Invalid selector: could not find matching sender / receiver');\n            } else if (matches > 1) {\n                throw new Error('Invalid selector: multiple matching senders / receivers');\n            } else {\n                const sr = senders[0] || receivers[0];\n\n                return sr.getStats();\n            }\n        }\n    }\n\n    getTransceivers(): RTCRtpTransceiver[] {\n        return this._transceivers.map(e => e.transceiver);\n    }\n\n    getSenders(): RTCRtpSender[] {\n        return this._transceivers.filter(e => !e.transceiver.stopped).map(e => e.transceiver.sender);\n    }\n\n    getReceivers(): RTCRtpReceiver[] {\n        return this._transceivers.filter(e => !e.transceiver.stopped).map(e => e.transceiver.receiver);\n    }\n\n    close(): void {\n        log.debug(`${this._pcId} close`);\n\n        if (this.connectionState === 'closed') {\n            return;\n        }\n\n        WebRTCModule.peerConnectionClose(this._pcId);\n\n        // Mark transceivers as stopped.\n        this._transceivers.forEach(({ transceiver })=> {\n            transceiver._setStopped();\n        });\n    }\n\n    restartIce(): void {\n        WebRTCModule.peerConnectionRestartIce(this._pcId);\n    }\n\n    _registerEvents(): void {\n        addListener(this, 'peerConnectionOnRenegotiationNeeded', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            this.dispatchEvent(new Event('negotiationneeded'));\n        });\n\n        addListener(this, 'peerConnectionIceConnectionChanged', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            this.iceConnectionState = ev.iceConnectionState;\n\n            this.dispatchEvent(new Event('iceconnectionstatechange'));\n        });\n\n        addListener(this, 'peerConnectionStateChanged', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            this.connectionState = ev.connectionState;\n\n            this.dispatchEvent(new Event('connectionstatechange'));\n\n            if (ev.connectionState === 'closed') {\n                // This PeerConnection is done, clean up.\n                removeListener(this);\n\n                WebRTCModule.peerConnectionDispose(this._pcId);\n            }\n        });\n\n        addListener(this, 'peerConnectionSignalingStateChanged', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            this.signalingState = ev.signalingState;\n\n            this.dispatchEvent(new Event('signalingstatechange'));\n        });\n\n        // Consider moving away from this event: https://github.com/WebKit/WebKit/pull/3953\n        addListener(this, 'peerConnectionOnTrack', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            log.debug(`${this._pcId} ontrack`);\n\n            // NOTE: We need to make sure the track event fires right before sRD completes,\n            // so we queue them up here and dispatch the events when sRD fires, but before completing it.\n            // In the future we should probably implement out own logic and drop this event altogether.\n            this._pendingTrackEvents.push(ev);\n        });\n\n        addListener(this, 'peerConnectionOnRemoveTrack', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            log.debug(`${this._pcId} onremovetrack ${ev.receiverId}`);\n\n            const receiver = this.getReceivers().find(r => r.id === ev.receiverId);\n            const track = receiver?.track;\n\n            if (receiver && track) {\n                // As per the spec:\n                // - Remove the track from any media streams that were previously passed to the `track` event.\n                // https://w3c.github.io/webrtc-pc/#dom-rtcpeerconnection-removetrack,\n                // - Mark the track as muted:\n                // https://w3c.github.io/webrtc-pc/#process-remote-track-removal\n                for (const stream of this._remoteStreams.values()) {\n                    if (stream._tracks.includes(track)) {\n                        const trackIdx = stream._tracks.indexOf(track);\n\n                        log.debug(`${this._pcId} removetrack ${track.id}`);\n\n                        stream._tracks.splice(trackIdx, 1);\n\n                        stream.dispatchEvent(new MediaStreamTrackEvent('removetrack', { track }));\n\n                        // Dispatch a mute event for the track.\n                        track._setMutedInternal(true);\n                    }\n                }\n            }\n        });\n\n        addListener(this, 'peerConnectionGotICECandidate', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            const sdpInfo = ev.sdp;\n\n            // Can happen when doing a rollback.\n            if (sdpInfo.type && sdpInfo.sdp) {\n                this.localDescription = new RTCSessionDescription(sdpInfo);\n            } else {\n                this.localDescription = null;\n            }\n\n            const candidate = new RTCIceCandidate(ev.candidate);\n\n            this.dispatchEvent(new RTCIceCandidateEvent('icecandidate', { candidate }));\n        });\n\n        addListener(this, 'peerConnectionIceGatheringChanged', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            this.iceGatheringState = ev.iceGatheringState;\n\n            if (this.iceGatheringState === 'complete') {\n                const sdpInfo = ev.sdp;\n\n                // Can happen when doing a rollback.\n                if (sdpInfo.type && sdpInfo.sdp) {\n                    this.localDescription = new RTCSessionDescription(sdpInfo);\n                } else {\n                    this.localDescription = null;\n                }\n\n                this.dispatchEvent(new RTCIceCandidateEvent('icecandidate', { candidate: null }));\n            }\n\n            this.dispatchEvent(new Event('icegatheringstatechange'));\n        });\n\n        addListener(this, 'peerConnectionDidOpenDataChannel', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            const channel = new RTCDataChannel(ev.dataChannel);\n\n            this.dispatchEvent(new RTCDataChannelEvent('datachannel', { channel }));\n\n            // Send 'open' event. Native doesn't update the state since it's already\n            // set at this point.\n            channel.dispatchEvent(new RTCDataChannelEvent('open', { channel }));\n        });\n\n        addListener(this, 'mediaStreamTrackMuteChanged', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            const [\n                track\n            ] = this.getReceivers().map(r => r.track).filter(t => t?.id === ev.trackId);\n\n            if (track) {\n                track._setMutedInternal(ev.muted);\n            }\n        });\n    }\n\n    /**\n     * Creates a new RTCDataChannel object with the given label. The\n     * RTCDataChannelInit dictionary can be used to configure properties of the\n     * underlying channel such as data reliability.\n     *\n     * @param {string} label - the value with which the label attribute of the new\n     * instance is to be initialized\n     * @param {RTCDataChannelInit} dataChannelDict - an optional dictionary of\n     * values with which to initialize corresponding attributes of the new\n     * instance such as id\n     */\n    createDataChannel(label: string, dataChannelDict?: RTCDataChannelInit): RTCDataChannel {\n        if (arguments.length === 0) {\n            throw new TypeError('1 argument required, but 0 present');\n        }\n\n        if (dataChannelDict && 'id' in dataChannelDict) {\n            const id = dataChannelDict.id;\n\n            if (typeof id !== 'number') {\n                throw new TypeError('DataChannel id must be a number: ' + id);\n            }\n        }\n\n        const channelInfo = WebRTCModule.createDataChannel(this._pcId, String(label), dataChannelDict);\n\n        if (channelInfo === null) {\n            throw new TypeError('Failed to create new DataChannel');\n        }\n\n        return new RTCDataChannel(channelInfo);\n    }\n\n    /**\n     * Check whether a media stream track exists already in a sender.\n     * See https://w3c.github.io/webrtc-pc/#dom-rtcpeerconnection-addtrack for more information\n     */\n    _trackExists(track: MediaStreamTrack): boolean {\n        const [ sender ] = this\n            .getSenders()\n            .filter(\n                sender => sender.track?.id === track.id\n            );\n\n        return sender? true : false;\n    }\n\n    /**\n     * Updates transceivers after offer/answer updates if necessary.\n     */\n    _updateTransceivers(transceiverUpdates, removeStopped = false) {\n        for (const update of transceiverUpdates) {\n            const [ transceiver ] = this\n                .getTransceivers()\n                .filter(t => t.sender.id === update.transceiverId);\n\n            if (!transceiver) {\n                continue;\n            }\n\n            if (update.currentDirection) {\n                transceiver._currentDirection = update.currentDirection;\n            }\n\n            transceiver._mid = update.mid;\n            transceiver._stopped = Boolean(update.isStopped);\n            transceiver._sender._rtpParameters = new RTCRtpSendParameters(update.senderRtpParameters);\n            transceiver._receiver._rtpParameters = new RTCRtpReceiveParameters(update.receiverRtpParameters);\n        }\n\n        if (removeStopped) {\n            const stopped = this.getTransceivers().filter(t => t.stopped);\n            const newTransceivers = this._transceivers.filter(t => !stopped.includes(t.transceiver));\n\n            this._transceivers = newTransceivers;\n        }\n    }\n\n    /**\n     * Inserts transceiver into the transceiver array in the order they are created (timestamp).\n     * @param order an index that refers to when it it was created relatively.\n     * @param transceiver the transceiver object to be inserted.\n     */\n    _insertTransceiverSorted(order: number, transceiver: RTCRtpTransceiver) {\n        this._transceivers.push({ order, transceiver });\n        this._transceivers.sort((a, b) => a.order - b.order);\n    }\n}\n\n/**\n * Define the `onxxx` event handlers.\n */\nconst proto = RTCPeerConnection.prototype;\n\ndefineEventAttribute(proto, 'connectionstatechange');\ndefineEventAttribute(proto, 'icecandidate');\ndefineEventAttribute(proto, 'icecandidateerror');\ndefineEventAttribute(proto, 'iceconnectionstatechange');\ndefineEventAttribute(proto, 'icegatheringstatechange');\ndefineEventAttribute(proto, 'negotiationneeded');\ndefineEventAttribute(proto, 'signalingstatechange');\ndefineEventAttribute(proto, 'datachannel');\ndefineEventAttribute(proto, 'track');\ndefineEventAttribute(proto, 'error');\n"], "mappings": ";AAAA,SAASA,WAAW,EAAEC,KAAK,EAAEC,oBAAoB,QAAQ,yBAAyB;AAClF,SAASC,aAAa,QAAQ,cAAc;AAE5C,SAASC,WAAW,EAAEC,cAAc,QAAQ,gBAAgB;AAC5D,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,uBAAuB,MAAM,2BAA2B;AAC/D,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,qBAAqB,MAAqC,yBAAyB;AAC1F,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAO,KAAKC,OAAO,MAAM,WAAW;AAGpC,MAAMC,GAAG,GAAG,IAAIhB,MAAM,CAAC,IAAI,CAAC;AAC5B,MAAM;EAAEiB;AAAa,CAAC,GAAGpB,aAAa;AAqDtC,IAAIqB,oBAAoB,GAAG,CAAC;AAE5B,eAAe,MAAMC,iBAAiB,SAASzB,WAAW,CAA4B;EAalF0B,WAAWA,CAACC,aAAgC,EAAE;IAC1C,KAAK,CAAC,CAAC;IAACC,eAAA,2BAbqC,IAAI;IAAAA,eAAA,4BACH,IAAI;IAAAA,eAAA,yBAClB,QAAQ;IAAAA,eAAA,4BACF,KAAK;IAAAA,eAAA,0BACL,KAAK;IAAAA,eAAA,6BACH,KAAK;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAU7C,IAAI,CAACC,KAAK,GAAGL,oBAAoB,EAAE;;IAEnC;IACA,IAAIG,aAAa,EAAE;MAAA,IAAAG,qBAAA;MACf,MAAMC,OAAO,IAAAD,qBAAA,GAAGH,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEK,UAAU,cAAAF,qBAAA,cAAAA,qBAAA,GAAI,EAAE;MAE/C,KAAK,MAAMG,MAAM,IAAIF,OAAO,EAAE;QAC1B,IAAIG,IAAI,GAAGD,MAAM,CAACE,GAAG,IAAIF,MAAM,CAACC,IAAI;QAEpC,OAAOD,MAAM,CAACE,GAAG;QACjB,OAAOF,MAAM,CAACC,IAAI;QAElB,IAAI,CAACA,IAAI,EAAE;UACP;QACJ;QAEA,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;UACtBA,IAAI,GAAG,CAAEA,IAAI,CAAE;QACnB;;QAEA;QACAD,MAAM,CAACC,IAAI,GAAGA,IAAI,CAACI,GAAG,CAACH,GAAG,IAAIA,GAAG,CAACI,WAAW,CAAC,CAAC,CAAC;MACpD;;MAEA;MACAZ,aAAa,CAACK,UAAU,GAAGD,OAAO,CAACS,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACP,IAAI,CAAC;IAC1D;IAEA,IAAI,CAACX,YAAY,CAACmB,kBAAkB,CAACf,aAAa,EAAE,IAAI,CAACE,KAAK,CAAC,EAAE;MAC7D,MAAM,IAAIc,KAAK,CAAC,6DAA6D,CAAC;IAClF;IAEA,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAE7B,IAAI,CAACC,eAAe,CAAC,CAAC;IAEtB1B,GAAG,CAAC2B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,OAAM,CAAC;EACnC;EAEA,MAAMqB,WAAWA,CAACC,OAAwB,EAAE;IACxC7B,GAAG,CAAC2B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,cAAa,CAAC;IAEtC,MAAM;MACFuB,OAAO;MACPC,eAAe;MACfC;IACJ,CAAC,GAAG,MAAM/B,YAAY,CAACgC,yBAAyB,CAAC,IAAI,CAAC1B,KAAK,EAAER,OAAO,CAACmC,qBAAqB,CAACL,OAAO,CAAC,CAAC;IAEpG7B,GAAG,CAAC2B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,iBAAgB,CAAC;IAEzCwB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,OAAO,CAACC,CAAC,IAAI;MAC1B,MAAM;QAAEC,gBAAgB;QAAEC;MAAY,CAAC,GAAGF,CAAC;MAC3C,MAAMG,SAAS,GAAG,IAAI5C,YAAY,CAAC;QAAE,GAAG2C,WAAW,CAACE,MAAM;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;MAC1E,MAAMC,WAAW,GACXJ,WAAW,CAACK,QAAQ,CAACF,KAAK,GAAG,IAAIvD,gBAAgB,CAACoD,WAAW,CAACK,QAAQ,CAACF,KAAK,CAAC,GAAG,IAAI;MAC1F,MAAMG,WAAW,GAAG,IAAInD,cAAc,CAAC;QAAE,GAAG6C,WAAW,CAACK,QAAQ;QAAEF,KAAK,EAAEC;MAAY,CAAC,CAAC;MACvF,MAAMG,cAAc,GAAG,IAAIjD,iBAAiB,CAAC;QACzC,GAAG0C,WAAW;QACdE,MAAM,EAAED,SAAS;QACjBI,QAAQ,EAAEC;MACd,CAAC,CAAC;MAEF,IAAI,CAACE,wBAAwB,CAACT,gBAAgB,EAAEQ,cAAc,CAAC;IACnE,CAAC,CAAC;IAEF,IAAI,CAACE,mBAAmB,CAACf,gBAAgB,CAAC;IAE1C,OAAOF,OAAO;EAClB;EAEA,MAAMkB,YAAYA,CAAA,EAAG;IACjBhD,GAAG,CAAC2B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,eAAc,CAAC;IAEvC,MAAM;MACFuB,OAAO;MACPE;IACJ,CAAC,GAAG,MAAM/B,YAAY,CAACgD,0BAA0B,CAAC,IAAI,CAAC1C,KAAK,EAAE,CAAC,CAAC,CAAC;IAEjE,IAAI,CAACwC,mBAAmB,CAACf,gBAAgB,CAAC;IAE1C,OAAOF,OAAO;EAClB;EAEAoB,gBAAgBA,CAAC7C,aAAa,EAAQ;IAClCJ,YAAY,CAACkD,8BAA8B,CAAC9C,aAAa,EAAE,IAAI,CAACE,KAAK,CAAC;EAC1E;EAEA,MAAM6C,mBAAmBA,CAACC,kBAAsE,EAAiB;IAAA,IAAAC,KAAA;IAC7GtD,GAAG,CAAC2B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,sBAAqB,CAAC;IAE9C,IAAIgD,IAAI;IAER,IAAIF,kBAAkB,EAAE;MAAA,IAAAG,qBAAA;MACpBD,IAAI,GAAG;QACHE,IAAI,EAAEJ,kBAAkB,CAACI,IAAI;QAC7BC,GAAG,GAAAF,qBAAA,GAAEH,kBAAkB,CAACK,GAAG,cAAAF,qBAAA,cAAAA,qBAAA,GAAI;MACnC,CAAC;MAED,IAAI,CAACzD,OAAO,CAAC4D,cAAc,CAACJ,IAAI,CAACE,IAAI,CAAC,EAAE;QACpC,MAAM,IAAIpC,KAAK,CAAE,8CAA6CkC,IAAI,CAACE,IAAK,EAAC,CAAC;MAC9E;IACJ,CAAC,MAAM;MACHF,IAAI,GAAG,IAAI;IACf;IAEA,MAAM;MACFzB,OAAO;MACPE;IACJ,CAAC,GAAG,MAAM/B,YAAY,CAAC2D,iCAAiC,CAAC,IAAI,CAACrD,KAAK,EAAEgD,IAAI,CAAC;IAE1E,IAAIzB,OAAO,CAAC2B,IAAI,IAAI3B,OAAO,CAAC4B,GAAG,EAAE;MAC7B,IAAI,CAACG,gBAAgB,GAAG,IAAIhE,qBAAqB,CAACiC,OAAO,CAAC;IAC9D,CAAC,MAAM;MACH,IAAI,CAAC+B,gBAAgB,GAAG,IAAI;IAChC;IAEA,IAAI,CAACd,mBAAmB,CAACf,gBAAgB,EAAE,mBAAoB,EAAAsB,KAAA,GAAAC,IAAI,cAAAD,KAAA,uBAAJA,KAAA,CAAMG,IAAI,MAAK,QAAQ,CAAC;IAEvFzD,GAAG,CAAC2B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,yBAAwB,CAAC;EACrD;EAEA,MAAMuD,oBAAoBA,CAACT,kBAAqE,EAAiB;IAAA,IAAAU,sBAAA,EAAAC,UAAA;IAC7GhE,GAAG,CAAC2B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,uBAAsB,CAAC;IAE/C,IAAI,CAAC8C,kBAAkB,EAAE;MACrB,OAAOY,OAAO,CAACC,MAAM,CAAC,IAAI7C,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE;IAEA,MAAMkC,IAAI,GAAG;MACTE,IAAI,EAAEJ,kBAAkB,CAACI,IAAI;MAC7BC,GAAG,GAAAK,sBAAA,GAAEV,kBAAkB,CAACK,GAAG,cAAAK,sBAAA,cAAAA,sBAAA,GAAI;IACnC,CAAC;IAED,IAAI,CAAChE,OAAO,CAAC4D,cAAc,EAAAK,UAAA,GAACT,IAAI,CAACE,IAAI,cAAAO,UAAA,cAAAA,UAAA,GAAI,EAAE,CAAC,EAAE;MAC1C,MAAM,IAAI3C,KAAK,CAAE,8CAA6CkC,IAAI,CAACE,IAAK,EAAC,CAAC;IAC9E;IAEA,MAAM;MACF3B,OAAO;MACPC,eAAe;MACfC;IACJ,CAAC,GAAG,MAAM/B,YAAY,CAACkE,kCAAkC,CAAC,IAAI,CAAC5D,KAAK,EAAEgD,IAAI,CAAC;IAE3E,IAAIzB,OAAO,CAAC2B,IAAI,IAAI3B,OAAO,CAAC4B,GAAG,EAAE;MAC7B,IAAI,CAACU,iBAAiB,GAAG,IAAIvE,qBAAqB,CAACiC,OAAO,CAAC;IAC/D,CAAC,MAAM;MACH,IAAI,CAACsC,iBAAiB,GAAG,IAAI;IACjC;IAEArC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,OAAO,CAACC,CAAC,IAAI;MAC1B,MAAM;QAAEC,gBAAgB;QAAEC;MAAY,CAAC,GAAGF,CAAC;MAC3C,MAAMG,SAAS,GAAG,IAAI5C,YAAY,CAAC;QAAE,GAAG2C,WAAW,CAACE,MAAM;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;MAC1E,MAAMC,WAAW,GACXJ,WAAW,CAACK,QAAQ,CAACF,KAAK,GAAG,IAAIvD,gBAAgB,CAACoD,WAAW,CAACK,QAAQ,CAACF,KAAK,CAAC,GAAG,IAAI;MAC1F,MAAMG,WAAW,GAAG,IAAInD,cAAc,CAAC;QAAE,GAAG6C,WAAW,CAACK,QAAQ;QAAEF,KAAK,EAAEC;MAAY,CAAC,CAAC;MACvF,MAAMG,cAAc,GAAG,IAAIjD,iBAAiB,CAAC;QACzC,GAAG0C,WAAW;QACdE,MAAM,EAAED,SAAS;QACjBI,QAAQ,EAAEC;MACd,CAAC,CAAC;MAEF,IAAI,CAACE,wBAAwB,CAACT,gBAAgB,EAAEQ,cAAc,CAAC;IACnE,CAAC,CAAC;IAEF,IAAI,CAACE,mBAAmB,CAACf,gBAAgB,EAAE,mBAAoBuB,IAAI,CAACE,IAAI,KAAK,QAAQ,CAAC;;IAEtF;IACA,MAAMY,kBAAkB,GAAG,IAAI,CAAC5C,mBAAmB;IAEnD,IAAI,CAACA,mBAAmB,GAAG,EAAE;IAE7B,KAAK,MAAM6C,EAAE,IAAID,kBAAkB,EAAE;MACjC,MAAM,CAAE/B,WAAW,CAAE,GAAG,IAAI,CACvBiC,eAAe,CAAC,CAAC,CACjBrD,MAAM,CAACkB,CAAC,IAAIA,CAAC,CAACO,QAAQ,CAAC6B,EAAE,KAAMF,EAAE,CAAC3B,QAAQ,CAAC6B,EAAE,CAAC;;MAEnD;MACA;MACA;;MAEA;MACA,MAAM/B,KAAuB,GAAGH,WAAW,CAACK,QAAQ,CAACF,KAAK;MAE1DH,WAAW,CAACmC,IAAI,GAAGH,EAAE,CAAChC,WAAW,CAACoC,GAAG;MACrCpC,WAAW,CAACqC,iBAAiB,GAAGL,EAAE,CAAChC,WAAW,CAACsC,gBAAgB;MAC/DtC,WAAW,CAACuC,UAAU,GAAGP,EAAE,CAAChC,WAAW,CAACwC,SAAS;;MAEjD;MACA,MAAMC,OAAsB,GAAGT,EAAE,CAACS,OAAO,CAAC/D,GAAG,CAACgE,UAAU,IAAI;QACxD;QACA;QACA,IAAI,CAAC,IAAI,CAACzD,cAAc,CAAC0D,GAAG,CAACD,UAAU,CAACE,QAAQ,CAAC,EAAE;UAC/C,MAAMC,MAAM,GAAG,IAAIlG,WAAW,CAAC;YAC3BiG,QAAQ,EAAEF,UAAU,CAACE,QAAQ;YAC7BE,cAAc,EAAEJ,UAAU,CAACI,cAAc;YACzCC,MAAM,EAAE;UACZ,CAAC,CAAC;UAEF,IAAI,CAAC9D,cAAc,CAAC+D,GAAG,CAACN,UAAU,CAACE,QAAQ,EAAEC,MAAM,CAAC;QACxD;QAEA,MAAMA,MAAM,GAAG,IAAI,CAAC5D,cAAc,CAACgE,GAAG,CAACP,UAAU,CAACE,QAAQ,CAAC;QAE3D,IAAI,EAACC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEK,OAAO,CAACC,QAAQ,CAAChD,KAAK,CAAC,GAAE;UAClC0C,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK,OAAO,CAACE,IAAI,CAACjD,KAAK,CAAC;QAC/B;QAEA,OAAO0C,MAAM;MACjB,CAAC,CAAC;MAEF,MAAMQ,SAAS,GAAG;QACdZ,OAAO;QACPzC,WAAW;QACXG,KAAK;QACLE,QAAQ,EAAEL,WAAW,CAACK;MAC1B,CAAC;MAGD,IAAI,CAACiD,aAAa,CAAC,IAAI9F,aAAa,CAAC,OAAO,EAAE6F,SAAS,CAAC,CAAC;MAEzDZ,OAAO,CAAC5C,OAAO,CAACgD,MAAM,IAAI;QACtBA,MAAM,CAACS,aAAa,CAAC,IAAIzG,qBAAqB,CAAC,UAAU,EAAE;UAAEsD;QAAM,CAAC,CAAC,CAAC;MAC1E,CAAC,CAAC;;MAEF;MACAA,KAAK,CAACoD,iBAAiB,CAAC,KAAK,CAAC;IAClC;IAEA7F,GAAG,CAAC2B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,0BAAyB,CAAC;EACtD;EAEA,MAAMuF,eAAeA,CAACC,SAAS,EAAiB;IAC5C/F,GAAG,CAAC2B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,kBAAiB,CAAC;IAE1C,IAAI,CAACwF,SAAS,IAAI,CAACA,SAAS,CAACA,SAAS,EAAE;MACpC;MACA;IACJ;IAEA,IAAI,CAACA,SAAS,CAACC,aAAa,KAAK,IAAI,IAChCD,SAAS,CAACC,aAAa,KAAKC,SAAS,MACrCF,SAAS,CAACG,MAAM,KAAK,IAAI,IACzBH,SAAS,CAACG,MAAM,KAAKD,SAAS,CAAC,EAClC;MACE,MAAM,IAAIE,SAAS,CAAC,iEAAiE,CAAC;IAC1F;IAEA,MAAMC,MAAM,GAAG,MAAMnG,YAAY,CAACoG,6BAA6B,CAC3D,IAAI,CAAC9F,KAAK,EACVR,OAAO,CAACuG,SAAS,CAACP,SAAS,CAC/B,CAAC;IAED,IAAI,CAAC3B,iBAAiB,GAAG,IAAIvE,qBAAqB,CAACuG,MAAM,CAAC;EAC9D;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,QAAQA,CAAC9D,KAAuB,EAA2C;IACvEzC,GAAG,CAAC2B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,WAAU,CAAC;IAEnC,IAAI,IAAI,CAACiG,eAAe,KAAK,QAAQ,EAAE;MACnC,MAAM,IAAInF,KAAK,CAAC,2BAA2B,CAAC;IAChD;IAEA,IAAI,IAAI,CAACoF,YAAY,CAAChE,KAAK,CAAC,EAAE;MAC1B,MAAM,IAAIpB,KAAK,CAAC,kCAAkC,CAAC;IACvD;IAAC,SAAAqF,IAAA,GAAAC,SAAA,CAAAC,MAAA,EATgC7B,OAAO,OAAAjE,KAAA,CAAA4F,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAAP9B,OAAO,CAAA8B,IAAA,QAAAF,SAAA,CAAAE,IAAA;IAAA;IAWxC,MAAMC,SAAS,GAAG/B,OAAO,CAAC/D,GAAG,CAACG,CAAC,IAAIA,CAAC,CAACqD,EAAE,CAAC;IACxC,MAAMuC,MAAM,GAAG9G,YAAY,CAAC+G,sBAAsB,CAAC,IAAI,CAACzG,KAAK,EAAEkC,KAAK,CAAC+B,EAAE,EAAE;MAAEsC;IAAU,CAAC,CAAC;IAEvF,IAAIC,MAAM,KAAK,IAAI,EAAE;MACjB,MAAM,IAAI1F,KAAK,CAAC,sBAAsB,CAAC;IAC3C;IAEA,MAAM;MAAEgB,gBAAgB;MAAEC,WAAW;MAAEE;IAAO,CAAC,GAAGuE,MAAM;;IAExD;IACA;IACA,MAAM,CAAEE,cAAc,CAAE,GAAG,IAAI,CAC1BC,UAAU,CAAC,CAAC,CACZhG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACqD,EAAE,KAAKhC,MAAM,CAACgC,EAAE,CAAC;IAEpC,IAAIyC,cAAc,EAAE;MAChB;MACAA,cAAc,CAACE,MAAM,GAAG1E,KAAK;;MAE7B;MACA,MAAM,CAAE2E,mBAAmB,CAAE,GAAG,IAAI,CAC/B7C,eAAe,CAAC,CAAC,CACjBrD,MAAM,CAACkB,CAAC,IAAIA,CAAC,CAACI,MAAM,CAACgC,EAAE,KAAKyC,cAAc,CAACzC,EAAE,CAAC;MAEnD4C,mBAAmB,CAACvC,UAAU,GAAGvC,WAAW,CAACwC,SAAS;MACtDsC,mBAAmB,CAACzC,iBAAiB,GAAGrC,WAAW,CAACsC,gBAAgB;MAEpE,OAAOqC,cAAc;IACzB;;IAEA;IACA,MAAM1E,SAAS,GAAG,IAAI5C,YAAY,CAAC;MAAE,GAAG2C,WAAW,CAACE,MAAM;MAAEC;IAAM,CAAC,CAAC;IACpE,MAAMC,WAAW,GAAGJ,WAAW,CAACK,QAAQ,CAACF,KAAK,GAAG,IAAIvD,gBAAgB,CAACoD,WAAW,CAACK,QAAQ,CAACF,KAAK,CAAC,GAAG,IAAI;IACxG,MAAMG,WAAW,GAAG,IAAInD,cAAc,CAAC;MAAE,GAAG6C,WAAW,CAACK,QAAQ;MAAEF,KAAK,EAAEC;IAAY,CAAC,CAAC;IACvF,MAAMG,cAAc,GAAG,IAAIjD,iBAAiB,CAAC;MACzC,GAAG0C,WAAW;MACdE,MAAM,EAAED,SAAS;MACjBI,QAAQ,EAAEC;IACd,CAAC,CAAC;IAEF,IAAI,CAACE,wBAAwB,CAACT,gBAAgB,EAAEQ,cAAc,CAAC;IAE/D,OAAON,SAAS;EACpB;EAEA8E,cAAcA,CAACC,MAA4C,EAAEC,IAAI,EAAqB;IAClFvH,GAAG,CAAC2B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,iBAAgB,CAAC;IAEzC,IAAIiH,GAAG,GAAG,CAAC,CAAC;IAEZ,IAAIF,MAAM,KAAK,OAAO,EAAE;MACpBE,GAAG,GAAG;QAAE/D,IAAI,EAAE;MAAQ,CAAC;IAC3B,CAAC,MAAM,IAAI6D,MAAM,KAAK,OAAO,EAAE;MAC3BE,GAAG,GAAG;QAAE/D,IAAI,EAAE;MAAQ,CAAC;IAC3B,CAAC,MAAM;MACH+D,GAAG,GAAG;QAAEC,OAAO,EAAEH,MAAM,CAAC9C;MAAG,CAAC;IAChC;;IAEA;IACA,IAAI+C,IAAI,IAAIA,IAAI,CAACxC,OAAO,EAAE;MACtBwC,IAAI,CAACT,SAAS,GAAGS,IAAI,CAACxC,OAAO,CAAC/D,GAAG,CAACmE,MAAM,IAAIA,MAAM,CAACX,EAAE,CAAC;IAC1D;IAEA,MAAMuC,MAAM,GAAG9G,YAAY,CAACyH,4BAA4B,CAAC,IAAI,CAACnH,KAAK,EAAE;MAAE,GAAGiH,GAAG;MAAED,IAAI,EAAE;QAAE,GAAGA;MAAK;IAAE,CAAC,CAAC;IAEnG,IAAIR,MAAM,KAAK,IAAI,EAAE;MACjB,MAAM,IAAI1F,KAAK,CAAC,gCAAgC,CAAC;IACrD;IAEA,MAAMe,CAAC,GAAG2E,MAAM,CAACzE,WAAW;IAC5B,IAAIG,KAA8B,GAAG,IAAI;IAEzC,IAAI,OAAO6E,MAAM,KAAK,QAAQ,EAAE;MAC5B,IAAIlF,CAAC,CAACI,MAAM,CAACC,KAAK,EAAE;QAChBA,KAAK,GAAG,IAAIvD,gBAAgB,CAACkD,CAAC,CAACI,MAAM,CAACC,KAAK,CAAC;MAChD;IACJ,CAAC,MAAM;MACH;MACAA,KAAK,GAAG6E,MAAM;IAClB;IAEA,MAAM9E,MAAM,GAAG,IAAI7C,YAAY,CAAC;MAAE,GAAGyC,CAAC,CAACI,MAAM;MAAEC;IAAM,CAAC,CAAC;IACvD,MAAMC,WAAW,GAAGN,CAAC,CAACO,QAAQ,CAACF,KAAK,GAAG,IAAIvD,gBAAgB,CAACkD,CAAC,CAACO,QAAQ,CAACF,KAAK,CAAC,GAAG,IAAI;IACpF,MAAME,QAAQ,GAAG,IAAIlD,cAAc,CAAC;MAAE,GAAG2C,CAAC,CAACO,QAAQ;MAAEF,KAAK,EAAEC;IAAY,CAAC,CAAC;IAC1E,MAAMJ,WAAW,GAAG,IAAI1C,iBAAiB,CAAC;MACtC,GAAGmH,MAAM,CAACzE,WAAW;MACrBE,MAAM;MACNG;IACJ,CAAC,CAAC;IAEF,IAAI,CAACG,wBAAwB,CAACiE,MAAM,CAAC1E,gBAAgB,EAAEC,WAAW,CAAC;IAEnE,OAAOA,WAAW;EACtB;EAEAqF,WAAWA,CAACnF,MAAoB,EAAE;IAC9BxC,GAAG,CAAC2B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,cAAa,CAAC;IAEtC,IAAI,IAAI,CAACA,KAAK,KAAKiC,MAAM,CAACoF,iBAAiB,EAAE;MACzC,MAAM,IAAIvG,KAAK,CAAC,gDAAgD,CAAC;IACrE;IAEA,IAAI,IAAI,CAACmF,eAAe,KAAK,QAAQ,EAAE;MACnC,MAAM,IAAInF,KAAK,CAAC,2BAA2B,CAAC;IAChD;IAEA,MAAM4F,cAAc,GAAG,IAAI,CACtBC,UAAU,CAAC,CAAC,CACZW,IAAI,CAAC1G,CAAC,IAAIA,CAAC,KAAKqB,MAAM,CAAC;IAE5B,IAAI,CAACyE,cAAc,EAAE;MACjB,MAAM,IAAI5F,KAAK,CAAC,uBAAuB,CAAC;IAC5C;IAEA,IAAI4F,cAAc,CAACxE,KAAK,KAAK,IAAI,EAAE;MAC/B;IACJ;;IAEA;IACAxC,YAAY,CAAC6H,yBAAyB,CAAC,IAAI,CAACvH,KAAK,EAAEiC,MAAM,CAACgC,EAAE,CAAC;IAE7DyC,cAAc,CAACE,MAAM,GAAG,IAAI;IAE5B,MAAM,CAAEC,mBAAmB,CAAE,GAAG,IAAI,CAC/B7C,eAAe,CAAC,CAAC,CACjBrD,MAAM,CAACkB,CAAC,IAAIA,CAAC,CAACI,MAAM,CAACgC,EAAE,KAAKyC,cAAc,CAACzC,EAAE,CAAC;IAEnD4C,mBAAmB,CAACvC,UAAU,GAAGuC,mBAAmB,CAACtC,SAAS,KAAK,UAAU,GAAG,UAAU,GAAG,UAAU;EAC3G;EAEA,MAAMiD,QAAQA,CAACC,QAA2B,EAAE;IACxChI,GAAG,CAAC2B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,WAAU,CAAC;IAEnC,IAAI,CAACyH,QAAQ,EAAE;MACX,MAAMC,IAAI,GAAG,MAAMhI,YAAY,CAACiI,sBAAsB,CAAC,IAAI,CAAC3H,KAAK,CAAC;;MAElE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,OAAO,IAAIiB,GAAG,CAAC2G,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC,CAAC;IACpC,CAAC,MAAM;MACH,MAAMI,OAAO,GAAG,IAAI,CAACnB,UAAU,CAAC,CAAC,CAAChG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACsB,KAAK,KAAKuF,QAAQ,CAAC;MACnE,MAAMM,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC,CAACrH,MAAM,CAACsH,CAAC,IAAIA,CAAC,CAAC/F,KAAK,KAAKuF,QAAQ,CAAC;MACvE,MAAMS,OAAO,GAAGJ,OAAO,CAACzB,MAAM,GAAG0B,SAAS,CAAC1B,MAAM;MAEjD,IAAI6B,OAAO,KAAK,CAAC,EAAE;QACf,MAAM,IAAIpH,KAAK,CAAC,6DAA6D,CAAC;MAClF,CAAC,MAAM,IAAIoH,OAAO,GAAG,CAAC,EAAE;QACpB,MAAM,IAAIpH,KAAK,CAAC,yDAAyD,CAAC;MAC9E,CAAC,MAAM;QACH,MAAMqH,EAAE,GAAGL,OAAO,CAAC,CAAC,CAAC,IAAIC,SAAS,CAAC,CAAC,CAAC;QAErC,OAAOI,EAAE,CAACX,QAAQ,CAAC,CAAC;MACxB;IACJ;EACJ;EAEAxD,eAAeA,CAAA,EAAwB;IACnC,OAAO,IAAI,CAACjD,aAAa,CAACN,GAAG,CAAC2H,CAAC,IAAIA,CAAC,CAACrG,WAAW,CAAC;EACrD;EAEA4E,UAAUA,CAAA,EAAmB;IACzB,OAAO,IAAI,CAAC5F,aAAa,CAACJ,MAAM,CAACyH,CAAC,IAAI,CAACA,CAAC,CAACrG,WAAW,CAACsG,OAAO,CAAC,CAAC5H,GAAG,CAAC2H,CAAC,IAAIA,CAAC,CAACrG,WAAW,CAACE,MAAM,CAAC;EAChG;EAEA+F,YAAYA,CAAA,EAAqB;IAC7B,OAAO,IAAI,CAACjH,aAAa,CAACJ,MAAM,CAACyH,CAAC,IAAI,CAACA,CAAC,CAACrG,WAAW,CAACsG,OAAO,CAAC,CAAC5H,GAAG,CAAC2H,CAAC,IAAIA,CAAC,CAACrG,WAAW,CAACK,QAAQ,CAAC;EAClG;EAEAkG,KAAKA,CAAA,EAAS;IACV7I,GAAG,CAAC2B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,QAAO,CAAC;IAEhC,IAAI,IAAI,CAACiG,eAAe,KAAK,QAAQ,EAAE;MACnC;IACJ;IAEAvG,YAAY,CAAC6I,mBAAmB,CAAC,IAAI,CAACvI,KAAK,CAAC;;IAE5C;IACA,IAAI,CAACe,aAAa,CAACa,OAAO,CAAC4G,IAAA,IAAoB;MAAA,IAAnB;QAAEzG;MAAY,CAAC,GAAAyG,IAAA;MACvCzG,WAAW,CAAC0G,WAAW,CAAC,CAAC;IAC7B,CAAC,CAAC;EACN;EAEAC,UAAUA,CAAA,EAAS;IACfhJ,YAAY,CAACiJ,wBAAwB,CAAC,IAAI,CAAC3I,KAAK,CAAC;EACrD;EAEAmB,eAAeA,CAAA,EAAS;IACpB5C,WAAW,CAAC,IAAI,EAAE,qCAAqC,EAAGwF,EAAO,IAAK;MAClE,IAAIA,EAAE,CAAC6E,IAAI,KAAK,IAAI,CAAC5I,KAAK,EAAE;QACxB;MACJ;MAEA,IAAI,CAACqF,aAAa,CAAC,IAAIjH,KAAK,CAAC,mBAAmB,CAAC,CAAC;IACtD,CAAC,CAAC;IAEFG,WAAW,CAAC,IAAI,EAAE,oCAAoC,EAAGwF,EAAO,IAAK;MACjE,IAAIA,EAAE,CAAC6E,IAAI,KAAK,IAAI,CAAC5I,KAAK,EAAE;QACxB;MACJ;MAEA,IAAI,CAAC6I,kBAAkB,GAAG9E,EAAE,CAAC8E,kBAAkB;MAE/C,IAAI,CAACxD,aAAa,CAAC,IAAIjH,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC7D,CAAC,CAAC;IAEFG,WAAW,CAAC,IAAI,EAAE,4BAA4B,EAAGwF,EAAO,IAAK;MACzD,IAAIA,EAAE,CAAC6E,IAAI,KAAK,IAAI,CAAC5I,KAAK,EAAE;QACxB;MACJ;MAEA,IAAI,CAACiG,eAAe,GAAGlC,EAAE,CAACkC,eAAe;MAEzC,IAAI,CAACZ,aAAa,CAAC,IAAIjH,KAAK,CAAC,uBAAuB,CAAC,CAAC;MAEtD,IAAI2F,EAAE,CAACkC,eAAe,KAAK,QAAQ,EAAE;QACjC;QACAzH,cAAc,CAAC,IAAI,CAAC;QAEpBkB,YAAY,CAACoJ,qBAAqB,CAAC,IAAI,CAAC9I,KAAK,CAAC;MAClD;IACJ,CAAC,CAAC;IAEFzB,WAAW,CAAC,IAAI,EAAE,qCAAqC,EAAGwF,EAAO,IAAK;MAClE,IAAIA,EAAE,CAAC6E,IAAI,KAAK,IAAI,CAAC5I,KAAK,EAAE;QACxB;MACJ;MAEA,IAAI,CAAC+I,cAAc,GAAGhF,EAAE,CAACgF,cAAc;MAEvC,IAAI,CAAC1D,aAAa,CAAC,IAAIjH,KAAK,CAAC,sBAAsB,CAAC,CAAC;IACzD,CAAC,CAAC;;IAEF;IACAG,WAAW,CAAC,IAAI,EAAE,uBAAuB,EAAGwF,EAAO,IAAK;MACpD,IAAIA,EAAE,CAAC6E,IAAI,KAAK,IAAI,CAAC5I,KAAK,EAAE;QACxB;MACJ;MAEAP,GAAG,CAAC2B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,UAAS,CAAC;;MAElC;MACA;MACA;MACA,IAAI,CAACkB,mBAAmB,CAACiE,IAAI,CAACpB,EAAE,CAAC;IACrC,CAAC,CAAC;IAEFxF,WAAW,CAAC,IAAI,EAAE,6BAA6B,EAAGwF,EAAO,IAAK;MAC1D,IAAIA,EAAE,CAAC6E,IAAI,KAAK,IAAI,CAAC5I,KAAK,EAAE;QACxB;MACJ;MAEAP,GAAG,CAAC2B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,kBAAiB+D,EAAE,CAACiF,UAAW,EAAC,CAAC;MAEzD,MAAM5G,QAAQ,GAAG,IAAI,CAAC4F,YAAY,CAAC,CAAC,CAACV,IAAI,CAACW,CAAC,IAAIA,CAAC,CAAChE,EAAE,KAAKF,EAAE,CAACiF,UAAU,CAAC;MACtE,MAAM9G,KAAK,GAAGE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEF,KAAK;MAE7B,IAAIE,QAAQ,IAAIF,KAAK,EAAE;QACnB;QACA;QACA;QACA;QACA;QACA,KAAK,MAAM0C,MAAM,IAAI,IAAI,CAAC5D,cAAc,CAACiI,MAAM,CAAC,CAAC,EAAE;UAC/C,IAAIrE,MAAM,CAACK,OAAO,CAACC,QAAQ,CAAChD,KAAK,CAAC,EAAE;YAChC,MAAMgH,QAAQ,GAAGtE,MAAM,CAACK,OAAO,CAACkE,OAAO,CAACjH,KAAK,CAAC;YAE9CzC,GAAG,CAAC2B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,gBAAekC,KAAK,CAAC+B,EAAG,EAAC,CAAC;YAElDW,MAAM,CAACK,OAAO,CAACmE,MAAM,CAACF,QAAQ,EAAE,CAAC,CAAC;YAElCtE,MAAM,CAACS,aAAa,CAAC,IAAIzG,qBAAqB,CAAC,aAAa,EAAE;cAAEsD;YAAM,CAAC,CAAC,CAAC;;YAEzE;YACAA,KAAK,CAACoD,iBAAiB,CAAC,IAAI,CAAC;UACjC;QACJ;MACJ;IACJ,CAAC,CAAC;IAEF/G,WAAW,CAAC,IAAI,EAAE,+BAA+B,EAAGwF,EAAO,IAAK;MAC5D,IAAIA,EAAE,CAAC6E,IAAI,KAAK,IAAI,CAAC5I,KAAK,EAAE;QACxB;MACJ;MAEA,MAAMuB,OAAO,GAAGwC,EAAE,CAACZ,GAAG;;MAEtB;MACA,IAAI5B,OAAO,CAAC2B,IAAI,IAAI3B,OAAO,CAAC4B,GAAG,EAAE;QAC7B,IAAI,CAACG,gBAAgB,GAAG,IAAIhE,qBAAqB,CAACiC,OAAO,CAAC;MAC9D,CAAC,MAAM;QACH,IAAI,CAAC+B,gBAAgB,GAAG,IAAI;MAChC;MAEA,MAAMkC,SAAS,GAAG,IAAIzG,eAAe,CAACgF,EAAE,CAACyB,SAAS,CAAC;MAEnD,IAAI,CAACH,aAAa,CAAC,IAAIrG,oBAAoB,CAAC,cAAc,EAAE;QAAEwG;MAAU,CAAC,CAAC,CAAC;IAC/E,CAAC,CAAC;IAEFjH,WAAW,CAAC,IAAI,EAAE,mCAAmC,EAAGwF,EAAO,IAAK;MAChE,IAAIA,EAAE,CAAC6E,IAAI,KAAK,IAAI,CAAC5I,KAAK,EAAE;QACxB;MACJ;MAEA,IAAI,CAACqJ,iBAAiB,GAAGtF,EAAE,CAACsF,iBAAiB;MAE7C,IAAI,IAAI,CAACA,iBAAiB,KAAK,UAAU,EAAE;QACvC,MAAM9H,OAAO,GAAGwC,EAAE,CAACZ,GAAG;;QAEtB;QACA,IAAI5B,OAAO,CAAC2B,IAAI,IAAI3B,OAAO,CAAC4B,GAAG,EAAE;UAC7B,IAAI,CAACG,gBAAgB,GAAG,IAAIhE,qBAAqB,CAACiC,OAAO,CAAC;QAC9D,CAAC,MAAM;UACH,IAAI,CAAC+B,gBAAgB,GAAG,IAAI;QAChC;QAEA,IAAI,CAAC+B,aAAa,CAAC,IAAIrG,oBAAoB,CAAC,cAAc,EAAE;UAAEwG,SAAS,EAAE;QAAK,CAAC,CAAC,CAAC;MACrF;MAEA,IAAI,CAACH,aAAa,CAAC,IAAIjH,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC5D,CAAC,CAAC;IAEFG,WAAW,CAAC,IAAI,EAAE,kCAAkC,EAAGwF,EAAO,IAAK;MAC/D,IAAIA,EAAE,CAAC6E,IAAI,KAAK,IAAI,CAAC5I,KAAK,EAAE;QACxB;MACJ;MAEA,MAAMsJ,OAAO,GAAG,IAAIzK,cAAc,CAACkF,EAAE,CAACwF,WAAW,CAAC;MAElD,IAAI,CAAClE,aAAa,CAAC,IAAIvG,mBAAmB,CAAC,aAAa,EAAE;QAAEwK;MAAQ,CAAC,CAAC,CAAC;;MAEvE;MACA;MACAA,OAAO,CAACjE,aAAa,CAAC,IAAIvG,mBAAmB,CAAC,MAAM,EAAE;QAAEwK;MAAQ,CAAC,CAAC,CAAC;IACvE,CAAC,CAAC;IAEF/K,WAAW,CAAC,IAAI,EAAE,6BAA6B,EAAGwF,EAAO,IAAK;MAC1D,IAAIA,EAAE,CAAC6E,IAAI,KAAK,IAAI,CAAC5I,KAAK,EAAE;QACxB;MACJ;MAEA,MAAM,CACFkC,KAAK,CACR,GAAG,IAAI,CAAC8F,YAAY,CAAC,CAAC,CAACvH,GAAG,CAACwH,CAAC,IAAIA,CAAC,CAAC/F,KAAK,CAAC,CAACvB,MAAM,CAACkB,CAAC,IAAI,CAAAA,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEoC,EAAE,MAAKF,EAAE,CAACmD,OAAO,CAAC;MAE3E,IAAIhF,KAAK,EAAE;QACPA,KAAK,CAACoD,iBAAiB,CAACvB,EAAE,CAACyF,KAAK,CAAC;MACrC;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,iBAAiBA,CAACC,KAAa,EAAEC,eAAoC,EAAkB;IACnF,IAAIvD,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;MACxB,MAAM,IAAIT,SAAS,CAAC,oCAAoC,CAAC;IAC7D;IAEA,IAAI+D,eAAe,IAAI,IAAI,IAAIA,eAAe,EAAE;MAC5C,MAAM1F,EAAE,GAAG0F,eAAe,CAAC1F,EAAE;MAE7B,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;QACxB,MAAM,IAAI2B,SAAS,CAAC,mCAAmC,GAAG3B,EAAE,CAAC;MACjE;IACJ;IAEA,MAAM2F,WAAW,GAAGlK,YAAY,CAAC+J,iBAAiB,CAAC,IAAI,CAACzJ,KAAK,EAAE6J,MAAM,CAACH,KAAK,CAAC,EAAEC,eAAe,CAAC;IAE9F,IAAIC,WAAW,KAAK,IAAI,EAAE;MACtB,MAAM,IAAIhE,SAAS,CAAC,kCAAkC,CAAC;IAC3D;IAEA,OAAO,IAAI/G,cAAc,CAAC+K,WAAW,CAAC;EAC1C;;EAEA;AACJ;AACA;AACA;EACI1D,YAAYA,CAAChE,KAAuB,EAAW;IAC3C,MAAM,CAAED,MAAM,CAAE,GAAG,IAAI,CAClB0E,UAAU,CAAC,CAAC,CACZhG,MAAM,CACHsB,MAAM;MAAA,IAAA6H,aAAA;MAAA,OAAI,EAAAA,aAAA,GAAA7H,MAAM,CAACC,KAAK,cAAA4H,aAAA,uBAAZA,aAAA,CAAc7F,EAAE,MAAK/B,KAAK,CAAC+B,EAAE;IAAA,CAC3C,CAAC;IAEL,OAAOhC,MAAM,GAAE,IAAI,GAAG,KAAK;EAC/B;;EAEA;AACJ;AACA;EACIO,mBAAmBA,CAACuH,kBAAkB,EAAyB;IAAA,IAAvBC,aAAa,GAAA5D,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAV,SAAA,GAAAU,SAAA,MAAG,KAAK;IACzD,KAAK,MAAM6D,MAAM,IAAIF,kBAAkB,EAAE;MACrC,MAAM,CAAEhI,WAAW,CAAE,GAAG,IAAI,CACvBiC,eAAe,CAAC,CAAC,CACjBrD,MAAM,CAACkB,CAAC,IAAIA,CAAC,CAACI,MAAM,CAACgC,EAAE,KAAKgG,MAAM,CAACC,aAAa,CAAC;MAEtD,IAAI,CAACnI,WAAW,EAAE;QACd;MACJ;MAEA,IAAIkI,MAAM,CAAC5F,gBAAgB,EAAE;QACzBtC,WAAW,CAACqC,iBAAiB,GAAG6F,MAAM,CAAC5F,gBAAgB;MAC3D;MAEAtC,WAAW,CAACmC,IAAI,GAAG+F,MAAM,CAAC9F,GAAG;MAC7BpC,WAAW,CAACoI,QAAQ,GAAGC,OAAO,CAACH,MAAM,CAACI,SAAS,CAAC;MAChDtI,WAAW,CAACuI,OAAO,CAACC,cAAc,GAAG,IAAIpL,oBAAoB,CAAC8K,MAAM,CAACO,mBAAmB,CAAC;MACzFzI,WAAW,CAAC0I,SAAS,CAACF,cAAc,GAAG,IAAItL,uBAAuB,CAACgL,MAAM,CAACS,qBAAqB,CAAC;IACpG;IAEA,IAAIV,aAAa,EAAE;MACf,MAAM3B,OAAO,GAAG,IAAI,CAACrE,eAAe,CAAC,CAAC,CAACrD,MAAM,CAACkB,CAAC,IAAIA,CAAC,CAACwG,OAAO,CAAC;MAC7D,MAAM7G,eAAe,GAAG,IAAI,CAACT,aAAa,CAACJ,MAAM,CAACkB,CAAC,IAAI,CAACwG,OAAO,CAACnD,QAAQ,CAACrD,CAAC,CAACE,WAAW,CAAC,CAAC;MAExF,IAAI,CAAChB,aAAa,GAAGS,eAAe;IACxC;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACIe,wBAAwBA,CAACoI,KAAa,EAAE5I,WAA8B,EAAE;IACpE,IAAI,CAAChB,aAAa,CAACoE,IAAI,CAAC;MAAEwF,KAAK;MAAE5I;IAAY,CAAC,CAAC;IAC/C,IAAI,CAAChB,aAAa,CAAC6J,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACF,KAAK,GAAGG,CAAC,CAACH,KAAK,CAAC;EACxD;AACJ;;AAEA;AACA;AACA;AACA,MAAMI,KAAK,GAAGnL,iBAAiB,CAACoL,SAAS;AAEzC3M,oBAAoB,CAAC0M,KAAK,EAAE,uBAAuB,CAAC;AACpD1M,oBAAoB,CAAC0M,KAAK,EAAE,cAAc,CAAC;AAC3C1M,oBAAoB,CAAC0M,KAAK,EAAE,mBAAmB,CAAC;AAChD1M,oBAAoB,CAAC0M,KAAK,EAAE,0BAA0B,CAAC;AACvD1M,oBAAoB,CAAC0M,KAAK,EAAE,yBAAyB,CAAC;AACtD1M,oBAAoB,CAAC0M,KAAK,EAAE,mBAAmB,CAAC;AAChD1M,oBAAoB,CAAC0M,KAAK,EAAE,sBAAsB,CAAC;AACnD1M,oBAAoB,CAAC0M,KAAK,EAAE,aAAa,CAAC;AAC1C1M,oBAAoB,CAAC0M,KAAK,EAAE,OAAO,CAAC;AACpC1M,oBAAoB,CAAC0M,KAAK,EAAE,OAAO,CAAC"}