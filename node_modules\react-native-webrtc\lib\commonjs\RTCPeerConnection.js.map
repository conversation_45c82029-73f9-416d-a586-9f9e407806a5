{"version": 3, "names": ["_index", "require", "_reactNative", "_EventEmitter", "_<PERSON>gger", "_interopRequireDefault", "_MediaStream", "_MediaStreamTrack", "_MediaStreamTrackEvent", "_RTCDataChannel", "_RTCDataChannelEvent", "_RTCIceCandidate", "_RTCIceCandidateEvent", "_RTCRtpReceiveParameters", "_RTCRtpReceiver", "_RTCRtpSendParameters", "_RTCRtpSender", "_RTCRtpTransceiver", "_RTCSessionDescription", "_RTCTrackEvent", "RTCUtil", "_interopRequireWildcard", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "_defineProperty", "value", "enumerable", "configurable", "writable", "log", "<PERSON><PERSON>", "WebRTCModule", "NativeModules", "nextPeerConnectionId", "RTCPeerConnection", "EventTarget", "constructor", "configuration", "_pcId", "_configuration$iceSer", "servers", "iceServers", "server", "urls", "url", "Array", "isArray", "map", "toLowerCase", "filter", "s", "peerConnectionInit", "Error", "_transceivers", "_remoteStreams", "Map", "_pendingTrackEvents", "_registerEvents", "debug", "createOffer", "options", "sdpInfo", "newTransceivers", "transceiversInfo", "peerConnectionCreateOffer", "normalizeOfferOptions", "for<PERSON>ach", "t", "transceiverOrder", "transceiver", "newSender", "RTCRtpSender", "sender", "track", "remoteTrack", "receiver", "MediaStreamTrack", "newReceiver", "RTCRtpReceiver", "newTransceiver", "RTCRtpTransceiver", "_insertTransceiverSorted", "_updateTransceivers", "createAnswer", "peerConnectionCreateAnswer", "setConfiguration", "peerConnectionSetConfiguration", "setLocalDescription", "sessionDescription", "_desc", "_sessionDescription$s", "type", "sdp", "isSdpTypeValid", "peerConnectionSetLocalDescription", "localDescription", "RTCSessionDescription", "setRemoteDescription", "_sessionDescription$s2", "_desc$type", "Promise", "reject", "peerConnectionSetRemoteDescription", "remoteDescription", "pendingTrackEvents", "ev", "getTransceivers", "id", "_mid", "mid", "_currentDirection", "currentDirection", "_direction", "direction", "streams", "streamInfo", "streamId", "stream", "MediaStream", "streamReactTag", "tracks", "_tracks", "includes", "push", "eventData", "dispatchEvent", "RTCTrackEvent", "MediaStreamTrackEvent", "_setMutedInternal", "addIceCandidate", "candidate", "sdpMLineIndex", "undefined", "sdpMid", "TypeError", "newSdp", "peerConnectionAddICECandidate", "deepClone", "addTrack", "connectionState", "_trackExists", "_len", "arguments", "length", "_key", "streamIds", "result", "peerConnectionAddTrack", "existingSender", "getSenders", "_track", "existingTransceiver", "addTransceiver", "source", "init", "src", "trackId", "peerConnectionAddTransceiver", "removeTrack", "_peerConnectionId", "find", "peerConnectionRemoveTrack", "getStats", "selector", "data", "peerConnectionGetStats", "JSON", "parse", "senders", "receivers", "getReceivers", "r", "matches", "sr", "e", "stopped", "close", "peerConnectionClose", "_ref", "_setStopped", "restartIce", "peerConnectionRestartIce", "addListener", "pcId", "Event", "iceConnectionState", "removeListener", "peerConnectionDispose", "signalingState", "receiverId", "values", "trackIdx", "indexOf", "splice", "RTCIceCandidate", "RTCIceCandidateEvent", "iceGatheringState", "channel", "RTCDataChannel", "dataChannel", "RTCDataChannelEvent", "muted", "createDataChannel", "label", "dataChannelDict", "channelInfo", "String", "_sender$track", "transceiverUpdates", "removeStopped", "update", "transceiverId", "_stopped", "Boolean", "isStopped", "_sender", "_rtpParameters", "RTCRtpSendParameters", "senderRtpParameters", "_receiver", "RTCRtpReceiveParameters", "receiverRtpParameters", "order", "sort", "a", "b", "exports", "proto", "defineEventAttribute"], "sources": ["RTCPeerConnection.ts"], "sourcesContent": ["import { EventTarget, Event, defineEventAttribute } from 'event-target-shim/index';\nimport { NativeModules } from 'react-native';\n\nimport { addListener, removeListener } from './EventEmitter';\nimport Logger from './Logger';\nimport MediaStream from './MediaStream';\nimport MediaStreamTrack from './MediaStreamTrack';\nimport MediaStreamTrackEvent from './MediaStreamTrackEvent';\nimport RTCDataChannel from './RTCDataChannel';\nimport RTCDataChannelEvent from './RTCDataChannelEvent';\nimport RTCIceCandidate from './RTCIceCandidate';\nimport RTCIceCandidateEvent from './RTCIceCandidateEvent';\nimport RTCRtpReceiveParameters from './RTCRtpReceiveParameters';\nimport RTCRtpReceiver from './RTCRtpReceiver';\nimport RTCRtpSendParameters from './RTCRtpSendParameters';\nimport RTCRtpSender from './RTCRtpSender';\nimport RTCRtpTransceiver from './RTCRtpTransceiver';\nimport RTCSessionDescription, { RTCSessionDescriptionInit } from './RTCSessionDescription';\nimport RTCTrackEvent from './RTCTrackEvent';\nimport * as RTCUtil from './RTCUtil';\nimport { RTCOfferOptions } from './RTCUtil';\n\nconst log = new Logger('pc');\nconst { WebRTCModule } = NativeModules;\n\ntype RTCSignalingState =\n    | 'stable'\n    | 'have-local-offer'\n    | 'have-remote-offer'\n    | 'have-local-pranswer'\n    | 'have-remote-pranswer'\n    | 'closed';\n\ntype RTCIceGatheringState = 'new' | 'gathering' | 'complete';\n\ntype RTCPeerConnectionState = 'new' | 'connecting' | 'connected' | 'disconnected' | 'failed' | 'closed';\n\ntype RTCIceConnectionState = 'new' | 'checking' | 'connected' | 'completed' | 'failed' | 'disconnected' | 'closed';\n\ntype RTCDataChannelInit = {\n    ordered?: boolean,\n    maxPacketLifeTime?: number,\n    maxRetransmits?: number,\n    protocol?: string,\n    negotiated?: boolean,\n    id?: number\n};\n\ntype RTCIceServer = {\n    credential?: string,\n    url?: string, // Deprecated.\n    urls?: string | string[],\n    username?: string\n};\n\ntype RTCConfiguration = {\n    bundlePolicy?: 'balanced' | 'max-compat' | 'max-bundle',\n    iceCandidatePoolSize?: number,\n    iceServers?: RTCIceServer[],\n    iceTransportPolicy?: 'all' | 'relay',\n    rtcpMuxPolicy?: 'negotiate' | 'require'\n};\n\ntype RTCPeerConnectionEventMap = {\n    connectionstatechange: Event<'connectionstatechange'>\n    icecandidate: RTCIceCandidateEvent<'icecandidate'>\n    icecandidateerror: RTCIceCandidateEvent<'icecandidateerror'>\n    iceconnectionstatechange: Event<'iceconnectionstatechange'>\n    icegatheringstatechange: Event<'icegatheringstatechange'>\n    negotiationneeded: Event<'negotiationneeded'>\n    signalingstatechange: Event<'signalingstatechange'>\n    datachannel: RTCDataChannelEvent<'datachannel'>\n    track: RTCTrackEvent<'track'>\n    error: Event<'error'>\n}\n\nlet nextPeerConnectionId = 0;\n\nexport default class RTCPeerConnection extends EventTarget<RTCPeerConnectionEventMap> {\n    localDescription: RTCSessionDescription | null = null;\n    remoteDescription: RTCSessionDescription | null = null;\n    signalingState: RTCSignalingState = 'stable';\n    iceGatheringState: RTCIceGatheringState = 'new';\n    connectionState: RTCPeerConnectionState = 'new';\n    iceConnectionState: RTCIceConnectionState = 'new';\n\n    _pcId: number;\n    _transceivers: { order: number, transceiver: RTCRtpTransceiver }[];\n    _remoteStreams: Map<string, MediaStream>;\n    _pendingTrackEvents: any[];\n\n    constructor(configuration?: RTCConfiguration) {\n        super();\n\n        this._pcId = nextPeerConnectionId++;\n\n        // Sanitize ICE servers.\n        if (configuration) {\n            const servers = configuration?.iceServers ?? [];\n\n            for (const server of servers) {\n                let urls = server.url || server.urls;\n\n                delete server.url;\n                delete server.urls;\n\n                if (!urls) {\n                    continue;\n                }\n\n                if (!Array.isArray(urls)) {\n                    urls = [ urls ];\n                }\n\n                // Native WebRTC does case sensitive parsing.\n                server.urls = urls.map(url => url.toLowerCase());\n            }\n\n            // Filter out bogus servers.\n            configuration.iceServers = servers.filter(s => s.urls);\n        }\n\n        if (!WebRTCModule.peerConnectionInit(configuration, this._pcId)) {\n            throw new Error('Failed to initialize PeerConnection, check the native logs!');\n        }\n\n        this._transceivers = [];\n        this._remoteStreams = new Map();\n        this._pendingTrackEvents = [];\n\n        this._registerEvents();\n\n        log.debug(`${this._pcId} ctor`);\n    }\n\n    async createOffer(options?:RTCOfferOptions) {\n        log.debug(`${this._pcId} createOffer`);\n\n        const {\n            sdpInfo,\n            newTransceivers,\n            transceiversInfo\n        } = await WebRTCModule.peerConnectionCreateOffer(this._pcId, RTCUtil.normalizeOfferOptions(options));\n\n        log.debug(`${this._pcId} createOffer OK`);\n\n        newTransceivers?.forEach(t => {\n            const { transceiverOrder, transceiver } = t;\n            const newSender = new RTCRtpSender({ ...transceiver.sender, track: null });\n            const remoteTrack\n                = transceiver.receiver.track ? new MediaStreamTrack(transceiver.receiver.track) : null;\n            const newReceiver = new RTCRtpReceiver({ ...transceiver.receiver, track: remoteTrack });\n            const newTransceiver = new RTCRtpTransceiver({\n                ...transceiver,\n                sender: newSender,\n                receiver: newReceiver,\n            });\n\n            this._insertTransceiverSorted(transceiverOrder, newTransceiver);\n        });\n\n        this._updateTransceivers(transceiversInfo);\n\n        return sdpInfo;\n    }\n\n    async createAnswer() {\n        log.debug(`${this._pcId} createAnswer`);\n\n        const {\n            sdpInfo,\n            transceiversInfo\n        } = await WebRTCModule.peerConnectionCreateAnswer(this._pcId, {});\n\n        this._updateTransceivers(transceiversInfo);\n\n        return sdpInfo;\n    }\n\n    setConfiguration(configuration): void {\n        WebRTCModule.peerConnectionSetConfiguration(configuration, this._pcId);\n    }\n\n    async setLocalDescription(sessionDescription?: RTCSessionDescription | RTCSessionDescriptionInit): Promise<void> {\n        log.debug(`${this._pcId} setLocalDescription`);\n\n        let desc;\n\n        if (sessionDescription) {\n            desc = {\n                type: sessionDescription.type,\n                sdp: sessionDescription.sdp ?? ''\n            };\n\n            if (!RTCUtil.isSdpTypeValid(desc.type)) {\n                throw new Error(`Invalid session description: invalid type: ${desc.type}`);\n            }\n        } else {\n            desc = null;\n        }\n\n        const {\n            sdpInfo,\n            transceiversInfo\n        } = await WebRTCModule.peerConnectionSetLocalDescription(this._pcId, desc);\n\n        if (sdpInfo.type && sdpInfo.sdp) {\n            this.localDescription = new RTCSessionDescription(sdpInfo);\n        } else {\n            this.localDescription = null;\n        }\n\n        this._updateTransceivers(transceiversInfo, /* removeStopped */ desc?.type === 'answer');\n\n        log.debug(`${this._pcId} setLocalDescription OK`);\n    }\n\n    async setRemoteDescription(sessionDescription: RTCSessionDescription | RTCSessionDescriptionInit): Promise<void> {\n        log.debug(`${this._pcId} setRemoteDescription`);\n\n        if (!sessionDescription) {\n            return Promise.reject(new Error('No session description provided'));\n        }\n\n        const desc = {\n            type: sessionDescription.type,\n            sdp: sessionDescription.sdp ?? ''\n        };\n\n        if (!RTCUtil.isSdpTypeValid(desc.type ?? '')) {\n            throw new Error(`Invalid session description: invalid type: ${desc.type}`);\n        }\n\n        const {\n            sdpInfo,\n            newTransceivers,\n            transceiversInfo\n        } = await WebRTCModule.peerConnectionSetRemoteDescription(this._pcId, desc);\n\n        if (sdpInfo.type && sdpInfo.sdp) {\n            this.remoteDescription = new RTCSessionDescription(sdpInfo);\n        } else {\n            this.remoteDescription = null;\n        }\n\n        newTransceivers?.forEach(t => {\n            const { transceiverOrder, transceiver } = t;\n            const newSender = new RTCRtpSender({ ...transceiver.sender, track: null });\n            const remoteTrack\n                = transceiver.receiver.track ? new MediaStreamTrack(transceiver.receiver.track) : null;\n            const newReceiver = new RTCRtpReceiver({ ...transceiver.receiver, track: remoteTrack });\n            const newTransceiver = new RTCRtpTransceiver({\n                ...transceiver,\n                sender: newSender,\n                receiver: newReceiver,\n            });\n\n            this._insertTransceiverSorted(transceiverOrder, newTransceiver);\n        });\n\n        this._updateTransceivers(transceiversInfo, /* removeStopped */ desc.type === 'answer');\n\n        // Fire track events. They must fire before sRD resolves.\n        const pendingTrackEvents = this._pendingTrackEvents;\n\n        this._pendingTrackEvents = [];\n\n        for (const ev of pendingTrackEvents) {\n            const [ transceiver ] = this\n                .getTransceivers()\n                .filter(t => t.receiver.id ===  ev.receiver.id);\n\n            // We need to fire this event for an existing track sometimes, like\n            // when the transceiver direction (on the sending side) switches from\n            // sendrecv to recvonly and then back.\n\n            // @ts-ignore\n            const track: MediaStreamTrack = transceiver.receiver.track;\n\n            transceiver._mid = ev.transceiver.mid;\n            transceiver._currentDirection = ev.transceiver.currentDirection;\n            transceiver._direction = ev.transceiver.direction;\n\n            // Get the stream object from the event. Create if necessary.\n            const streams: MediaStream[] = ev.streams.map(streamInfo => {\n                // Here we are making sure that we don't create stream objects that already exist\n                // So that event listeners do get the same object if it has been created before.\n                if (!this._remoteStreams.has(streamInfo.streamId)) {\n                    const stream = new MediaStream({\n                        streamId: streamInfo.streamId,\n                        streamReactTag: streamInfo.streamReactTag,\n                        tracks: []\n                    });\n\n                    this._remoteStreams.set(streamInfo.streamId, stream);\n                }\n\n                const stream = this._remoteStreams.get(streamInfo.streamId);\n\n                if (!stream?._tracks.includes(track)) {\n                    stream?._tracks.push(track);\n                }\n\n                return stream;\n            });\n\n            const eventData = {\n                streams,\n                transceiver,\n                track,\n                receiver: transceiver.receiver\n            };\n\n\n            this.dispatchEvent(new RTCTrackEvent('track', eventData));\n\n            streams.forEach(stream => {\n                stream.dispatchEvent(new MediaStreamTrackEvent('addtrack', { track }));\n            });\n\n            // Dispatch an unmute event for the track.\n            track._setMutedInternal(false);\n        }\n\n        log.debug(`${this._pcId} setRemoteDescription OK`);\n    }\n\n    async addIceCandidate(candidate): Promise<void> {\n        log.debug(`${this._pcId} addIceCandidate`);\n\n        if (!candidate || !candidate.candidate) {\n            // XXX end-of candidates is not implemented: https://bugs.chromium.org/p/webrtc/issues/detail?id=9218\n            return;\n        }\n\n        if ((candidate.sdpMLineIndex === null ||\n             candidate.sdpMLineIndex === undefined) &&\n            (candidate.sdpMid === null ||\n             candidate.sdpMid === undefined)\n        ) {\n            throw new TypeError('`sdpMLineIndex` and `sdpMid` must not be both null or undefined');\n        }\n\n        const newSdp = await WebRTCModule.peerConnectionAddICECandidate(\n            this._pcId,\n            RTCUtil.deepClone(candidate)\n        );\n\n        this.remoteDescription = new RTCSessionDescription(newSdp);\n    }\n\n    /**\n     * @brief Adds a new track to the {@link RTCPeerConnection},\n     * and indicates that it is contained in the specified {@link MediaStream}s.\n     * This method has to be synchronous as the W3C API expects a track to be returned\n     * @param {MediaStreamTrack} track The track to be added\n     * @param {...MediaStream} streams One or more {@link MediaStream}s the track needs to be added to\n     * https://w3c.github.io/webrtc-pc/#dom-rtcpeerconnection-addtrack\n     */\n    addTrack(track: MediaStreamTrack, ...streams: MediaStream[]): RTCRtpSender {\n        log.debug(`${this._pcId} addTrack`);\n\n        if (this.connectionState === 'closed') {\n            throw new Error('Peer Connection is closed');\n        }\n\n        if (this._trackExists(track)) {\n            throw new Error('Track already exists in a sender');\n        }\n\n        const streamIds = streams.map(s => s.id);\n        const result = WebRTCModule.peerConnectionAddTrack(this._pcId, track.id, { streamIds });\n\n        if (result === null) {\n            throw new Error('Could not add sender');\n        }\n\n        const { transceiverOrder, transceiver, sender } = result;\n\n        // According to the W3C docs, the sender could have been reused, and\n        // so we check if that is the case, and update accordingly.\n        const [ existingSender ] = this\n            .getSenders()\n            .filter(s => s.id === sender.id);\n\n        if (existingSender) {\n            // Update sender\n            existingSender._track = track;\n\n            // Update the corresponding transceiver as well\n            const [ existingTransceiver ] = this\n                .getTransceivers()\n                .filter(t => t.sender.id === existingSender.id);\n\n            existingTransceiver._direction = transceiver.direction;\n            existingTransceiver._currentDirection = transceiver.currentDirection;\n\n            return existingSender;\n        }\n\n        // This is a new transceiver, should create a transceiver for it and add it\n        const newSender = new RTCRtpSender({ ...transceiver.sender, track });\n        const remoteTrack = transceiver.receiver.track ? new MediaStreamTrack(transceiver.receiver.track) : null;\n        const newReceiver = new RTCRtpReceiver({ ...transceiver.receiver, track: remoteTrack });\n        const newTransceiver = new RTCRtpTransceiver({\n            ...transceiver,\n            sender: newSender,\n            receiver: newReceiver,\n        });\n\n        this._insertTransceiverSorted(transceiverOrder, newTransceiver);\n\n        return newSender;\n    }\n\n    addTransceiver(source: 'audio' | 'video' | MediaStreamTrack, init): RTCRtpTransceiver {\n        log.debug(`${this._pcId} addTransceiver`);\n\n        let src = {};\n\n        if (source === 'audio') {\n            src = { type: 'audio' };\n        } else if (source === 'video') {\n            src = { type: 'video' };\n        } else {\n            src = { trackId: source.id };\n        }\n\n        // Extract the stream ids\n        if (init && init.streams) {\n            init.streamIds = init.streams.map(stream => stream.id);\n        }\n\n        const result = WebRTCModule.peerConnectionAddTransceiver(this._pcId, { ...src, init: { ...init } });\n\n        if (result === null) {\n            throw new Error('Transceiver could not be added');\n        }\n\n        const t = result.transceiver;\n        let track: MediaStreamTrack | null = null;\n\n        if (typeof source === 'string') {\n            if (t.sender.track) {\n                track = new MediaStreamTrack(t.sender.track);\n            }\n        } else {\n            // 'source' is a MediaStreamTrack\n            track = source;\n        }\n\n        const sender = new RTCRtpSender({ ...t.sender, track });\n        const remoteTrack = t.receiver.track ? new MediaStreamTrack(t.receiver.track) : null;\n        const receiver = new RTCRtpReceiver({ ...t.receiver, track: remoteTrack });\n        const transceiver = new RTCRtpTransceiver({\n            ...result.transceiver,\n            sender,\n            receiver\n        });\n\n        this._insertTransceiverSorted(result.transceiverOrder, transceiver);\n\n        return transceiver;\n    }\n\n    removeTrack(sender: RTCRtpSender) {\n        log.debug(`${this._pcId} removeTrack`);\n\n        if (this._pcId !== sender._peerConnectionId) {\n            throw new Error('Sender does not belong to this peer connection');\n        }\n\n        if (this.connectionState === 'closed') {\n            throw new Error('Peer Connection is closed');\n        }\n\n        const existingSender = this\n            .getSenders()\n            .find(s => s === sender);\n\n        if (!existingSender) {\n            throw new Error('Sender does not exist');\n        }\n\n        if (existingSender.track === null) {\n            return;\n        }\n\n        // Blocking!\n        WebRTCModule.peerConnectionRemoveTrack(this._pcId, sender.id);\n\n        existingSender._track = null;\n\n        const [ existingTransceiver ] = this\n            .getTransceivers()\n            .filter(t => t.sender.id === existingSender.id);\n\n        existingTransceiver._direction = existingTransceiver.direction === 'sendrecv' ? 'recvonly' : 'inactive';\n    }\n\n    async getStats(selector?: MediaStreamTrack) {\n        log.debug(`${this._pcId} getStats`);\n\n        if (!selector) {\n            const data = await WebRTCModule.peerConnectionGetStats(this._pcId);\n\n            /**\n             * On both Android and iOS it is faster to construct a single\n             * JSON string representing the Map of StatsReports and have it\n             * pass through the React Native bridge rather than the Map of\n             * StatsReports. While the implementations do try to be faster in\n             * general, the stress is on being faster to pass through the React\n             * Native bridge which is a bottleneck that tends to be visible in\n             * the UI when there is congestion involving UI-related passing.\n             */\n            return new Map(JSON.parse(data));\n        } else {\n            const senders = this.getSenders().filter(s => s.track === selector);\n            const receivers = this.getReceivers().filter(r => r.track === selector);\n            const matches = senders.length + receivers.length;\n\n            if (matches === 0) {\n                throw new Error('Invalid selector: could not find matching sender / receiver');\n            } else if (matches > 1) {\n                throw new Error('Invalid selector: multiple matching senders / receivers');\n            } else {\n                const sr = senders[0] || receivers[0];\n\n                return sr.getStats();\n            }\n        }\n    }\n\n    getTransceivers(): RTCRtpTransceiver[] {\n        return this._transceivers.map(e => e.transceiver);\n    }\n\n    getSenders(): RTCRtpSender[] {\n        return this._transceivers.filter(e => !e.transceiver.stopped).map(e => e.transceiver.sender);\n    }\n\n    getReceivers(): RTCRtpReceiver[] {\n        return this._transceivers.filter(e => !e.transceiver.stopped).map(e => e.transceiver.receiver);\n    }\n\n    close(): void {\n        log.debug(`${this._pcId} close`);\n\n        if (this.connectionState === 'closed') {\n            return;\n        }\n\n        WebRTCModule.peerConnectionClose(this._pcId);\n\n        // Mark transceivers as stopped.\n        this._transceivers.forEach(({ transceiver })=> {\n            transceiver._setStopped();\n        });\n    }\n\n    restartIce(): void {\n        WebRTCModule.peerConnectionRestartIce(this._pcId);\n    }\n\n    _registerEvents(): void {\n        addListener(this, 'peerConnectionOnRenegotiationNeeded', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            this.dispatchEvent(new Event('negotiationneeded'));\n        });\n\n        addListener(this, 'peerConnectionIceConnectionChanged', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            this.iceConnectionState = ev.iceConnectionState;\n\n            this.dispatchEvent(new Event('iceconnectionstatechange'));\n        });\n\n        addListener(this, 'peerConnectionStateChanged', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            this.connectionState = ev.connectionState;\n\n            this.dispatchEvent(new Event('connectionstatechange'));\n\n            if (ev.connectionState === 'closed') {\n                // This PeerConnection is done, clean up.\n                removeListener(this);\n\n                WebRTCModule.peerConnectionDispose(this._pcId);\n            }\n        });\n\n        addListener(this, 'peerConnectionSignalingStateChanged', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            this.signalingState = ev.signalingState;\n\n            this.dispatchEvent(new Event('signalingstatechange'));\n        });\n\n        // Consider moving away from this event: https://github.com/WebKit/WebKit/pull/3953\n        addListener(this, 'peerConnectionOnTrack', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            log.debug(`${this._pcId} ontrack`);\n\n            // NOTE: We need to make sure the track event fires right before sRD completes,\n            // so we queue them up here and dispatch the events when sRD fires, but before completing it.\n            // In the future we should probably implement out own logic and drop this event altogether.\n            this._pendingTrackEvents.push(ev);\n        });\n\n        addListener(this, 'peerConnectionOnRemoveTrack', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            log.debug(`${this._pcId} onremovetrack ${ev.receiverId}`);\n\n            const receiver = this.getReceivers().find(r => r.id === ev.receiverId);\n            const track = receiver?.track;\n\n            if (receiver && track) {\n                // As per the spec:\n                // - Remove the track from any media streams that were previously passed to the `track` event.\n                // https://w3c.github.io/webrtc-pc/#dom-rtcpeerconnection-removetrack,\n                // - Mark the track as muted:\n                // https://w3c.github.io/webrtc-pc/#process-remote-track-removal\n                for (const stream of this._remoteStreams.values()) {\n                    if (stream._tracks.includes(track)) {\n                        const trackIdx = stream._tracks.indexOf(track);\n\n                        log.debug(`${this._pcId} removetrack ${track.id}`);\n\n                        stream._tracks.splice(trackIdx, 1);\n\n                        stream.dispatchEvent(new MediaStreamTrackEvent('removetrack', { track }));\n\n                        // Dispatch a mute event for the track.\n                        track._setMutedInternal(true);\n                    }\n                }\n            }\n        });\n\n        addListener(this, 'peerConnectionGotICECandidate', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            const sdpInfo = ev.sdp;\n\n            // Can happen when doing a rollback.\n            if (sdpInfo.type && sdpInfo.sdp) {\n                this.localDescription = new RTCSessionDescription(sdpInfo);\n            } else {\n                this.localDescription = null;\n            }\n\n            const candidate = new RTCIceCandidate(ev.candidate);\n\n            this.dispatchEvent(new RTCIceCandidateEvent('icecandidate', { candidate }));\n        });\n\n        addListener(this, 'peerConnectionIceGatheringChanged', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            this.iceGatheringState = ev.iceGatheringState;\n\n            if (this.iceGatheringState === 'complete') {\n                const sdpInfo = ev.sdp;\n\n                // Can happen when doing a rollback.\n                if (sdpInfo.type && sdpInfo.sdp) {\n                    this.localDescription = new RTCSessionDescription(sdpInfo);\n                } else {\n                    this.localDescription = null;\n                }\n\n                this.dispatchEvent(new RTCIceCandidateEvent('icecandidate', { candidate: null }));\n            }\n\n            this.dispatchEvent(new Event('icegatheringstatechange'));\n        });\n\n        addListener(this, 'peerConnectionDidOpenDataChannel', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            const channel = new RTCDataChannel(ev.dataChannel);\n\n            this.dispatchEvent(new RTCDataChannelEvent('datachannel', { channel }));\n\n            // Send 'open' event. Native doesn't update the state since it's already\n            // set at this point.\n            channel.dispatchEvent(new RTCDataChannelEvent('open', { channel }));\n        });\n\n        addListener(this, 'mediaStreamTrackMuteChanged', (ev: any) => {\n            if (ev.pcId !== this._pcId) {\n                return;\n            }\n\n            const [\n                track\n            ] = this.getReceivers().map(r => r.track).filter(t => t?.id === ev.trackId);\n\n            if (track) {\n                track._setMutedInternal(ev.muted);\n            }\n        });\n    }\n\n    /**\n     * Creates a new RTCDataChannel object with the given label. The\n     * RTCDataChannelInit dictionary can be used to configure properties of the\n     * underlying channel such as data reliability.\n     *\n     * @param {string} label - the value with which the label attribute of the new\n     * instance is to be initialized\n     * @param {RTCDataChannelInit} dataChannelDict - an optional dictionary of\n     * values with which to initialize corresponding attributes of the new\n     * instance such as id\n     */\n    createDataChannel(label: string, dataChannelDict?: RTCDataChannelInit): RTCDataChannel {\n        if (arguments.length === 0) {\n            throw new TypeError('1 argument required, but 0 present');\n        }\n\n        if (dataChannelDict && 'id' in dataChannelDict) {\n            const id = dataChannelDict.id;\n\n            if (typeof id !== 'number') {\n                throw new TypeError('DataChannel id must be a number: ' + id);\n            }\n        }\n\n        const channelInfo = WebRTCModule.createDataChannel(this._pcId, String(label), dataChannelDict);\n\n        if (channelInfo === null) {\n            throw new TypeError('Failed to create new DataChannel');\n        }\n\n        return new RTCDataChannel(channelInfo);\n    }\n\n    /**\n     * Check whether a media stream track exists already in a sender.\n     * See https://w3c.github.io/webrtc-pc/#dom-rtcpeerconnection-addtrack for more information\n     */\n    _trackExists(track: MediaStreamTrack): boolean {\n        const [ sender ] = this\n            .getSenders()\n            .filter(\n                sender => sender.track?.id === track.id\n            );\n\n        return sender? true : false;\n    }\n\n    /**\n     * Updates transceivers after offer/answer updates if necessary.\n     */\n    _updateTransceivers(transceiverUpdates, removeStopped = false) {\n        for (const update of transceiverUpdates) {\n            const [ transceiver ] = this\n                .getTransceivers()\n                .filter(t => t.sender.id === update.transceiverId);\n\n            if (!transceiver) {\n                continue;\n            }\n\n            if (update.currentDirection) {\n                transceiver._currentDirection = update.currentDirection;\n            }\n\n            transceiver._mid = update.mid;\n            transceiver._stopped = Boolean(update.isStopped);\n            transceiver._sender._rtpParameters = new RTCRtpSendParameters(update.senderRtpParameters);\n            transceiver._receiver._rtpParameters = new RTCRtpReceiveParameters(update.receiverRtpParameters);\n        }\n\n        if (removeStopped) {\n            const stopped = this.getTransceivers().filter(t => t.stopped);\n            const newTransceivers = this._transceivers.filter(t => !stopped.includes(t.transceiver));\n\n            this._transceivers = newTransceivers;\n        }\n    }\n\n    /**\n     * Inserts transceiver into the transceiver array in the order they are created (timestamp).\n     * @param order an index that refers to when it it was created relatively.\n     * @param transceiver the transceiver object to be inserted.\n     */\n    _insertTransceiverSorted(order: number, transceiver: RTCRtpTransceiver) {\n        this._transceivers.push({ order, transceiver });\n        this._transceivers.sort((a, b) => a.order - b.order);\n    }\n}\n\n/**\n * Define the `onxxx` event handlers.\n */\nconst proto = RTCPeerConnection.prototype;\n\ndefineEventAttribute(proto, 'connectionstatechange');\ndefineEventAttribute(proto, 'icecandidate');\ndefineEventAttribute(proto, 'icecandidateerror');\ndefineEventAttribute(proto, 'iceconnectionstatechange');\ndefineEventAttribute(proto, 'icegatheringstatechange');\ndefineEventAttribute(proto, 'negotiationneeded');\ndefineEventAttribute(proto, 'signalingstatechange');\ndefineEventAttribute(proto, 'datachannel');\ndefineEventAttribute(proto, 'track');\ndefineEventAttribute(proto, 'error');\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,aAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,YAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,iBAAA,GAAAF,sBAAA,CAAAJ,OAAA;AACA,IAAAO,sBAAA,GAAAH,sBAAA,CAAAJ,OAAA;AACA,IAAAQ,eAAA,GAAAJ,sBAAA,CAAAJ,OAAA;AACA,IAAAS,oBAAA,GAAAL,sBAAA,CAAAJ,OAAA;AACA,IAAAU,gBAAA,GAAAN,sBAAA,CAAAJ,OAAA;AACA,IAAAW,qBAAA,GAAAP,sBAAA,CAAAJ,OAAA;AACA,IAAAY,wBAAA,GAAAR,sBAAA,CAAAJ,OAAA;AACA,IAAAa,eAAA,GAAAT,sBAAA,CAAAJ,OAAA;AACA,IAAAc,qBAAA,GAAAV,sBAAA,CAAAJ,OAAA;AACA,IAAAe,aAAA,GAAAX,sBAAA,CAAAJ,OAAA;AACA,IAAAgB,kBAAA,GAAAZ,sBAAA,CAAAJ,OAAA;AACA,IAAAiB,sBAAA,GAAAb,sBAAA,CAAAJ,OAAA;AACA,IAAAkB,cAAA,GAAAd,sBAAA,CAAAJ,OAAA;AACA,IAAAmB,OAAA,GAAAC,uBAAA,CAAApB,OAAA;AAAqC,SAAAqB,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAF,wBAAAM,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAAA,SAAA5B,uBAAAsB,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAiB,gBAAAjB,GAAA,EAAAW,GAAA,EAAAO,KAAA,QAAAP,GAAA,IAAAX,GAAA,IAAAQ,MAAA,CAAAC,cAAA,CAAAT,GAAA,EAAAW,GAAA,IAAAO,KAAA,EAAAA,KAAA,EAAAC,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAArB,GAAA,CAAAW,GAAA,IAAAO,KAAA,WAAAlB,GAAA;AAGrC,MAAMsB,GAAG,GAAG,IAAIC,eAAM,CAAC,IAAI,CAAC;AAC5B,MAAM;EAAEC;AAAa,CAAC,GAAGC,0BAAa;AAqDtC,IAAIC,oBAAoB,GAAG,CAAC;AAEb,MAAMC,iBAAiB,SAASC,kBAAW,CAA4B;EAalFC,WAAWA,CAACC,aAAgC,EAAE;IAC1C,KAAK,CAAC,CAAC;IAACb,eAAA,2BAbqC,IAAI;IAAAA,eAAA,4BACH,IAAI;IAAAA,eAAA,yBAClB,QAAQ;IAAAA,eAAA,4BACF,KAAK;IAAAA,eAAA,0BACL,KAAK;IAAAA,eAAA,6BACH,KAAK;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAU7C,IAAI,CAACc,KAAK,GAAGL,oBAAoB,EAAE;;IAEnC;IACA,IAAII,aAAa,EAAE;MAAA,IAAAE,qBAAA;MACf,MAAMC,OAAO,IAAAD,qBAAA,GAAGF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEI,UAAU,cAAAF,qBAAA,cAAAA,qBAAA,GAAI,EAAE;MAE/C,KAAK,MAAMG,MAAM,IAAIF,OAAO,EAAE;QAC1B,IAAIG,IAAI,GAAGD,MAAM,CAACE,GAAG,IAAIF,MAAM,CAACC,IAAI;QAEpC,OAAOD,MAAM,CAACE,GAAG;QACjB,OAAOF,MAAM,CAACC,IAAI;QAElB,IAAI,CAACA,IAAI,EAAE;UACP;QACJ;QAEA,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;UACtBA,IAAI,GAAG,CAAEA,IAAI,CAAE;QACnB;;QAEA;QACAD,MAAM,CAACC,IAAI,GAAGA,IAAI,CAACI,GAAG,CAACH,GAAG,IAAIA,GAAG,CAACI,WAAW,CAAC,CAAC,CAAC;MACpD;;MAEA;MACAX,aAAa,CAACI,UAAU,GAAGD,OAAO,CAACS,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACP,IAAI,CAAC;IAC1D;IAEA,IAAI,CAACZ,YAAY,CAACoB,kBAAkB,CAACd,aAAa,EAAE,IAAI,CAACC,KAAK,CAAC,EAAE;MAC7D,MAAM,IAAIc,KAAK,CAAC,6DAA6D,CAAC;IAClF;IAEA,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAE7B,IAAI,CAACC,eAAe,CAAC,CAAC;IAEtB5B,GAAG,CAAC6B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,OAAM,CAAC;EACnC;EAEA,MAAMqB,WAAWA,CAACC,OAAwB,EAAE;IACxC/B,GAAG,CAAC6B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,cAAa,CAAC;IAEtC,MAAM;MACFuB,OAAO;MACPC,eAAe;MACfC;IACJ,CAAC,GAAG,MAAMhC,YAAY,CAACiC,yBAAyB,CAAC,IAAI,CAAC1B,KAAK,EAAEtC,OAAO,CAACiE,qBAAqB,CAACL,OAAO,CAAC,CAAC;IAEpG/B,GAAG,CAAC6B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,iBAAgB,CAAC;IAEzCwB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,OAAO,CAACC,CAAC,IAAI;MAC1B,MAAM;QAAEC,gBAAgB;QAAEC;MAAY,CAAC,GAAGF,CAAC;MAC3C,MAAMG,SAAS,GAAG,IAAIC,qBAAY,CAAC;QAAE,GAAGF,WAAW,CAACG,MAAM;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;MAC1E,MAAMC,WAAW,GACXL,WAAW,CAACM,QAAQ,CAACF,KAAK,GAAG,IAAIG,yBAAgB,CAACP,WAAW,CAACM,QAAQ,CAACF,KAAK,CAAC,GAAG,IAAI;MAC1F,MAAMI,WAAW,GAAG,IAAIC,uBAAc,CAAC;QAAE,GAAGT,WAAW,CAACM,QAAQ;QAAEF,KAAK,EAAEC;MAAY,CAAC,CAAC;MACvF,MAAMK,cAAc,GAAG,IAAIC,0BAAiB,CAAC;QACzC,GAAGX,WAAW;QACdG,MAAM,EAAEF,SAAS;QACjBK,QAAQ,EAAEE;MACd,CAAC,CAAC;MAEF,IAAI,CAACI,wBAAwB,CAACb,gBAAgB,EAAEW,cAAc,CAAC;IACnE,CAAC,CAAC;IAEF,IAAI,CAACG,mBAAmB,CAACnB,gBAAgB,CAAC;IAE1C,OAAOF,OAAO;EAClB;EAEA,MAAMsB,YAAYA,CAAA,EAAG;IACjBtD,GAAG,CAAC6B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,eAAc,CAAC;IAEvC,MAAM;MACFuB,OAAO;MACPE;IACJ,CAAC,GAAG,MAAMhC,YAAY,CAACqD,0BAA0B,CAAC,IAAI,CAAC9C,KAAK,EAAE,CAAC,CAAC,CAAC;IAEjE,IAAI,CAAC4C,mBAAmB,CAACnB,gBAAgB,CAAC;IAE1C,OAAOF,OAAO;EAClB;EAEAwB,gBAAgBA,CAAChD,aAAa,EAAQ;IAClCN,YAAY,CAACuD,8BAA8B,CAACjD,aAAa,EAAE,IAAI,CAACC,KAAK,CAAC;EAC1E;EAEA,MAAMiD,mBAAmBA,CAACC,kBAAsE,EAAiB;IAAA,IAAAC,KAAA;IAC7G5D,GAAG,CAAC6B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,sBAAqB,CAAC;IAE9C,IAAIhB,IAAI;IAER,IAAIkE,kBAAkB,EAAE;MAAA,IAAAE,qBAAA;MACpBpE,IAAI,GAAG;QACHqE,IAAI,EAAEH,kBAAkB,CAACG,IAAI;QAC7BC,GAAG,GAAAF,qBAAA,GAAEF,kBAAkB,CAACI,GAAG,cAAAF,qBAAA,cAAAA,qBAAA,GAAI;MACnC,CAAC;MAED,IAAI,CAAC1F,OAAO,CAAC6F,cAAc,CAACvE,IAAI,CAACqE,IAAI,CAAC,EAAE;QACpC,MAAM,IAAIvC,KAAK,CAAE,8CAA6C9B,IAAI,CAACqE,IAAK,EAAC,CAAC;MAC9E;IACJ,CAAC,MAAM;MACHrE,IAAI,GAAG,IAAI;IACf;IAEA,MAAM;MACFuC,OAAO;MACPE;IACJ,CAAC,GAAG,MAAMhC,YAAY,CAAC+D,iCAAiC,CAAC,IAAI,CAACxD,KAAK,EAAEhB,IAAI,CAAC;IAE1E,IAAIuC,OAAO,CAAC8B,IAAI,IAAI9B,OAAO,CAAC+B,GAAG,EAAE;MAC7B,IAAI,CAACG,gBAAgB,GAAG,IAAIC,8BAAqB,CAACnC,OAAO,CAAC;IAC9D,CAAC,MAAM;MACH,IAAI,CAACkC,gBAAgB,GAAG,IAAI;IAChC;IAEA,IAAI,CAACb,mBAAmB,CAACnB,gBAAgB,EAAE,mBAAoB,EAAA0B,KAAA,GAAAnE,IAAI,cAAAmE,KAAA,uBAAJA,KAAA,CAAME,IAAI,MAAK,QAAQ,CAAC;IAEvF9D,GAAG,CAAC6B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,yBAAwB,CAAC;EACrD;EAEA,MAAM2D,oBAAoBA,CAACT,kBAAqE,EAAiB;IAAA,IAAAU,sBAAA,EAAAC,UAAA;IAC7GtE,GAAG,CAAC6B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,uBAAsB,CAAC;IAE/C,IAAI,CAACkD,kBAAkB,EAAE;MACrB,OAAOY,OAAO,CAACC,MAAM,CAAC,IAAIjD,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE;IAEA,MAAM9B,IAAI,GAAG;MACTqE,IAAI,EAAEH,kBAAkB,CAACG,IAAI;MAC7BC,GAAG,GAAAM,sBAAA,GAAEV,kBAAkB,CAACI,GAAG,cAAAM,sBAAA,cAAAA,sBAAA,GAAI;IACnC,CAAC;IAED,IAAI,CAAClG,OAAO,CAAC6F,cAAc,EAAAM,UAAA,GAAC7E,IAAI,CAACqE,IAAI,cAAAQ,UAAA,cAAAA,UAAA,GAAI,EAAE,CAAC,EAAE;MAC1C,MAAM,IAAI/C,KAAK,CAAE,8CAA6C9B,IAAI,CAACqE,IAAK,EAAC,CAAC;IAC9E;IAEA,MAAM;MACF9B,OAAO;MACPC,eAAe;MACfC;IACJ,CAAC,GAAG,MAAMhC,YAAY,CAACuE,kCAAkC,CAAC,IAAI,CAAChE,KAAK,EAAEhB,IAAI,CAAC;IAE3E,IAAIuC,OAAO,CAAC8B,IAAI,IAAI9B,OAAO,CAAC+B,GAAG,EAAE;MAC7B,IAAI,CAACW,iBAAiB,GAAG,IAAIP,8BAAqB,CAACnC,OAAO,CAAC;IAC/D,CAAC,MAAM;MACH,IAAI,CAAC0C,iBAAiB,GAAG,IAAI;IACjC;IAEAzC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,OAAO,CAACC,CAAC,IAAI;MAC1B,MAAM;QAAEC,gBAAgB;QAAEC;MAAY,CAAC,GAAGF,CAAC;MAC3C,MAAMG,SAAS,GAAG,IAAIC,qBAAY,CAAC;QAAE,GAAGF,WAAW,CAACG,MAAM;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;MAC1E,MAAMC,WAAW,GACXL,WAAW,CAACM,QAAQ,CAACF,KAAK,GAAG,IAAIG,yBAAgB,CAACP,WAAW,CAACM,QAAQ,CAACF,KAAK,CAAC,GAAG,IAAI;MAC1F,MAAMI,WAAW,GAAG,IAAIC,uBAAc,CAAC;QAAE,GAAGT,WAAW,CAACM,QAAQ;QAAEF,KAAK,EAAEC;MAAY,CAAC,CAAC;MACvF,MAAMK,cAAc,GAAG,IAAIC,0BAAiB,CAAC;QACzC,GAAGX,WAAW;QACdG,MAAM,EAAEF,SAAS;QACjBK,QAAQ,EAAEE;MACd,CAAC,CAAC;MAEF,IAAI,CAACI,wBAAwB,CAACb,gBAAgB,EAAEW,cAAc,CAAC;IACnE,CAAC,CAAC;IAEF,IAAI,CAACG,mBAAmB,CAACnB,gBAAgB,EAAE,mBAAoBzC,IAAI,CAACqE,IAAI,KAAK,QAAQ,CAAC;;IAEtF;IACA,MAAMa,kBAAkB,GAAG,IAAI,CAAChD,mBAAmB;IAEnD,IAAI,CAACA,mBAAmB,GAAG,EAAE;IAE7B,KAAK,MAAMiD,EAAE,IAAID,kBAAkB,EAAE;MACjC,MAAM,CAAEnC,WAAW,CAAE,GAAG,IAAI,CACvBqC,eAAe,CAAC,CAAC,CACjBzD,MAAM,CAACkB,CAAC,IAAIA,CAAC,CAACQ,QAAQ,CAACgC,EAAE,KAAMF,EAAE,CAAC9B,QAAQ,CAACgC,EAAE,CAAC;;MAEnD;MACA;MACA;;MAEA;MACA,MAAMlC,KAAuB,GAAGJ,WAAW,CAACM,QAAQ,CAACF,KAAK;MAE1DJ,WAAW,CAACuC,IAAI,GAAGH,EAAE,CAACpC,WAAW,CAACwC,GAAG;MACrCxC,WAAW,CAACyC,iBAAiB,GAAGL,EAAE,CAACpC,WAAW,CAAC0C,gBAAgB;MAC/D1C,WAAW,CAAC2C,UAAU,GAAGP,EAAE,CAACpC,WAAW,CAAC4C,SAAS;;MAEjD;MACA,MAAMC,OAAsB,GAAGT,EAAE,CAACS,OAAO,CAACnE,GAAG,CAACoE,UAAU,IAAI;QACxD;QACA;QACA,IAAI,CAAC,IAAI,CAAC7D,cAAc,CAAC3C,GAAG,CAACwG,UAAU,CAACC,QAAQ,CAAC,EAAE;UAC/C,MAAMC,MAAM,GAAG,IAAIC,oBAAW,CAAC;YAC3BF,QAAQ,EAAED,UAAU,CAACC,QAAQ;YAC7BG,cAAc,EAAEJ,UAAU,CAACI,cAAc;YACzCC,MAAM,EAAE;UACZ,CAAC,CAAC;UAEF,IAAI,CAAClE,cAAc,CAAC/B,GAAG,CAAC4F,UAAU,CAACC,QAAQ,EAAEC,MAAM,CAAC;QACxD;QAEA,MAAMA,MAAM,GAAG,IAAI,CAAC/D,cAAc,CAAC1C,GAAG,CAACuG,UAAU,CAACC,QAAQ,CAAC;QAE3D,IAAI,EAACC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEI,OAAO,CAACC,QAAQ,CAACjD,KAAK,CAAC,GAAE;UAClC4C,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,OAAO,CAACE,IAAI,CAAClD,KAAK,CAAC;QAC/B;QAEA,OAAO4C,MAAM;MACjB,CAAC,CAAC;MAEF,MAAMO,SAAS,GAAG;QACdV,OAAO;QACP7C,WAAW;QACXI,KAAK;QACLE,QAAQ,EAAEN,WAAW,CAACM;MAC1B,CAAC;MAGD,IAAI,CAACkD,aAAa,CAAC,IAAIC,sBAAa,CAAC,OAAO,EAAEF,SAAS,CAAC,CAAC;MAEzDV,OAAO,CAAChD,OAAO,CAACmD,MAAM,IAAI;QACtBA,MAAM,CAACQ,aAAa,CAAC,IAAIE,8BAAqB,CAAC,UAAU,EAAE;UAAEtD;QAAM,CAAC,CAAC,CAAC;MAC1E,CAAC,CAAC;;MAEF;MACAA,KAAK,CAACuD,iBAAiB,CAAC,KAAK,CAAC;IAClC;IAEAnG,GAAG,CAAC6B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,0BAAyB,CAAC;EACtD;EAEA,MAAM2F,eAAeA,CAACC,SAAS,EAAiB;IAC5CrG,GAAG,CAAC6B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,kBAAiB,CAAC;IAE1C,IAAI,CAAC4F,SAAS,IAAI,CAACA,SAAS,CAACA,SAAS,EAAE;MACpC;MACA;IACJ;IAEA,IAAI,CAACA,SAAS,CAACC,aAAa,KAAK,IAAI,IAChCD,SAAS,CAACC,aAAa,KAAKC,SAAS,MACrCF,SAAS,CAACG,MAAM,KAAK,IAAI,IACzBH,SAAS,CAACG,MAAM,KAAKD,SAAS,CAAC,EAClC;MACE,MAAM,IAAIE,SAAS,CAAC,iEAAiE,CAAC;IAC1F;IAEA,MAAMC,MAAM,GAAG,MAAMxG,YAAY,CAACyG,6BAA6B,CAC3D,IAAI,CAAClG,KAAK,EACVtC,OAAO,CAACyI,SAAS,CAACP,SAAS,CAC/B,CAAC;IAED,IAAI,CAAC3B,iBAAiB,GAAG,IAAIP,8BAAqB,CAACuC,MAAM,CAAC;EAC9D;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,QAAQA,CAACjE,KAAuB,EAA2C;IACvE5C,GAAG,CAAC6B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,WAAU,CAAC;IAEnC,IAAI,IAAI,CAACqG,eAAe,KAAK,QAAQ,EAAE;MACnC,MAAM,IAAIvF,KAAK,CAAC,2BAA2B,CAAC;IAChD;IAEA,IAAI,IAAI,CAACwF,YAAY,CAACnE,KAAK,CAAC,EAAE;MAC1B,MAAM,IAAIrB,KAAK,CAAC,kCAAkC,CAAC;IACvD;IAAC,SAAAyF,IAAA,GAAAC,SAAA,CAAAC,MAAA,EATgC7B,OAAO,OAAArE,KAAA,CAAAgG,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAAP9B,OAAO,CAAA8B,IAAA,QAAAF,SAAA,CAAAE,IAAA;IAAA;IAWxC,MAAMC,SAAS,GAAG/B,OAAO,CAACnE,GAAG,CAACG,CAAC,IAAIA,CAAC,CAACyD,EAAE,CAAC;IACxC,MAAMuC,MAAM,GAAGnH,YAAY,CAACoH,sBAAsB,CAAC,IAAI,CAAC7G,KAAK,EAAEmC,KAAK,CAACkC,EAAE,EAAE;MAAEsC;IAAU,CAAC,CAAC;IAEvF,IAAIC,MAAM,KAAK,IAAI,EAAE;MACjB,MAAM,IAAI9F,KAAK,CAAC,sBAAsB,CAAC;IAC3C;IAEA,MAAM;MAAEgB,gBAAgB;MAAEC,WAAW;MAAEG;IAAO,CAAC,GAAG0E,MAAM;;IAExD;IACA;IACA,MAAM,CAAEE,cAAc,CAAE,GAAG,IAAI,CAC1BC,UAAU,CAAC,CAAC,CACZpG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACyD,EAAE,KAAKnC,MAAM,CAACmC,EAAE,CAAC;IAEpC,IAAIyC,cAAc,EAAE;MAChB;MACAA,cAAc,CAACE,MAAM,GAAG7E,KAAK;;MAE7B;MACA,MAAM,CAAE8E,mBAAmB,CAAE,GAAG,IAAI,CAC/B7C,eAAe,CAAC,CAAC,CACjBzD,MAAM,CAACkB,CAAC,IAAIA,CAAC,CAACK,MAAM,CAACmC,EAAE,KAAKyC,cAAc,CAACzC,EAAE,CAAC;MAEnD4C,mBAAmB,CAACvC,UAAU,GAAG3C,WAAW,CAAC4C,SAAS;MACtDsC,mBAAmB,CAACzC,iBAAiB,GAAGzC,WAAW,CAAC0C,gBAAgB;MAEpE,OAAOqC,cAAc;IACzB;;IAEA;IACA,MAAM9E,SAAS,GAAG,IAAIC,qBAAY,CAAC;MAAE,GAAGF,WAAW,CAACG,MAAM;MAAEC;IAAM,CAAC,CAAC;IACpE,MAAMC,WAAW,GAAGL,WAAW,CAACM,QAAQ,CAACF,KAAK,GAAG,IAAIG,yBAAgB,CAACP,WAAW,CAACM,QAAQ,CAACF,KAAK,CAAC,GAAG,IAAI;IACxG,MAAMI,WAAW,GAAG,IAAIC,uBAAc,CAAC;MAAE,GAAGT,WAAW,CAACM,QAAQ;MAAEF,KAAK,EAAEC;IAAY,CAAC,CAAC;IACvF,MAAMK,cAAc,GAAG,IAAIC,0BAAiB,CAAC;MACzC,GAAGX,WAAW;MACdG,MAAM,EAAEF,SAAS;MACjBK,QAAQ,EAAEE;IACd,CAAC,CAAC;IAEF,IAAI,CAACI,wBAAwB,CAACb,gBAAgB,EAAEW,cAAc,CAAC;IAE/D,OAAOT,SAAS;EACpB;EAEAkF,cAAcA,CAACC,MAA4C,EAAEC,IAAI,EAAqB;IAClF7H,GAAG,CAAC6B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,iBAAgB,CAAC;IAEzC,IAAIqH,GAAG,GAAG,CAAC,CAAC;IAEZ,IAAIF,MAAM,KAAK,OAAO,EAAE;MACpBE,GAAG,GAAG;QAAEhE,IAAI,EAAE;MAAQ,CAAC;IAC3B,CAAC,MAAM,IAAI8D,MAAM,KAAK,OAAO,EAAE;MAC3BE,GAAG,GAAG;QAAEhE,IAAI,EAAE;MAAQ,CAAC;IAC3B,CAAC,MAAM;MACHgE,GAAG,GAAG;QAAEC,OAAO,EAAEH,MAAM,CAAC9C;MAAG,CAAC;IAChC;;IAEA;IACA,IAAI+C,IAAI,IAAIA,IAAI,CAACxC,OAAO,EAAE;MACtBwC,IAAI,CAACT,SAAS,GAAGS,IAAI,CAACxC,OAAO,CAACnE,GAAG,CAACsE,MAAM,IAAIA,MAAM,CAACV,EAAE,CAAC;IAC1D;IAEA,MAAMuC,MAAM,GAAGnH,YAAY,CAAC8H,4BAA4B,CAAC,IAAI,CAACvH,KAAK,EAAE;MAAE,GAAGqH,GAAG;MAAED,IAAI,EAAE;QAAE,GAAGA;MAAK;IAAE,CAAC,CAAC;IAEnG,IAAIR,MAAM,KAAK,IAAI,EAAE;MACjB,MAAM,IAAI9F,KAAK,CAAC,gCAAgC,CAAC;IACrD;IAEA,MAAMe,CAAC,GAAG+E,MAAM,CAAC7E,WAAW;IAC5B,IAAII,KAA8B,GAAG,IAAI;IAEzC,IAAI,OAAOgF,MAAM,KAAK,QAAQ,EAAE;MAC5B,IAAItF,CAAC,CAACK,MAAM,CAACC,KAAK,EAAE;QAChBA,KAAK,GAAG,IAAIG,yBAAgB,CAACT,CAAC,CAACK,MAAM,CAACC,KAAK,CAAC;MAChD;IACJ,CAAC,MAAM;MACH;MACAA,KAAK,GAAGgF,MAAM;IAClB;IAEA,MAAMjF,MAAM,GAAG,IAAID,qBAAY,CAAC;MAAE,GAAGJ,CAAC,CAACK,MAAM;MAAEC;IAAM,CAAC,CAAC;IACvD,MAAMC,WAAW,GAAGP,CAAC,CAACQ,QAAQ,CAACF,KAAK,GAAG,IAAIG,yBAAgB,CAACT,CAAC,CAACQ,QAAQ,CAACF,KAAK,CAAC,GAAG,IAAI;IACpF,MAAME,QAAQ,GAAG,IAAIG,uBAAc,CAAC;MAAE,GAAGX,CAAC,CAACQ,QAAQ;MAAEF,KAAK,EAAEC;IAAY,CAAC,CAAC;IAC1E,MAAML,WAAW,GAAG,IAAIW,0BAAiB,CAAC;MACtC,GAAGkE,MAAM,CAAC7E,WAAW;MACrBG,MAAM;MACNG;IACJ,CAAC,CAAC;IAEF,IAAI,CAACM,wBAAwB,CAACiE,MAAM,CAAC9E,gBAAgB,EAAEC,WAAW,CAAC;IAEnE,OAAOA,WAAW;EACtB;EAEAyF,WAAWA,CAACtF,MAAoB,EAAE;IAC9B3C,GAAG,CAAC6B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,cAAa,CAAC;IAEtC,IAAI,IAAI,CAACA,KAAK,KAAKkC,MAAM,CAACuF,iBAAiB,EAAE;MACzC,MAAM,IAAI3G,KAAK,CAAC,gDAAgD,CAAC;IACrE;IAEA,IAAI,IAAI,CAACuF,eAAe,KAAK,QAAQ,EAAE;MACnC,MAAM,IAAIvF,KAAK,CAAC,2BAA2B,CAAC;IAChD;IAEA,MAAMgG,cAAc,GAAG,IAAI,CACtBC,UAAU,CAAC,CAAC,CACZW,IAAI,CAAC9G,CAAC,IAAIA,CAAC,KAAKsB,MAAM,CAAC;IAE5B,IAAI,CAAC4E,cAAc,EAAE;MACjB,MAAM,IAAIhG,KAAK,CAAC,uBAAuB,CAAC;IAC5C;IAEA,IAAIgG,cAAc,CAAC3E,KAAK,KAAK,IAAI,EAAE;MAC/B;IACJ;;IAEA;IACA1C,YAAY,CAACkI,yBAAyB,CAAC,IAAI,CAAC3H,KAAK,EAAEkC,MAAM,CAACmC,EAAE,CAAC;IAE7DyC,cAAc,CAACE,MAAM,GAAG,IAAI;IAE5B,MAAM,CAAEC,mBAAmB,CAAE,GAAG,IAAI,CAC/B7C,eAAe,CAAC,CAAC,CACjBzD,MAAM,CAACkB,CAAC,IAAIA,CAAC,CAACK,MAAM,CAACmC,EAAE,KAAKyC,cAAc,CAACzC,EAAE,CAAC;IAEnD4C,mBAAmB,CAACvC,UAAU,GAAGuC,mBAAmB,CAACtC,SAAS,KAAK,UAAU,GAAG,UAAU,GAAG,UAAU;EAC3G;EAEA,MAAMiD,QAAQA,CAACC,QAA2B,EAAE;IACxCtI,GAAG,CAAC6B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,WAAU,CAAC;IAEnC,IAAI,CAAC6H,QAAQ,EAAE;MACX,MAAMC,IAAI,GAAG,MAAMrI,YAAY,CAACsI,sBAAsB,CAAC,IAAI,CAAC/H,KAAK,CAAC;;MAElE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,OAAO,IAAIiB,GAAG,CAAC+G,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC,CAAC;IACpC,CAAC,MAAM;MACH,MAAMI,OAAO,GAAG,IAAI,CAACnB,UAAU,CAAC,CAAC,CAACpG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACuB,KAAK,KAAK0F,QAAQ,CAAC;MACnE,MAAMM,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC,CAACzH,MAAM,CAAC0H,CAAC,IAAIA,CAAC,CAAClG,KAAK,KAAK0F,QAAQ,CAAC;MACvE,MAAMS,OAAO,GAAGJ,OAAO,CAACzB,MAAM,GAAG0B,SAAS,CAAC1B,MAAM;MAEjD,IAAI6B,OAAO,KAAK,CAAC,EAAE;QACf,MAAM,IAAIxH,KAAK,CAAC,6DAA6D,CAAC;MAClF,CAAC,MAAM,IAAIwH,OAAO,GAAG,CAAC,EAAE;QACpB,MAAM,IAAIxH,KAAK,CAAC,yDAAyD,CAAC;MAC9E,CAAC,MAAM;QACH,MAAMyH,EAAE,GAAGL,OAAO,CAAC,CAAC,CAAC,IAAIC,SAAS,CAAC,CAAC,CAAC;QAErC,OAAOI,EAAE,CAACX,QAAQ,CAAC,CAAC;MACxB;IACJ;EACJ;EAEAxD,eAAeA,CAAA,EAAwB;IACnC,OAAO,IAAI,CAACrD,aAAa,CAACN,GAAG,CAAC+H,CAAC,IAAIA,CAAC,CAACzG,WAAW,CAAC;EACrD;EAEAgF,UAAUA,CAAA,EAAmB;IACzB,OAAO,IAAI,CAAChG,aAAa,CAACJ,MAAM,CAAC6H,CAAC,IAAI,CAACA,CAAC,CAACzG,WAAW,CAAC0G,OAAO,CAAC,CAAChI,GAAG,CAAC+H,CAAC,IAAIA,CAAC,CAACzG,WAAW,CAACG,MAAM,CAAC;EAChG;EAEAkG,YAAYA,CAAA,EAAqB;IAC7B,OAAO,IAAI,CAACrH,aAAa,CAACJ,MAAM,CAAC6H,CAAC,IAAI,CAACA,CAAC,CAACzG,WAAW,CAAC0G,OAAO,CAAC,CAAChI,GAAG,CAAC+H,CAAC,IAAIA,CAAC,CAACzG,WAAW,CAACM,QAAQ,CAAC;EAClG;EAEAqG,KAAKA,CAAA,EAAS;IACVnJ,GAAG,CAAC6B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,QAAO,CAAC;IAEhC,IAAI,IAAI,CAACqG,eAAe,KAAK,QAAQ,EAAE;MACnC;IACJ;IAEA5G,YAAY,CAACkJ,mBAAmB,CAAC,IAAI,CAAC3I,KAAK,CAAC;;IAE5C;IACA,IAAI,CAACe,aAAa,CAACa,OAAO,CAACgH,IAAA,IAAoB;MAAA,IAAnB;QAAE7G;MAAY,CAAC,GAAA6G,IAAA;MACvC7G,WAAW,CAAC8G,WAAW,CAAC,CAAC;IAC7B,CAAC,CAAC;EACN;EAEAC,UAAUA,CAAA,EAAS;IACfrJ,YAAY,CAACsJ,wBAAwB,CAAC,IAAI,CAAC/I,KAAK,CAAC;EACrD;EAEAmB,eAAeA,CAAA,EAAS;IACpB,IAAA6H,yBAAW,EAAC,IAAI,EAAE,qCAAqC,EAAG7E,EAAO,IAAK;MAClE,IAAIA,EAAE,CAAC8E,IAAI,KAAK,IAAI,CAACjJ,KAAK,EAAE;QACxB;MACJ;MAEA,IAAI,CAACuF,aAAa,CAAC,IAAI2D,YAAK,CAAC,mBAAmB,CAAC,CAAC;IACtD,CAAC,CAAC;IAEF,IAAAF,yBAAW,EAAC,IAAI,EAAE,oCAAoC,EAAG7E,EAAO,IAAK;MACjE,IAAIA,EAAE,CAAC8E,IAAI,KAAK,IAAI,CAACjJ,KAAK,EAAE;QACxB;MACJ;MAEA,IAAI,CAACmJ,kBAAkB,GAAGhF,EAAE,CAACgF,kBAAkB;MAE/C,IAAI,CAAC5D,aAAa,CAAC,IAAI2D,YAAK,CAAC,0BAA0B,CAAC,CAAC;IAC7D,CAAC,CAAC;IAEF,IAAAF,yBAAW,EAAC,IAAI,EAAE,4BAA4B,EAAG7E,EAAO,IAAK;MACzD,IAAIA,EAAE,CAAC8E,IAAI,KAAK,IAAI,CAACjJ,KAAK,EAAE;QACxB;MACJ;MAEA,IAAI,CAACqG,eAAe,GAAGlC,EAAE,CAACkC,eAAe;MAEzC,IAAI,CAACd,aAAa,CAAC,IAAI2D,YAAK,CAAC,uBAAuB,CAAC,CAAC;MAEtD,IAAI/E,EAAE,CAACkC,eAAe,KAAK,QAAQ,EAAE;QACjC;QACA,IAAA+C,4BAAc,EAAC,IAAI,CAAC;QAEpB3J,YAAY,CAAC4J,qBAAqB,CAAC,IAAI,CAACrJ,KAAK,CAAC;MAClD;IACJ,CAAC,CAAC;IAEF,IAAAgJ,yBAAW,EAAC,IAAI,EAAE,qCAAqC,EAAG7E,EAAO,IAAK;MAClE,IAAIA,EAAE,CAAC8E,IAAI,KAAK,IAAI,CAACjJ,KAAK,EAAE;QACxB;MACJ;MAEA,IAAI,CAACsJ,cAAc,GAAGnF,EAAE,CAACmF,cAAc;MAEvC,IAAI,CAAC/D,aAAa,CAAC,IAAI2D,YAAK,CAAC,sBAAsB,CAAC,CAAC;IACzD,CAAC,CAAC;;IAEF;IACA,IAAAF,yBAAW,EAAC,IAAI,EAAE,uBAAuB,EAAG7E,EAAO,IAAK;MACpD,IAAIA,EAAE,CAAC8E,IAAI,KAAK,IAAI,CAACjJ,KAAK,EAAE;QACxB;MACJ;MAEAT,GAAG,CAAC6B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,UAAS,CAAC;;MAElC;MACA;MACA;MACA,IAAI,CAACkB,mBAAmB,CAACmE,IAAI,CAAClB,EAAE,CAAC;IACrC,CAAC,CAAC;IAEF,IAAA6E,yBAAW,EAAC,IAAI,EAAE,6BAA6B,EAAG7E,EAAO,IAAK;MAC1D,IAAIA,EAAE,CAAC8E,IAAI,KAAK,IAAI,CAACjJ,KAAK,EAAE;QACxB;MACJ;MAEAT,GAAG,CAAC6B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,kBAAiBmE,EAAE,CAACoF,UAAW,EAAC,CAAC;MAEzD,MAAMlH,QAAQ,GAAG,IAAI,CAAC+F,YAAY,CAAC,CAAC,CAACV,IAAI,CAACW,CAAC,IAAIA,CAAC,CAAChE,EAAE,KAAKF,EAAE,CAACoF,UAAU,CAAC;MACtE,MAAMpH,KAAK,GAAGE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEF,KAAK;MAE7B,IAAIE,QAAQ,IAAIF,KAAK,EAAE;QACnB;QACA;QACA;QACA;QACA;QACA,KAAK,MAAM4C,MAAM,IAAI,IAAI,CAAC/D,cAAc,CAACwI,MAAM,CAAC,CAAC,EAAE;UAC/C,IAAIzE,MAAM,CAACI,OAAO,CAACC,QAAQ,CAACjD,KAAK,CAAC,EAAE;YAChC,MAAMsH,QAAQ,GAAG1E,MAAM,CAACI,OAAO,CAACuE,OAAO,CAACvH,KAAK,CAAC;YAE9C5C,GAAG,CAAC6B,KAAK,CAAE,GAAE,IAAI,CAACpB,KAAM,gBAAemC,KAAK,CAACkC,EAAG,EAAC,CAAC;YAElDU,MAAM,CAACI,OAAO,CAACwE,MAAM,CAACF,QAAQ,EAAE,CAAC,CAAC;YAElC1E,MAAM,CAACQ,aAAa,CAAC,IAAIE,8BAAqB,CAAC,aAAa,EAAE;cAAEtD;YAAM,CAAC,CAAC,CAAC;;YAEzE;YACAA,KAAK,CAACuD,iBAAiB,CAAC,IAAI,CAAC;UACjC;QACJ;MACJ;IACJ,CAAC,CAAC;IAEF,IAAAsD,yBAAW,EAAC,IAAI,EAAE,+BAA+B,EAAG7E,EAAO,IAAK;MAC5D,IAAIA,EAAE,CAAC8E,IAAI,KAAK,IAAI,CAACjJ,KAAK,EAAE;QACxB;MACJ;MAEA,MAAMuB,OAAO,GAAG4C,EAAE,CAACb,GAAG;;MAEtB;MACA,IAAI/B,OAAO,CAAC8B,IAAI,IAAI9B,OAAO,CAAC+B,GAAG,EAAE;QAC7B,IAAI,CAACG,gBAAgB,GAAG,IAAIC,8BAAqB,CAACnC,OAAO,CAAC;MAC9D,CAAC,MAAM;QACH,IAAI,CAACkC,gBAAgB,GAAG,IAAI;MAChC;MAEA,MAAMmC,SAAS,GAAG,IAAIgE,wBAAe,CAACzF,EAAE,CAACyB,SAAS,CAAC;MAEnD,IAAI,CAACL,aAAa,CAAC,IAAIsE,6BAAoB,CAAC,cAAc,EAAE;QAAEjE;MAAU,CAAC,CAAC,CAAC;IAC/E,CAAC,CAAC;IAEF,IAAAoD,yBAAW,EAAC,IAAI,EAAE,mCAAmC,EAAG7E,EAAO,IAAK;MAChE,IAAIA,EAAE,CAAC8E,IAAI,KAAK,IAAI,CAACjJ,KAAK,EAAE;QACxB;MACJ;MAEA,IAAI,CAAC8J,iBAAiB,GAAG3F,EAAE,CAAC2F,iBAAiB;MAE7C,IAAI,IAAI,CAACA,iBAAiB,KAAK,UAAU,EAAE;QACvC,MAAMvI,OAAO,GAAG4C,EAAE,CAACb,GAAG;;QAEtB;QACA,IAAI/B,OAAO,CAAC8B,IAAI,IAAI9B,OAAO,CAAC+B,GAAG,EAAE;UAC7B,IAAI,CAACG,gBAAgB,GAAG,IAAIC,8BAAqB,CAACnC,OAAO,CAAC;QAC9D,CAAC,MAAM;UACH,IAAI,CAACkC,gBAAgB,GAAG,IAAI;QAChC;QAEA,IAAI,CAAC8B,aAAa,CAAC,IAAIsE,6BAAoB,CAAC,cAAc,EAAE;UAAEjE,SAAS,EAAE;QAAK,CAAC,CAAC,CAAC;MACrF;MAEA,IAAI,CAACL,aAAa,CAAC,IAAI2D,YAAK,CAAC,yBAAyB,CAAC,CAAC;IAC5D,CAAC,CAAC;IAEF,IAAAF,yBAAW,EAAC,IAAI,EAAE,kCAAkC,EAAG7E,EAAO,IAAK;MAC/D,IAAIA,EAAE,CAAC8E,IAAI,KAAK,IAAI,CAACjJ,KAAK,EAAE;QACxB;MACJ;MAEA,MAAM+J,OAAO,GAAG,IAAIC,uBAAc,CAAC7F,EAAE,CAAC8F,WAAW,CAAC;MAElD,IAAI,CAAC1E,aAAa,CAAC,IAAI2E,4BAAmB,CAAC,aAAa,EAAE;QAAEH;MAAQ,CAAC,CAAC,CAAC;;MAEvE;MACA;MACAA,OAAO,CAACxE,aAAa,CAAC,IAAI2E,4BAAmB,CAAC,MAAM,EAAE;QAAEH;MAAQ,CAAC,CAAC,CAAC;IACvE,CAAC,CAAC;IAEF,IAAAf,yBAAW,EAAC,IAAI,EAAE,6BAA6B,EAAG7E,EAAO,IAAK;MAC1D,IAAIA,EAAE,CAAC8E,IAAI,KAAK,IAAI,CAACjJ,KAAK,EAAE;QACxB;MACJ;MAEA,MAAM,CACFmC,KAAK,CACR,GAAG,IAAI,CAACiG,YAAY,CAAC,CAAC,CAAC3H,GAAG,CAAC4H,CAAC,IAAIA,CAAC,CAAClG,KAAK,CAAC,CAACxB,MAAM,CAACkB,CAAC,IAAI,CAAAA,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEwC,EAAE,MAAKF,EAAE,CAACmD,OAAO,CAAC;MAE3E,IAAInF,KAAK,EAAE;QACPA,KAAK,CAACuD,iBAAiB,CAACvB,EAAE,CAACgG,KAAK,CAAC;MACrC;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,iBAAiBA,CAACC,KAAa,EAAEC,eAAoC,EAAkB;IACnF,IAAI9D,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;MACxB,MAAM,IAAIT,SAAS,CAAC,oCAAoC,CAAC;IAC7D;IAEA,IAAIsE,eAAe,IAAI,IAAI,IAAIA,eAAe,EAAE;MAC5C,MAAMjG,EAAE,GAAGiG,eAAe,CAACjG,EAAE;MAE7B,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;QACxB,MAAM,IAAI2B,SAAS,CAAC,mCAAmC,GAAG3B,EAAE,CAAC;MACjE;IACJ;IAEA,MAAMkG,WAAW,GAAG9K,YAAY,CAAC2K,iBAAiB,CAAC,IAAI,CAACpK,KAAK,EAAEwK,MAAM,CAACH,KAAK,CAAC,EAAEC,eAAe,CAAC;IAE9F,IAAIC,WAAW,KAAK,IAAI,EAAE;MACtB,MAAM,IAAIvE,SAAS,CAAC,kCAAkC,CAAC;IAC3D;IAEA,OAAO,IAAIgE,uBAAc,CAACO,WAAW,CAAC;EAC1C;;EAEA;AACJ;AACA;AACA;EACIjE,YAAYA,CAACnE,KAAuB,EAAW;IAC3C,MAAM,CAAED,MAAM,CAAE,GAAG,IAAI,CAClB6E,UAAU,CAAC,CAAC,CACZpG,MAAM,CACHuB,MAAM;MAAA,IAAAuI,aAAA;MAAA,OAAI,EAAAA,aAAA,GAAAvI,MAAM,CAACC,KAAK,cAAAsI,aAAA,uBAAZA,aAAA,CAAcpG,EAAE,MAAKlC,KAAK,CAACkC,EAAE;IAAA,CAC3C,CAAC;IAEL,OAAOnC,MAAM,GAAE,IAAI,GAAG,KAAK;EAC/B;;EAEA;AACJ;AACA;EACIU,mBAAmBA,CAAC8H,kBAAkB,EAAyB;IAAA,IAAvBC,aAAa,GAAAnE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAV,SAAA,GAAAU,SAAA,MAAG,KAAK;IACzD,KAAK,MAAMoE,MAAM,IAAIF,kBAAkB,EAAE;MACrC,MAAM,CAAE3I,WAAW,CAAE,GAAG,IAAI,CACvBqC,eAAe,CAAC,CAAC,CACjBzD,MAAM,CAACkB,CAAC,IAAIA,CAAC,CAACK,MAAM,CAACmC,EAAE,KAAKuG,MAAM,CAACC,aAAa,CAAC;MAEtD,IAAI,CAAC9I,WAAW,EAAE;QACd;MACJ;MAEA,IAAI6I,MAAM,CAACnG,gBAAgB,EAAE;QACzB1C,WAAW,CAACyC,iBAAiB,GAAGoG,MAAM,CAACnG,gBAAgB;MAC3D;MAEA1C,WAAW,CAACuC,IAAI,GAAGsG,MAAM,CAACrG,GAAG;MAC7BxC,WAAW,CAAC+I,QAAQ,GAAGC,OAAO,CAACH,MAAM,CAACI,SAAS,CAAC;MAChDjJ,WAAW,CAACkJ,OAAO,CAACC,cAAc,GAAG,IAAIC,6BAAoB,CAACP,MAAM,CAACQ,mBAAmB,CAAC;MACzFrJ,WAAW,CAACsJ,SAAS,CAACH,cAAc,GAAG,IAAII,gCAAuB,CAACV,MAAM,CAACW,qBAAqB,CAAC;IACpG;IAEA,IAAIZ,aAAa,EAAE;MACf,MAAMlC,OAAO,GAAG,IAAI,CAACrE,eAAe,CAAC,CAAC,CAACzD,MAAM,CAACkB,CAAC,IAAIA,CAAC,CAAC4G,OAAO,CAAC;MAC7D,MAAMjH,eAAe,GAAG,IAAI,CAACT,aAAa,CAACJ,MAAM,CAACkB,CAAC,IAAI,CAAC4G,OAAO,CAACrD,QAAQ,CAACvD,CAAC,CAACE,WAAW,CAAC,CAAC;MAExF,IAAI,CAAChB,aAAa,GAAGS,eAAe;IACxC;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACImB,wBAAwBA,CAAC6I,KAAa,EAAEzJ,WAA8B,EAAE;IACpE,IAAI,CAAChB,aAAa,CAACsE,IAAI,CAAC;MAAEmG,KAAK;MAAEzJ;IAAY,CAAC,CAAC;IAC/C,IAAI,CAAChB,aAAa,CAAC0K,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACF,KAAK,GAAGG,CAAC,CAACH,KAAK,CAAC;EACxD;AACJ;;AAEA;AACA;AACA;AAFAI,OAAA,CAAAzN,OAAA,GAAAyB,iBAAA;AAGA,MAAMiM,KAAK,GAAGjM,iBAAiB,CAACf,SAAS;AAEzC,IAAAiN,2BAAoB,EAACD,KAAK,EAAE,uBAAuB,CAAC;AACpD,IAAAC,2BAAoB,EAACD,KAAK,EAAE,cAAc,CAAC;AAC3C,IAAAC,2BAAoB,EAACD,KAAK,EAAE,mBAAmB,CAAC;AAChD,IAAAC,2BAAoB,EAACD,KAAK,EAAE,0BAA0B,CAAC;AACvD,IAAAC,2BAAoB,EAACD,KAAK,EAAE,yBAAyB,CAAC;AACtD,IAAAC,2BAAoB,EAACD,KAAK,EAAE,mBAAmB,CAAC;AAChD,IAAAC,2BAAoB,EAACD,KAAK,EAAE,sBAAsB,CAAC;AACnD,IAAAC,2BAAoB,EAACD,KAAK,EAAE,aAAa,CAAC;AAC1C,IAAAC,2BAAoB,EAACD,KAAK,EAAE,OAAO,CAAC;AACpC,IAAAC,2BAAoB,EAACD,KAAK,EAAE,OAAO,CAAC"}