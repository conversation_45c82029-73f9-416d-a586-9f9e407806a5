{"version": 3, "names": ["NativeModules", "WebRTCModule", "RTCRtpTransceiver", "constructor", "args", "_args$mid", "_args$currentDirectio", "_defineProperty", "_peerConnectionId", "peerConnectionId", "_mid", "mid", "_direction", "direction", "_currentDirection", "currentDirection", "_stopped", "Boolean", "isStopped", "_sender", "sender", "_receiver", "receiver", "stopped", "val", "includes", "TypeError", "Error", "oldDirection", "transceiverSetDirection", "id", "catch", "stop", "transceiverStop", "then", "_setStopped", "setCodecPreferences", "codecs", "transceiverSetCodecPreferences"], "sources": ["RTCRtpTransceiver.ts"], "sourcesContent": ["import { NativeModules } from 'react-native';\n\nimport RTCRtpCodecCapability from './RTCRtpCodecCapability';\nimport RTCRtpReceiver from './RTCRtpReceiver';\nimport RTCRtpSender from './RTCRtpSender';\n\nconst { WebRTCModule } = NativeModules;\n\nexport default class RTCRtpTransceiver {\n    _peerConnectionId: number;\n    _sender: RTCRtpSender;\n    _receiver: RTCRtpReceiver;\n\n    _mid: string | null = null;\n    _direction: string;\n    _currentDirection: string;\n    _stopped: boolean;\n\n    constructor(args: {\n        peerConnectionId: number,\n        isStopped: boolean,\n        direction: string,\n        currentDirection: string,\n        mid?: string,\n        sender: RTCRtpSender,\n        receiver: RTCRtpReceiver,\n    }) {\n        this._peerConnectionId = args.peerConnectionId;\n        this._mid = args.mid ?? null;\n        this._direction = args.direction;\n        this._currentDirection = args.currentDirection ?? null;\n        this._stopped = Boolean(args.isStopped);\n        this._sender = args.sender;\n        this._receiver = args.receiver;\n    }\n\n    get mid() {\n        return this._mid;\n    }\n\n    get stopped() {\n        return this._stopped;\n    }\n\n    get direction() {\n        return this._direction;\n    }\n\n    set direction(val) {\n        if (![ 'sendonly', 'recvonly', 'sendrecv', 'inactive' ].includes(val)) {\n            throw new TypeError('Invalid direction provided');\n        }\n\n        if (this._stopped) {\n            throw new Error('Transceiver Stopped');\n        }\n\n        if (this._direction === val) {\n            return;\n        }\n\n        const oldDirection = this._direction;\n\n        WebRTCModule.transceiverSetDirection(this._peerConnectionId, this.sender.id, val)\n            .catch(() => {\n                this._direction = oldDirection;\n            });\n\n        this._direction = val;\n    }\n\n    get currentDirection() {\n        return this._currentDirection;\n    }\n\n    get sender() {\n        return this._sender;\n    }\n\n    get receiver() {\n        return this._receiver;\n    }\n\n    stop() {\n        if (this._stopped) {\n            return;\n        }\n\n        WebRTCModule.transceiverStop(this._peerConnectionId, this.sender.id)\n            .then(() => this._setStopped());\n    }\n\n    setCodecPreferences(codecs: RTCRtpCodecCapability[]) {\n        WebRTCModule.transceiverSetCodecPreferences(\n            this._peerConnectionId,\n            this.sender.id,\n            codecs\n        );\n    }\n\n    _setStopped() {\n        this._stopped = true;\n        this._direction = 'stopped';\n        this._currentDirection = 'stopped';\n        this._mid = null;\n    }\n}\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,cAAc;AAM5C,MAAM;EAAEC;AAAa,CAAC,GAAGD,aAAa;AAEtC,eAAe,MAAME,iBAAiB,CAAC;EAUnCC,WAAWA,CAACC,IAQX,EAAE;IAAA,IAAAC,SAAA,EAAAC,qBAAA;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,eAbmB,IAAI;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IActB,IAAI,CAACC,iBAAiB,GAAGJ,IAAI,CAACK,gBAAgB;IAC9C,IAAI,CAACC,IAAI,IAAAL,SAAA,GAAGD,IAAI,CAACO,GAAG,cAAAN,SAAA,cAAAA,SAAA,GAAI,IAAI;IAC5B,IAAI,CAACO,UAAU,GAAGR,IAAI,CAACS,SAAS;IAChC,IAAI,CAACC,iBAAiB,IAAAR,qBAAA,GAAGF,IAAI,CAACW,gBAAgB,cAAAT,qBAAA,cAAAA,qBAAA,GAAI,IAAI;IACtD,IAAI,CAACU,QAAQ,GAAGC,OAAO,CAACb,IAAI,CAACc,SAAS,CAAC;IACvC,IAAI,CAACC,OAAO,GAAGf,IAAI,CAACgB,MAAM;IAC1B,IAAI,CAACC,SAAS,GAAGjB,IAAI,CAACkB,QAAQ;EAClC;EAEA,IAAIX,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACD,IAAI;EACpB;EAEA,IAAIa,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACP,QAAQ;EACxB;EAEA,IAAIH,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACD,UAAU;EAC1B;EAEA,IAAIC,SAASA,CAACW,GAAG,EAAE;IACf,IAAI,CAAC,CAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAE,CAACC,QAAQ,CAACD,GAAG,CAAC,EAAE;MACnE,MAAM,IAAIE,SAAS,CAAC,4BAA4B,CAAC;IACrD;IAEA,IAAI,IAAI,CAACV,QAAQ,EAAE;MACf,MAAM,IAAIW,KAAK,CAAC,qBAAqB,CAAC;IAC1C;IAEA,IAAI,IAAI,CAACf,UAAU,KAAKY,GAAG,EAAE;MACzB;IACJ;IAEA,MAAMI,YAAY,GAAG,IAAI,CAAChB,UAAU;IAEpCX,YAAY,CAAC4B,uBAAuB,CAAC,IAAI,CAACrB,iBAAiB,EAAE,IAAI,CAACY,MAAM,CAACU,EAAE,EAAEN,GAAG,CAAC,CAC5EO,KAAK,CAAC,MAAM;MACT,IAAI,CAACnB,UAAU,GAAGgB,YAAY;IAClC,CAAC,CAAC;IAEN,IAAI,CAAChB,UAAU,GAAGY,GAAG;EACzB;EAEA,IAAIT,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACD,iBAAiB;EACjC;EAEA,IAAIM,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACD,OAAO;EACvB;EAEA,IAAIG,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACD,SAAS;EACzB;EAEAW,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAChB,QAAQ,EAAE;MACf;IACJ;IAEAf,YAAY,CAACgC,eAAe,CAAC,IAAI,CAACzB,iBAAiB,EAAE,IAAI,CAACY,MAAM,CAACU,EAAE,CAAC,CAC/DI,IAAI,CAAC,MAAM,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;EACvC;EAEAC,mBAAmBA,CAACC,MAA+B,EAAE;IACjDpC,YAAY,CAACqC,8BAA8B,CACvC,IAAI,CAAC9B,iBAAiB,EACtB,IAAI,CAACY,MAAM,CAACU,EAAE,EACdO,MACJ,CAAC;EACL;EAEAF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACJ,UAAU,GAAG,SAAS;IAC3B,IAAI,CAACE,iBAAiB,GAAG,SAAS;IAClC,IAAI,CAACJ,IAAI,GAAG,IAAI;EACpB;AACJ"}