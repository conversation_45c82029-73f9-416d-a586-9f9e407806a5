import { EventEmitter } from 'events';

interface AgoraConfig {
  appId: string;
}

interface JoinChannelOptions {
  channelName: string;
  role: number;
  uid: number;
}

class EnhancedMockAgoraService extends EventEmitter {
  private appId: string = '';
  private isInitialized: boolean = false;
  private currentChannel: string | null = null;
  private currentRole: number | null = null;
  private currentUid: number | null = null;
  private isMuted: boolean = false;
  private cameraEnabled: boolean = true;
  private frontCamera: boolean = true;
  private isStreaming: boolean = false;

  constructor() {
    super();
    console.log('EnhancedMockAgoraService: Constructor called');
  }

  async initialize(config: AgoraConfig): Promise<void> {
    console.log('EnhancedMockAgoraService: Initializing with config', config);
    this.appId = config.appId;
    this.isInitialized = true;
    
    // Simulate initialization delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    console.log(`EnhancedMockAgoraService: Initialized with appId: ${this.appId}`);
  }

  async joinChannel(options: JoinChannelOptions): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Agora service not initialized');
    }

    console.log('EnhancedMockAgoraService: Joining channel', options);
    
    this.currentChannel = options.channelName;
    this.currentRole = options.role;
    this.currentUid = options.uid;

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Emit join success
    this.emit('onJoinChannelSuccess', {
      channel: options.channelName,
      uid: options.uid,
      elapsed: 1000
    });

    console.log(`EnhancedMockAgoraService: Successfully joined channel ${options.channelName}`);

    // If we're a broadcaster, start streaming
    if (options.role === 1) { // ClientRoleBroadcaster
      this.startBroadcasting();
    } else {
      // If we're audience, simulate finding a broadcaster
      this.simulateRemoteUser();
    }
  }

  private startBroadcasting(): void {
    console.log('EnhancedMockAgoraService: Starting broadcasting');
    this.isStreaming = true;
    
    // Simulate camera initialization
    setTimeout(() => {
      console.log('EnhancedMockAgoraService: Camera initialized');
      this.emit('onLocalVideoStateChanged', {
        state: 1, // Capturing
        error: 0
      });
    }, 500);
  }

  private simulateRemoteUser(): void {
    // Simulate a remote user joining after a delay
    setTimeout(() => {
      const remoteUid = 12345;
      console.log(`EnhancedMockAgoraService: Remote user ${remoteUid} joined`);
      
      this.emit('onUserJoined', {
        uid: remoteUid,
        elapsed: 2000
      });

      // Simulate remote video
      setTimeout(() => {
        this.emit('onRemoteVideoStateChanged', {
          uid: remoteUid,
          state: 2, // Decoding
          reason: 6, // RemoteVideoStateReasonRemoteUnmuted
          elapsed: 2500
        });
      }, 500);
    }, 2000);
  }

  async leaveChannel(): Promise<void> {
    console.log('EnhancedMockAgoraService: Leaving channel');
    
    if (this.currentChannel) {
      // Emit leave event
      this.emit('onLeaveChannel', {
        channel: this.currentChannel,
        stats: {
          duration: 30000,
          txBytes: 1024000,
          rxBytes: 2048000
        }
      });

      // If there was a remote user, simulate them leaving
      if (this.currentRole === 2) { // Audience
        this.emit('onUserOffline', {
          uid: 12345,
          reason: 1 // UserOfflineReasonQuit
        });
      }
    }

    this.currentChannel = null;
    this.currentRole = null;
    this.currentUid = null;
    this.isStreaming = false;

    console.log('EnhancedMockAgoraService: Left channel successfully');
  }

  async muteLocalAudioStream(muted: boolean): Promise<void> {
    console.log(`EnhancedMockAgoraService: ${muted ? 'Muting' : 'Unmuting'} local audio`);
    this.isMuted = muted;
    
    this.emit('onLocalAudioStateChanged', {
      state: muted ? 0 : 1, // Stopped : Recording
      error: 0
    });
  }

  async enableLocalVideo(enabled: boolean): Promise<void> {
    console.log(`EnhancedMockAgoraService: ${enabled ? 'Enabling' : 'Disabling'} local video`);
    this.cameraEnabled = enabled;
    
    this.emit('onLocalVideoStateChanged', {
      state: enabled ? 1 : 0, // Capturing : Stopped
      error: 0
    });
  }

  async switchCamera(): Promise<void> {
    console.log('EnhancedMockAgoraService: Switching camera');
    this.frontCamera = !this.frontCamera;
    
    // Simulate camera switch delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    console.log(`EnhancedMockAgoraService: Switched to ${this.frontCamera ? 'front' : 'back'} camera`);
  }

  async setClientRole(role: number): Promise<void> {
    console.log(`EnhancedMockAgoraService: Setting client role to ${role}`);
    this.currentRole = role;
    
    this.emit('onClientRoleChanged', {
      oldRole: this.currentRole,
      newRole: role
    });
  }

  // Event listener management
  addListener(event: string, listener: (...args: any[]) => void): void {
    console.log(`EnhancedMockAgoraService: Added listener for ${event}`);
    super.addListener(event, listener);
  }

  removeListener(event: string, listener: (...args: any[]) => void): void {
    console.log(`EnhancedMockAgoraService: Removed listener for ${event}`);
    super.removeListener(event, listener);
  }

  removeAllListeners(event?: string): void {
    console.log(`EnhancedMockAgoraService: Removed all listeners${event ? ` for ${event}` : ''}`);
    super.removeAllListeners(event);
  }

  // Getters for current state
  getCurrentChannel(): string | null {
    return this.currentChannel;
  }

  getCurrentRole(): number | null {
    return this.currentRole;
  }

  getCurrentUid(): number | null {
    return this.currentUid;
  }

  isMutedState(): boolean {
    return this.isMuted;
  }

  isCameraEnabled(): boolean {
    return this.cameraEnabled;
  }

  isFrontCamera(): boolean {
    return this.frontCamera;
  }

  isCurrentlyStreaming(): boolean {
    return this.isStreaming;
  }

  // Simulate network quality
  simulateNetworkQuality(): void {
    if (this.currentChannel) {
      setInterval(() => {
        this.emit('onNetworkQuality', {
          uid: this.currentUid,
          txQuality: Math.floor(Math.random() * 6) + 1, // 1-6
          rxQuality: Math.floor(Math.random() * 6) + 1  // 1-6
        });
      }, 5000);
    }
  }

  // Simulate real-time stats
  simulateRtcStats(): void {
    if (this.currentChannel) {
      setInterval(() => {
        this.emit('onRtcStats', {
          duration: Date.now() % 1000000,
          txBytes: Math.floor(Math.random() * 1000000),
          rxBytes: Math.floor(Math.random() * 2000000),
          txKBitRate: Math.floor(Math.random() * 500),
          rxKBitRate: Math.floor(Math.random() * 1000),
          users: this.currentRole === 1 ? 1 : 2
        });
      }, 3000);
    }
  }
}

export default EnhancedMockAgoraService;
