# ninja log v5
1	1670	7759272532395959	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/.trae/more/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o	68255148280ac022
132	2129	7759272536952239	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o	b9a4e0c5de300530
45	4704	7759272562670913	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o	6907fa56ebfb34d7
85	1684	7759272532589175	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/.trae/more/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o	ea5577ed06bb8053
157	5362	7759272569291984	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o	16d0070a0b800a1c
150	2908	7759272544762270	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o	d154a813971588e9
13	1867	7759272534346108	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/.trae/more/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o	c71b89fcf9b952cf
125	5360	7759272569256855	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o	eda503f23c825e8b
106	5112	7759272566764761	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o	391a0999c1b8e5dc
65	2565	7759272541371713	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o	3bfc6bddda3e7455
18	2032	7759272535879786	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o	aca1de75cbc7f996
99	5286	7759272568535117	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o	d74d165e4614b462
138	4804	7759272563625947	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o	183eefb20917cc0
112	4805	7759272563641607	CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o	4326b59ea5d718f3
31	4941	7759272565047579	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o	68b1393daeb30a8
58	4949	7759272565123793	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o	e2225574ad3dc4a8
8	5127	7759272566956156	CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o	4f1055cab935312d
78	5187	7759272567487039	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o	374dc0b2ca629ff3
72	6083	7759272576462761	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o	d0b5e1a484131d29
52	5249	7759272568150551	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o	f00e69682fab9a1e
38	5387	7759272569547869	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIInteropModuleRegistry.cpp.o	6b8c0c8f4cb770e2
6083	6182	7759272577476956	../../../../build/intermediates/cxx/Debug/71356r30/obj/arm64-v8a/libexpo-modules-core.so	7085dda32fcc7268
25	5401	7759272569673635	CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o	81229a10d192968d
91	5619	7759272571874318	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o	8dbb8204291cb238
144	5630	7759272571976663	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o	6a45944b74dbd6ce
118	5789	7759272573524395	CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o	486cf31aa5f3535d
0	8	0	clean	17a56fa2c58de3da
0	5	0	clean	17a56fa2c58de3da
