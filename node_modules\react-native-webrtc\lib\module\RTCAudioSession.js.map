{"version": 3, "names": ["NativeModules", "Platform", "WebRTCModule", "RTCAudioSession", "audioSessionDidActivate", "OS", "audioSessionDidDeactivate"], "sources": ["RTCAudioSession.ts"], "sourcesContent": ["import { NativeModules, Platform } from 'react-native';\n\nconst { WebRTCModule } = NativeModules;\n\nexport default class RTCAudioSession {\n    /**\n     * To be called when CallKit activates the audio session.\n     */\n    static audioSessionDidActivate() {\n        // Only valid for iOS\n        if (Platform.OS === 'ios') {\n            WebRTCModule.audioSessionDidActivate();\n        }\n    }\n\n    /**\n     * To be called when CallKit deactivates the audio session.\n     */\n    static audioSessionDidDeactivate() {\n        // Only valid for iOS\n        if (Platform.OS === 'ios') {\n            WebRTCModule.audioSessionDidDeactivate();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,QAAQ,QAAQ,cAAc;AAEtD,MAAM;EAAEC;AAAa,CAAC,GAAGF,aAAa;AAEtC,eAAe,MAAMG,eAAe,CAAC;EACjC;AACJ;AACA;EACI,OAAOC,uBAAuBA,CAAA,EAAG;IAC7B;IACA,IAAIH,QAAQ,CAACI,EAAE,KAAK,KAAK,EAAE;MACvBH,YAAY,CAACE,uBAAuB,CAAC,CAAC;IAC1C;EACJ;;EAEA;AACJ;AACA;EACI,OAAOE,yBAAyBA,CAAA,EAAG;IAC/B;IACA,IAAIL,QAAQ,CAACI,EAAE,KAAK,KAAK,EAAE;MACvBH,YAAY,CAACI,yBAAyB,CAAC,CAAC;IAC5C;EACJ;AACJ"}