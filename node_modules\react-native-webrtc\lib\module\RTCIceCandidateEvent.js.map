{"version": 3, "names": ["Event", "RTCIceCandidateEvent", "constructor", "type", "eventInitDict", "_eventInitDict$candid", "_defineProperty", "candidate"], "sources": ["RTCIceCandidateEvent.ts"], "sourcesContent": ["import { Event } from 'event-target-shim/index';\n\nimport type RTCIceCandidate from './RTCIceCandidate';\n\ntype RTC_ICECANDIDATE_EVENTS = 'icecandidate' | 'icecandidateerror'\n\ninterface IRTCDataChannelEventInitDict extends Event.EventInit {\n    candidate: RTCIceCandidate | null\n}\n\n/**\n * @eventClass\n * This event is fired whenever the icecandidate related RTC_EVENTS changed.\n * @type {RTCIceCandidateEvent} for icecandidate related.\n * @param {RTC_ICECANDIDATE_EVENTS} type - The type of event.\n * @param {IRTCDataChannelEventInitDict} eventInitDict - The event init properties.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/RTCPeerConnection#events MDN} for details.\n */\nexport default class RTCIceCandidateEvent<TEventType extends RTC_ICECANDIDATE_EVENTS> extends Event<TEventType> {\n    /** @eventProperty */\n    candidate: RTCIceCandidate | null;\n    constructor(type: TEventType, eventInitDict: IRTCDataChannelEventInitDict) {\n        super(type, eventInitDict);\n        this.candidate = eventInitDict?.candidate ?? null;\n    }\n}\n"], "mappings": ";AAAA,SAASA,KAAK,QAAQ,yBAAyB;AAU/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMC,oBAAoB,SAAqDD,KAAK,CAAa;EAC5G;;EAEAE,WAAWA,CAACC,IAAgB,EAAEC,aAA2C,EAAE;IAAA,IAAAC,qBAAA;IACvE,KAAK,CAACF,IAAI,EAAEC,aAAa,CAAC;IAACE,eAAA;IAC3B,IAAI,CAACC,SAAS,IAAAF,qBAAA,GAAGD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEG,SAAS,cAAAF,qBAAA,cAAAA,qBAAA,GAAI,IAAI;EACrD;AACJ"}