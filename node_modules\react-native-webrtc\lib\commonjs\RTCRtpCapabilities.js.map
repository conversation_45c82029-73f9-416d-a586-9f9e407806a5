{"version": 3, "names": ["RTCRtpCapabilities", "constructor", "codecs", "_defineProperty", "_codecs", "Object", "freeze", "exports", "default"], "sources": ["RTCRtpCapabilities.ts"], "sourcesContent": ["import RTCRtpCodecCapability from './RTCRtpCodecCapability';\n\n/**\n * @brief represents codec capabilities for senders and receivers.\n */\nexport default class RTCRtpCapabilities {\n    _codecs: RTCRtpCodecCapability[] = [];\n    constructor(codecs: RTCRtpCodecCapability[]) {\n        this._codecs = codecs;\n        Object.freeze(this);\n    }\n\n    get codecs() {\n        return this._codecs;\n    }\n}\n"], "mappings": ";;;;;;;AAEA;AACA;AACA;AACe,MAAMA,kBAAkB,CAAC;EAEpCC,WAAWA,CAACC,MAA+B,EAAE;IAAAC,eAAA,kBADV,EAAE;IAEjC,IAAI,CAACC,OAAO,GAAGF,MAAM;IACrBG,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACvB;EAEA,IAAIJ,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACE,OAAO;EACvB;AACJ;AAACG,OAAA,CAAAC,OAAA,GAAAR,kBAAA"}