# Supabase Configuration
SUPABASE_URL=https://opgyuyeuczddftaqkvdt.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9wZ3l1eWV1Y3pkZGZ0YXFrdmR0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI2NjA0MzQsImV4cCI6MjA1ODIzNjQzNH0.s7cPyvnAe32_YkB3a2bFifid3eIgkeLTllmvgZ96wQ8

# Agora Configuration
AGORA_APP_ID=********************************
AGORA_APP_CERTIFICATE=********************************

# Force real streaming (set to 'true' to use real Agora SDK even in Expo Go)
EXPO_PUBLIC_FORCE_REAL_STREAMING=false

# Use enhanced mock with camera (set to 'false' to use basic mock)
EXPO_PUBLIC_USE_ENHANCED_MOCK=true

# API URLs
SUPABASE_FUNCTIONS_URL=https://opgyuyeuczddftaqkvdt.supabase.co/functions/v1

# AI API Keys
DEEPSEEK_API_KEY=sk-********************************
KIMI_API_KEY=sk-or-v1-480c17119bed28308f3a72a61bf6fb8a687feabdcf8c60a78fd9acb41b6479f1s
KIMI_K2_API_KEY=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

# Ollama Configuration
OLLAMA_BASE_URL=http://127.0.0.1:11434
OLLAMA_API_URL=http://localhost:11434/api
OLLAMA_CHAT_URL=http://localhost:11434/api/chat