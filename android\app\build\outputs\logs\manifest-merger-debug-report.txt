-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:1:1-32:12
INJECTED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:1:1-32:12
INJECTED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:1:1-32:12
INJECTED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:1:1-32:12
MERGED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:1:1-32:12
MERGED from [com.facebook.flipper:flipper-network-plugin:0.182.0] C:\Users\<USER>\.gradle\caches\transforms-3\07e9c9a1db1f9f480478e58b8ab65d27\transformed\jetified-flipper-network-plugin-0.182.0\AndroidManifest.xml:8:1-15:12
MERGED from [com.facebook.flipper:flipper-fresco-plugin:0.182.0] C:\Users\<USER>\.gradle\caches\transforms-3\aef701c1b4379c6acc207d855444584c\transformed\jetified-flipper-fresco-plugin-0.182.0\AndroidManifest.xml:8:1-15:12
MERGED from [com.facebook.flipper:flipper:0.182.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd18304b20e6fdaf3e3425e5d35bd51e\transformed\jetified-flipper-0.182.0\AndroidManifest.xml:8:1-18:12
MERGED from [:expo] C:\Users\<USER>\.trae\more\node_modules\expo\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:react-native-permissions] C:\Users\<USER>\.trae\more\node_modules\react-native-permissions\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-application] C:\Users\<USER>\.trae\more\node_modules\expo-application\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-constants] C:\Users\<USER>\.trae\more\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-33:12
MERGED from [:expo-font] C:\Users\<USER>\.trae\more\node_modules\expo-font\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-keep-awake] C:\Users\<USER>\.trae\more\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-secure-store] C:\Users\<USER>\.trae\more\node_modules\expo-secure-store\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-18:12
MERGED from [com.facebook.react:react-android:0.72.10] C:\Users\<USER>\.gradle\caches\transforms-3\ed667b43ae46cb0af977d67989c9eadf\transformed\jetified-react-android-0.72.10-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:flipper:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa6508b02746eef3b58248afd68d2249\transformed\jetified-flipper-2.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:stetho:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\c9c4351a603f74990420edf13814483b\transformed\jetified-stetho-2.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:fresco:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\6916a4bbd9cac5cd52d40e372ec229db\transformed\jetified-fresco-2.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\f15bec81ce8958a721e68c9727768fe7\transformed\jetified-imagepipeline-okhttp3-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:animated-gif:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\491ab11cdc6f40996107fd55a8c24518\transformed\jetified-animated-gif-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:webpsupport:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a941b36865489bcc9ec1464ea40ecea\transformed\jetified-webpsupport-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.react:hermes-android:0.72.10] C:\Users\<USER>\.gradle\caches\transforms-3\5f996f3f5bf12d5a11aa2c1c4b8e266f\transformed\jetified-hermes-android-0.72.10-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\bb0cd3f206943031efb6ed20ab3386da\transformed\jetified-fragment-ktx-1.5.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\808043cb4cf2ad49d6936bcf600e8cf8\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.appcompat:appcompat:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\f7c5e03387f63f3788dbd5294d085d57\transformed\appcompat-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\07ad9d47cf3432f2bd69708b35894f17\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\d289b368d1c76f56212ad7fb41afa591\transformed\fragment-1.5.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\a3dd254264d8bd294a3c0b06f550a215\transformed\jetified-activity-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\c513b4875f8efe5b4c6585ca9f292fb3\transformed\jetified-activity-ktx-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\3fe3eeca0055649b8a8e25c3243a726d\transformed\jetified-appcompat-resources-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\218a52ab81d718d16ea6c9defe0a6b78\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c16de79b864adfe8d65782d96dac77d3\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af2408613e8bb6c12f75327d8b83848\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8d20c1b9c73a6ff51cfecd7f0a5912d\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\da16e6e2c3ade7cee5d810da4d9bdd0e\transformed\jetified-emoji2-views-helper-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f247157e7ddbdaefa22790775dc73d8a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf55d77c61a0b6a7a3181a42669ac3de\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\40a68710ab125457959c55d49934019d\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e9a204d336c633d95646e80dc3eba364\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b00d4a6a6215b61f4786c0934938958\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1c3587e5042482ca1212af2adf9c8c7\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\66c91c181cf71b1dbaabb147afdc945f\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\37237e168ff21f74c2fa7449816f191f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f9ae64e9e90e3635b8868f24320d38a3\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba4bd8e57b283323e27897aa7114628\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9e680dbaa0842d78fb3d305a20874afa\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a3d72f67a47d870cfdd7b87dc29306ff\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\03067c3924ebed008dc3406848106115\transformed\jetified-core-ktx-1.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c77580c485e6189d662dee139241c30c\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5ea14cbe6648367a1a132ad366b227d4\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\916eed05ad19469c544f3b428e0db3f3\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80eb95a753d48716874adf5ae7f6e73d\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\33515c94ab0dd71e7cd5cdadd943ba7f\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\409f2c5d163608b218e12928a2d1aad1\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\01b98716bd18db7c75d98a3148ec3aaa\transformed\core-1.8.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9fbca55ab621f16ef933b265a8c5d154\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8a7cb78e3ecc70292ebedd83a40b6f7a\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\56cf9b4b0ca11af36195c4dd104b579d\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebcf50a8c36edaad61408fd0a53bc20d\transformed\jetified-tracing-ktx-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\transforms-3\e19d276ce1ea70e5c412ccdf76d1119f\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:1:1-3:12
MERGED from [com.facebook.fbjni:fbjni:0.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8b6cc7607ffe5936bd42bb311e7e5848\transformed\jetified-fbjni-0.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:animated-base:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6cd8b5df66c3e088da8b805e8293dd25\transformed\jetified-animated-base-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:animated-drawable:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ccfc720418f310dfeac01965ca9b7e92\transformed\jetified-animated-drawable-2.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:drawee:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4beecdf7ece4b29549030e66145e4634\transformed\jetified-drawee-2.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:nativeimagefilters:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2ef20c82aa47d0149b51aaea8745de1\transformed\jetified-nativeimagefilters-2.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-native:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9335dce207f81f37d20b8056d4ab49d\transformed\jetified-memory-type-native-2.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-java:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c4f9c6b340d6d3969c77e91f44bc0ad\transformed\jetified-memory-type-java-2.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-native:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3b9da79e7939f92ecc98e0c4e592c6d\transformed\jetified-imagepipeline-native-2.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:soloader:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a2945b079ef65cef1a563d36abc7d02\transformed\jetified-soloader-2.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\4201aca3a97f634c9516e0bda615b0b2\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:2:1-17:12
MERGED from [com.facebook.fresco:memory-type-ashmem:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a2c82ce58b8ed0c18664f93277090bb\transformed\jetified-memory-type-ashmem-2.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\358c9adc86384bf96e038dba215a739e\transformed\jetified-imagepipeline-2.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad51de9742597e5670121b0f908286ad\transformed\jetified-nativeimagetranscoder-2.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-base:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a77659e9a3d4112a5982131e8680591\transformed\jetified-imagepipeline-base-2.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f43eadb6adc642952e4e5d28e2c2e9f8\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2660846b8c17c3774a74d166e48ca0f3\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\95d1533f87ae9a952a8deeb52e685a76\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:middleware:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c6cc36eb6303153f19b9248c318f74b\transformed\jetified-middleware-2.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:ui-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6cefae1e4d383732331803f8ce10a6c\transformed\jetified-ui-common-2.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:fbcore:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\f6f8d43a0f12c31c6bb4c5bed01eac0b\transformed\jetified-fbcore-2.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18238cc9d0a1a29df562c165e728975b\transformed\sqlite-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86a26b8c8e62ac0dd349e3c70f1c1a7a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf6623ade03b74ea81b50d2a711defd0\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d021db3fe608572a43ddd014c15cd1d3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\196ae1d304e1a7021c1c6c936e888ea6\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7bb7b4733d841c11da2c439984e06dc\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4802b04b2389f3f7d4022101c9ebfac6\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\017a69326d647c824a4c9c4124330dd0\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.agora.rtc:full-sdk:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\eeecda4bf35182e5447bd9322e36cf26\transformed\jetified-full-sdk-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:2:1-21:12
MERGED from [io.agora.rtc:iris-rtc:4.5.2-build.1] C:\Users\<USER>\.gradle\caches\transforms-3\977330be3d06d4e517554317a28b511b\transformed\jetified-iris-rtc-4.5.2-build.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\75c6c7e98755245c38048373f05ae40b\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:2:1-13:12
MERGED from [io.agora.rtc:full-rtc-basic:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\8afb5d148d56c8f90cd67c79e724d951\transformed\jetified-full-rtc-basic-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:ains:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\eae9ab6f4437299fbbedca0d6d307075\transformed\jetified-ains-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:ains-ll:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\1d3874b1b7be03563c80fbd376f72077\transformed\jetified-ains-ll-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:audio-beauty:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\be7022e4b92559ac4554aa3b0116b33e\transformed\jetified-audio-beauty-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:clear-vision:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\36f46438d076f90584f30a81c2f11433\transformed\jetified-clear-vision-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-content-inspect:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\7a3296c552c101399f559c9610266ff1\transformed\jetified-full-content-inspect-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:screen-capture:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\698758565e3a81e9469725ca41172231\transformed\jetified-screen-capture-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-virtual-background:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\9e70661ba3240f023a9f8ad67c68e796\transformed\jetified-full-virtual-background-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:spatial-audio:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\f7c3c1a00c8f990764761ffc2f56fb7e\transformed\jetified-spatial-audio-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:aiaec:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\d4f92428583aa08795b140f0b8a39193\transformed\jetified-aiaec-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:aiaec-ll:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\eb299c535f5615c58711e9a22aed9f1e\transformed\jetified-aiaec-ll-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-vqa:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\0835ec7e8d0f4b5abd550cad07588cc2\transformed\jetified-full-vqa-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-face-detect:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\3b4e50795acbe650a71a0dc304b02da5\transformed\jetified-full-face-detect-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-face-capture:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\9107a790cdcc42e3fc1e953029f0fd2b\transformed\jetified-full-face-capture-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-voice-drive:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\407f54c3b8af7d534d4b5301847821f0\transformed\jetified-full-voice-drive-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-video-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\0b4a2ba5094a8d47dee9d8ce4b8328ff\transformed\jetified-full-video-codec-enc-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-video-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\eaf0a6d4fbd047364a61c7599676630c\transformed\jetified-full-video-codec-dec-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-video-av1-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\708155913131d36c174ca16731d05d16\transformed\jetified-full-video-av1-codec-enc-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-video-av1-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\a69d63b0376604f95fced90eb394629a\transformed\jetified-full-video-av1-codec-dec-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\df688c0498547c9b2bf53333229e6b91\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [io.agora.infra:aosl:1.2.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\f3f3000af211b98e49cc281c7ad9ba9c\transformed\jetified-aosl-1.2.13.1\AndroidManifest.xml:2:1-11:12
INJECTED from C:\Users\<USER>\.trae\more\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\.trae\more\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\.trae\more\android\app\src\debug\AndroidManifest.xml:1:1-7:12
	package
		INJECTED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:1:1-32:12
		INJECTED from C:\Users\<USER>\.trae\more\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:1:1-32:12
		INJECTED from C:\Users\<USER>\.trae\more\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\debug\AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:1:1-32:12
		INJECTED from C:\Users\<USER>\.trae\more\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:3:3-64
MERGED from [com.facebook.flipper:flipper:0.182.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd18304b20e6fdaf3e3425e5d35bd51e\transformed\jetified-flipper-0.182.0\AndroidManifest.xml:15:5-67
MERGED from [com.facebook.flipper:flipper:0.182.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd18304b20e6fdaf3e3425e5d35bd51e\transformed\jetified-flipper-0.182.0\AndroidManifest.xml:15:5-67
MERGED from [:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-67
MERGED from [:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-67
MERGED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-67
MERGED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-67
	android:name
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:3:20-62
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:5:3-75
MERGED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:5:3-75
MERGED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:5:3-75
	android:name
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:5:20-73
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:6:3-63
	android:name
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:6:20-61
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:8:3-77
MERGED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-80
MERGED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-80
	android:name
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:8:20-75
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:9:3-78
MERGED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-81
MERGED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-81
	android:name
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:9:20-76
queries
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:12:3-19:13
MERGED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:5-18:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:14:5-18:14
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:15:7-59
	android:name
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:15:15-56
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:16:7-68
	android:name
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:16:17-65
data
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:17:7-38
	android:scheme
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:17:13-35
application
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:3-31:17
MERGED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:3-31:17
MERGED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:3-31:17
MERGED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-16:19
MERGED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\37237e168ff21f74c2fa7449816f191f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\37237e168ff21f74c2fa7449816f191f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\01b98716bd18db7c75d98a3148ec3aaa\transformed\core-1.8.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\01b98716bd18db7c75d98a3148ec3aaa\transformed\core-1.8.0\AndroidManifest.xml:24:5-89
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\4201aca3a97f634c9516e0bda615b0b2\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\4201aca3a97f634c9516e0bda615b0b2\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:11:5-15:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2660846b8c17c3774a74d166e48ca0f3\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2660846b8c17c3774a74d166e48ca0f3\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d021db3fe608572a43ddd014c15cd1d3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d021db3fe608572a43ddd014c15cd1d3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:10:5-20:19
MERGED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:10:5-20:19
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\75c6c7e98755245c38048373f05ae40b\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\75c6c7e98755245c38048373f05ae40b\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:11:5-20
	android:appComponentFactory
		ADDED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\01b98716bd18db7c75d98a3148ec3aaa\transformed\core-1.8.0\AndroidManifest.xml:24:18-86
	android:label
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:48-80
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:48-80
	tools:ignore
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\debug\AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:116-161
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:116-161
	tools:targetApi
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\debug\AndroidManifest.xml:6:54-74
	android:icon
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:81-115
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:81-115
	android:allowBackup
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:162-189
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:162-189
	android:theme
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:190-221
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:190-221
	tools:replace
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\debug\AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\debug\AndroidManifest.xml:6:18-53
	android:name
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:16-47
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:16-47
meta-data#expo.modules.updates.EXPO_UPDATE_URL
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:22:5-103
	android:value
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:22:68-101
	android:name
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:22:16-67
meta-data#expo.modules.updates.EXPO_SDK_VERSION
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:23:5-112
	android:value
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:23:69-110
	android:name
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:23:16-68
activity#com.streamingapp.MainActivity
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:24:5-29:16
	android:label
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:24:44-76
	android:launchMode
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:24:155-186
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:24:187-229
	android:exported
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:24:276-299
	android:configChanges
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:24:77-154
	android:theme
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:24:230-275
	android:name
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:24:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:25:7-28:23
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:26:9-60
	android:name
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:26:17-58
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:27:9-68
	android:name
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:27:19-66
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:30:5-106
	android:exported
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:30:80-104
	android:name
		ADDED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:30:15-79
uses-sdk
INJECTED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml
MERGED from [com.facebook.flipper:flipper-network-plugin:0.182.0] C:\Users\<USER>\.gradle\caches\transforms-3\07e9c9a1db1f9f480478e58b8ab65d27\transformed\jetified-flipper-network-plugin-0.182.0\AndroidManifest.xml:11:5-13:41
MERGED from [com.facebook.flipper:flipper-network-plugin:0.182.0] C:\Users\<USER>\.gradle\caches\transforms-3\07e9c9a1db1f9f480478e58b8ab65d27\transformed\jetified-flipper-network-plugin-0.182.0\AndroidManifest.xml:11:5-13:41
MERGED from [com.facebook.flipper:flipper-fresco-plugin:0.182.0] C:\Users\<USER>\.gradle\caches\transforms-3\aef701c1b4379c6acc207d855444584c\transformed\jetified-flipper-fresco-plugin-0.182.0\AndroidManifest.xml:11:5-13:41
MERGED from [com.facebook.flipper:flipper-fresco-plugin:0.182.0] C:\Users\<USER>\.gradle\caches\transforms-3\aef701c1b4379c6acc207d855444584c\transformed\jetified-flipper-fresco-plugin-0.182.0\AndroidManifest.xml:11:5-13:41
MERGED from [com.facebook.flipper:flipper:0.182.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd18304b20e6fdaf3e3425e5d35bd51e\transformed\jetified-flipper-0.182.0\AndroidManifest.xml:11:5-13:41
MERGED from [com.facebook.flipper:flipper:0.182.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd18304b20e6fdaf3e3425e5d35bd51e\transformed\jetified-flipper-0.182.0\AndroidManifest.xml:11:5-13:41
MERGED from [:expo] C:\Users\<USER>\.trae\more\node_modules\expo\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo] C:\Users\<USER>\.trae\more\node_modules\expo\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-permissions] C:\Users\<USER>\.trae\more\node_modules\react-native-permissions\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-permissions] C:\Users\<USER>\.trae\more\node_modules\react-native-permissions\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-application] C:\Users\<USER>\.trae\more\node_modules\expo-application\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-application] C:\Users\<USER>\.trae\more\node_modules\expo-application\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\Users\<USER>\.trae\more\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\Users\<USER>\.trae\more\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:expo-font] C:\Users\<USER>\.trae\more\node_modules\expo-font\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-font] C:\Users\<USER>\.trae\more\node_modules\expo-font\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-keep-awake] C:\Users\<USER>\.trae\more\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-keep-awake] C:\Users\<USER>\.trae\more\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-secure-store] C:\Users\<USER>\.trae\more\node_modules\expo-secure-store\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-secure-store] C:\Users\<USER>\.trae\more\node_modules\expo-secure-store\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [com.facebook.react:react-android:0.72.10] C:\Users\<USER>\.gradle\caches\transforms-3\ed667b43ae46cb0af977d67989c9eadf\transformed\jetified-react-android-0.72.10-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.72.10] C:\Users\<USER>\.gradle\caches\transforms-3\ed667b43ae46cb0af977d67989c9eadf\transformed\jetified-react-android-0.72.10-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:flipper:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa6508b02746eef3b58248afd68d2249\transformed\jetified-flipper-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:flipper:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa6508b02746eef3b58248afd68d2249\transformed\jetified-flipper-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:stetho:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\c9c4351a603f74990420edf13814483b\transformed\jetified-stetho-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:stetho:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\c9c4351a603f74990420edf13814483b\transformed\jetified-stetho-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fresco:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\6916a4bbd9cac5cd52d40e372ec229db\transformed\jetified-fresco-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fresco:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\6916a4bbd9cac5cd52d40e372ec229db\transformed\jetified-fresco-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\f15bec81ce8958a721e68c9727768fe7\transformed\jetified-imagepipeline-okhttp3-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\f15bec81ce8958a721e68c9727768fe7\transformed\jetified-imagepipeline-okhttp3-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-gif:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\491ab11cdc6f40996107fd55a8c24518\transformed\jetified-animated-gif-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-gif:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\491ab11cdc6f40996107fd55a8c24518\transformed\jetified-animated-gif-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:webpsupport:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a941b36865489bcc9ec1464ea40ecea\transformed\jetified-webpsupport-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:webpsupport:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a941b36865489bcc9ec1464ea40ecea\transformed\jetified-webpsupport-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.react:hermes-android:0.72.10] C:\Users\<USER>\.gradle\caches\transforms-3\5f996f3f5bf12d5a11aa2c1c4b8e266f\transformed\jetified-hermes-android-0.72.10-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.72.10] C:\Users\<USER>\.gradle\caches\transforms-3\5f996f3f5bf12d5a11aa2c1c4b8e266f\transformed\jetified-hermes-android-0.72.10-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\bb0cd3f206943031efb6ed20ab3386da\transformed\jetified-fragment-ktx-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\bb0cd3f206943031efb6ed20ab3386da\transformed\jetified-fragment-ktx-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\808043cb4cf2ad49d6936bcf600e8cf8\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\808043cb4cf2ad49d6936bcf600e8cf8\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\f7c5e03387f63f3788dbd5294d085d57\transformed\appcompat-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\f7c5e03387f63f3788dbd5294d085d57\transformed\appcompat-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\07ad9d47cf3432f2bd69708b35894f17\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\07ad9d47cf3432f2bd69708b35894f17\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\d289b368d1c76f56212ad7fb41afa591\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\d289b368d1c76f56212ad7fb41afa591\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\a3dd254264d8bd294a3c0b06f550a215\transformed\jetified-activity-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\a3dd254264d8bd294a3c0b06f550a215\transformed\jetified-activity-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\c513b4875f8efe5b4c6585ca9f292fb3\transformed\jetified-activity-ktx-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\c513b4875f8efe5b4c6585ca9f292fb3\transformed\jetified-activity-ktx-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\3fe3eeca0055649b8a8e25c3243a726d\transformed\jetified-appcompat-resources-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\3fe3eeca0055649b8a8e25c3243a726d\transformed\jetified-appcompat-resources-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\218a52ab81d718d16ea6c9defe0a6b78\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\218a52ab81d718d16ea6c9defe0a6b78\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c16de79b864adfe8d65782d96dac77d3\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c16de79b864adfe8d65782d96dac77d3\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af2408613e8bb6c12f75327d8b83848\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6af2408613e8bb6c12f75327d8b83848\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8d20c1b9c73a6ff51cfecd7f0a5912d\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8d20c1b9c73a6ff51cfecd7f0a5912d\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\da16e6e2c3ade7cee5d810da4d9bdd0e\transformed\jetified-emoji2-views-helper-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\da16e6e2c3ade7cee5d810da4d9bdd0e\transformed\jetified-emoji2-views-helper-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f247157e7ddbdaefa22790775dc73d8a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f247157e7ddbdaefa22790775dc73d8a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf55d77c61a0b6a7a3181a42669ac3de\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf55d77c61a0b6a7a3181a42669ac3de\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\40a68710ab125457959c55d49934019d\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\40a68710ab125457959c55d49934019d\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e9a204d336c633d95646e80dc3eba364\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e9a204d336c633d95646e80dc3eba364\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b00d4a6a6215b61f4786c0934938958\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b00d4a6a6215b61f4786c0934938958\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1c3587e5042482ca1212af2adf9c8c7\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e1c3587e5042482ca1212af2adf9c8c7\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\66c91c181cf71b1dbaabb147afdc945f\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\66c91c181cf71b1dbaabb147afdc945f\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\37237e168ff21f74c2fa7449816f191f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\37237e168ff21f74c2fa7449816f191f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f9ae64e9e90e3635b8868f24320d38a3\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f9ae64e9e90e3635b8868f24320d38a3\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba4bd8e57b283323e27897aa7114628\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\dba4bd8e57b283323e27897aa7114628\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9e680dbaa0842d78fb3d305a20874afa\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9e680dbaa0842d78fb3d305a20874afa\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a3d72f67a47d870cfdd7b87dc29306ff\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a3d72f67a47d870cfdd7b87dc29306ff\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\03067c3924ebed008dc3406848106115\transformed\jetified-core-ktx-1.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\03067c3924ebed008dc3406848106115\transformed\jetified-core-ktx-1.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c77580c485e6189d662dee139241c30c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c77580c485e6189d662dee139241c30c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5ea14cbe6648367a1a132ad366b227d4\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5ea14cbe6648367a1a132ad366b227d4\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\916eed05ad19469c544f3b428e0db3f3\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\916eed05ad19469c544f3b428e0db3f3\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80eb95a753d48716874adf5ae7f6e73d\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80eb95a753d48716874adf5ae7f6e73d\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\33515c94ab0dd71e7cd5cdadd943ba7f\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\33515c94ab0dd71e7cd5cdadd943ba7f\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\409f2c5d163608b218e12928a2d1aad1\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\409f2c5d163608b218e12928a2d1aad1\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\01b98716bd18db7c75d98a3148ec3aaa\transformed\core-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\01b98716bd18db7c75d98a3148ec3aaa\transformed\core-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9fbca55ab621f16ef933b265a8c5d154\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9fbca55ab621f16ef933b265a8c5d154\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8a7cb78e3ecc70292ebedd83a40b6f7a\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8a7cb78e3ecc70292ebedd83a40b6f7a\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\56cf9b4b0ca11af36195c4dd104b579d\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\56cf9b4b0ca11af36195c4dd104b579d\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebcf50a8c36edaad61408fd0a53bc20d\transformed\jetified-tracing-ktx-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing-ktx:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebcf50a8c36edaad61408fd0a53bc20d\transformed\jetified-tracing-ktx-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\transforms-3\e19d276ce1ea70e5c412ccdf76d1119f\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:2:2-70
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\transforms-3\e19d276ce1ea70e5c412ccdf76d1119f\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:2:2-70
MERGED from [com.facebook.fbjni:fbjni:0.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8b6cc7607ffe5936bd42bb311e7e5848\transformed\jetified-fbjni-0.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fbjni:fbjni:0.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8b6cc7607ffe5936bd42bb311e7e5848\transformed\jetified-fbjni-0.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-base:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6cd8b5df66c3e088da8b805e8293dd25\transformed\jetified-animated-base-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-base:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6cd8b5df66c3e088da8b805e8293dd25\transformed\jetified-animated-base-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-drawable:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ccfc720418f310dfeac01965ca9b7e92\transformed\jetified-animated-drawable-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-drawable:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ccfc720418f310dfeac01965ca9b7e92\transformed\jetified-animated-drawable-2.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:drawee:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4beecdf7ece4b29549030e66145e4634\transformed\jetified-drawee-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:drawee:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4beecdf7ece4b29549030e66145e4634\transformed\jetified-drawee-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagefilters:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2ef20c82aa47d0149b51aaea8745de1\transformed\jetified-nativeimagefilters-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagefilters:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2ef20c82aa47d0149b51aaea8745de1\transformed\jetified-nativeimagefilters-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-native:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9335dce207f81f37d20b8056d4ab49d\transformed\jetified-memory-type-native-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-native:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9335dce207f81f37d20b8056d4ab49d\transformed\jetified-memory-type-native-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-java:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c4f9c6b340d6d3969c77e91f44bc0ad\transformed\jetified-memory-type-java-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-java:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c4f9c6b340d6d3969c77e91f44bc0ad\transformed\jetified-memory-type-java-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-native:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3b9da79e7939f92ecc98e0c4e592c6d\transformed\jetified-imagepipeline-native-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-native:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3b9da79e7939f92ecc98e0c4e592c6d\transformed\jetified-imagepipeline-native-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:soloader:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a2945b079ef65cef1a563d36abc7d02\transformed\jetified-soloader-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:soloader:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a2945b079ef65cef1a563d36abc7d02\transformed\jetified-soloader-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\4201aca3a97f634c9516e0bda615b0b2\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\4201aca3a97f634c9516e0bda615b0b2\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.fresco:memory-type-ashmem:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a2c82ce58b8ed0c18664f93277090bb\transformed\jetified-memory-type-ashmem-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-ashmem:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a2c82ce58b8ed0c18664f93277090bb\transformed\jetified-memory-type-ashmem-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\358c9adc86384bf96e038dba215a739e\transformed\jetified-imagepipeline-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\358c9adc86384bf96e038dba215a739e\transformed\jetified-imagepipeline-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad51de9742597e5670121b0f908286ad\transformed\jetified-nativeimagetranscoder-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ad51de9742597e5670121b0f908286ad\transformed\jetified-nativeimagetranscoder-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-base:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a77659e9a3d4112a5982131e8680591\transformed\jetified-imagepipeline-base-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-base:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a77659e9a3d4112a5982131e8680591\transformed\jetified-imagepipeline-base-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f43eadb6adc642952e4e5d28e2c2e9f8\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f43eadb6adc642952e4e5d28e2c2e9f8\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2660846b8c17c3774a74d166e48ca0f3\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2660846b8c17c3774a74d166e48ca0f3\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\95d1533f87ae9a952a8deeb52e685a76\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\95d1533f87ae9a952a8deeb52e685a76\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:middleware:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c6cc36eb6303153f19b9248c318f74b\transformed\jetified-middleware-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:middleware:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c6cc36eb6303153f19b9248c318f74b\transformed\jetified-middleware-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:ui-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6cefae1e4d383732331803f8ce10a6c\transformed\jetified-ui-common-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:ui-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6cefae1e4d383732331803f8ce10a6c\transformed\jetified-ui-common-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fbcore:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\f6f8d43a0f12c31c6bb4c5bed01eac0b\transformed\jetified-fbcore-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fbcore:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\f6f8d43a0f12c31c6bb4c5bed01eac0b\transformed\jetified-fbcore-2.6.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18238cc9d0a1a29df562c165e728975b\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\18238cc9d0a1a29df562c165e728975b\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86a26b8c8e62ac0dd349e3c70f1c1a7a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86a26b8c8e62ac0dd349e3c70f1c1a7a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf6623ade03b74ea81b50d2a711defd0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf6623ade03b74ea81b50d2a711defd0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d021db3fe608572a43ddd014c15cd1d3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\d021db3fe608572a43ddd014c15cd1d3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\196ae1d304e1a7021c1c6c936e888ea6\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\196ae1d304e1a7021c1c6c936e888ea6\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7bb7b4733d841c11da2c439984e06dc\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7bb7b4733d841c11da2c439984e06dc\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4802b04b2389f3f7d4022101c9ebfac6\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4802b04b2389f3f7d4022101c9ebfac6\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\017a69326d647c824a4c9c4124330dd0\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\017a69326d647c824a4c9c4124330dd0\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [io.agora.rtc:full-sdk:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\eeecda4bf35182e5447bd9322e36cf26\transformed\jetified-full-sdk-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-sdk:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\eeecda4bf35182e5447bd9322e36cf26\transformed\jetified-full-sdk-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:5:5-73
MERGED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:5:5-73
MERGED from [io.agora.rtc:iris-rtc:4.5.2-build.1] C:\Users\<USER>\.gradle\caches\transforms-3\977330be3d06d4e517554317a28b511b\transformed\jetified-iris-rtc-4.5.2-build.1\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:iris-rtc:4.5.2-build.1] C:\Users\<USER>\.gradle\caches\transforms-3\977330be3d06d4e517554317a28b511b\transformed\jetified-iris-rtc-4.5.2-build.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\75c6c7e98755245c38048373f05ae40b\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\75c6c7e98755245c38048373f05ae40b\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:5:5-7:41
MERGED from [io.agora.rtc:full-rtc-basic:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\8afb5d148d56c8f90cd67c79e724d951\transformed\jetified-full-rtc-basic-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-rtc-basic:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\8afb5d148d56c8f90cd67c79e724d951\transformed\jetified-full-rtc-basic-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:ains:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\eae9ab6f4437299fbbedca0d6d307075\transformed\jetified-ains-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:ains:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\eae9ab6f4437299fbbedca0d6d307075\transformed\jetified-ains-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:ains-ll:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\1d3874b1b7be03563c80fbd376f72077\transformed\jetified-ains-ll-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:ains-ll:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\1d3874b1b7be03563c80fbd376f72077\transformed\jetified-ains-ll-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:audio-beauty:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\be7022e4b92559ac4554aa3b0116b33e\transformed\jetified-audio-beauty-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:audio-beauty:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\be7022e4b92559ac4554aa3b0116b33e\transformed\jetified-audio-beauty-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:clear-vision:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\36f46438d076f90584f30a81c2f11433\transformed\jetified-clear-vision-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:clear-vision:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\36f46438d076f90584f30a81c2f11433\transformed\jetified-clear-vision-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-content-inspect:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\7a3296c552c101399f559c9610266ff1\transformed\jetified-full-content-inspect-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-content-inspect:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\7a3296c552c101399f559c9610266ff1\transformed\jetified-full-content-inspect-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:screen-capture:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\698758565e3a81e9469725ca41172231\transformed\jetified-screen-capture-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:screen-capture:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\698758565e3a81e9469725ca41172231\transformed\jetified-screen-capture-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-virtual-background:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\9e70661ba3240f023a9f8ad67c68e796\transformed\jetified-full-virtual-background-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-virtual-background:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\9e70661ba3240f023a9f8ad67c68e796\transformed\jetified-full-virtual-background-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:spatial-audio:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\f7c3c1a00c8f990764761ffc2f56fb7e\transformed\jetified-spatial-audio-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:spatial-audio:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\f7c3c1a00c8f990764761ffc2f56fb7e\transformed\jetified-spatial-audio-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:aiaec:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\d4f92428583aa08795b140f0b8a39193\transformed\jetified-aiaec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:aiaec:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\d4f92428583aa08795b140f0b8a39193\transformed\jetified-aiaec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:aiaec-ll:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\eb299c535f5615c58711e9a22aed9f1e\transformed\jetified-aiaec-ll-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:aiaec-ll:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\eb299c535f5615c58711e9a22aed9f1e\transformed\jetified-aiaec-ll-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-vqa:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\0835ec7e8d0f4b5abd550cad07588cc2\transformed\jetified-full-vqa-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-vqa:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\0835ec7e8d0f4b5abd550cad07588cc2\transformed\jetified-full-vqa-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-face-detect:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\3b4e50795acbe650a71a0dc304b02da5\transformed\jetified-full-face-detect-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-face-detect:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\3b4e50795acbe650a71a0dc304b02da5\transformed\jetified-full-face-detect-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-face-capture:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\9107a790cdcc42e3fc1e953029f0fd2b\transformed\jetified-full-face-capture-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-face-capture:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\9107a790cdcc42e3fc1e953029f0fd2b\transformed\jetified-full-face-capture-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-voice-drive:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\407f54c3b8af7d534d4b5301847821f0\transformed\jetified-full-voice-drive-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-voice-drive:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\407f54c3b8af7d534d4b5301847821f0\transformed\jetified-full-voice-drive-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\0b4a2ba5094a8d47dee9d8ce4b8328ff\transformed\jetified-full-video-codec-enc-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\0b4a2ba5094a8d47dee9d8ce4b8328ff\transformed\jetified-full-video-codec-enc-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\eaf0a6d4fbd047364a61c7599676630c\transformed\jetified-full-video-codec-dec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\eaf0a6d4fbd047364a61c7599676630c\transformed\jetified-full-video-codec-dec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-av1-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\708155913131d36c174ca16731d05d16\transformed\jetified-full-video-av1-codec-enc-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-av1-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\708155913131d36c174ca16731d05d16\transformed\jetified-full-video-av1-codec-enc-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-av1-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\a69d63b0376604f95fced90eb394629a\transformed\jetified-full-video-av1-codec-dec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-av1-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\a69d63b0376604f95fced90eb394629a\transformed\jetified-full-video-av1-codec-dec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\df688c0498547c9b2bf53333229e6b91\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\df688c0498547c9b2bf53333229e6b91\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [io.agora.infra:aosl:1.2.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\f3f3000af211b98e49cc281c7ad9ba9c\transformed\jetified-aosl-1.2.13.1\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.infra:aosl:1.2.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\f3f3000af211b98e49cc281c7ad9ba9c\transformed\jetified-aosl-1.2.13.1\AndroidManifest.xml:7:5-9:41
INJECTED from C:\Users\<USER>\.trae\more\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\.trae\more\android\app\src\debug\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\.trae\more\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\.trae\more\android\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [com.facebook.flipper:flipper:0.182.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd18304b20e6fdaf3e3425e5d35bd51e\transformed\jetified-flipper-0.182.0\AndroidManifest.xml:16:5-76
MERGED from [:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-76
MERGED from [:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-76
	android:name
		ADDED from [com.facebook.flipper:flipper:0.182.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd18304b20e6fdaf3e3425e5d35bd51e\transformed\jetified-flipper-0.182.0\AndroidManifest.xml:16:22-73
uses-permission#android.permission.CAMERA
ADDED from [:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-65
	android:name
		ADDED from [:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from [:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-71
	android:name
		ADDED from [:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-68
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from [:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-80
	android:name
		ADDED from [:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:22-77
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:5-79
	android:name
		ADDED from [:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:22-76
uses-permission#android.permission.BLUETOOTH
ADDED from [:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:5-68
	android:name
		ADDED from [:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:22-65
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-79
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:26:13-48
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-74
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-29:70
	android:resource
		ADDED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-67
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:28:17-67
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\4201aca3a97f634c9516e0bda615b0b2\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\4201aca3a97f634c9516e0bda615b0b2\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\4201aca3a97f634c9516e0bda615b0b2\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-57
uses-permission#android.permission.USE_BIOMETRIC
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\808043cb4cf2ad49d6936bcf600e8cf8\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\808043cb4cf2ad49d6936bcf600e8cf8\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\808043cb4cf2ad49d6936bcf600e8cf8\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\808043cb4cf2ad49d6936bcf600e8cf8\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\37237e168ff21f74c2fa7449816f191f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\37237e168ff21f74c2fa7449816f191f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2660846b8c17c3774a74d166e48ca0f3\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2660846b8c17c3774a74d166e48ca0f3\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:30:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:28:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:29:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:27:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:32:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\37237e168ff21f74c2fa7449816f191f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\37237e168ff21f74c2fa7449816f191f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\37237e168ff21f74c2fa7449816f191f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:7:5-77
	android:name
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:7:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:8:5-94
	android:name
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:8:22-91
activity#io.agora.rtc2.extensions.MediaProjectionMgr$LocalScreenCaptureAssistantActivity
ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:11:9-14:63
	android:screenOrientation
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:13:13-52
	android:configChanges
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:12:13-59
	android:theme
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:14:13-61
	android:name
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:11:19-89
service#io.agora.rtc2.extensions.MediaProjectionMgr$LocalScreenSharingService
ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:16:9-19:19
	android:foregroundServiceType
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:18:13-60
	android:name
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:17:13-73
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\75c6c7e98755245c38048373f05ae40b\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\75c6c7e98755245c38048373f05ae40b\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
