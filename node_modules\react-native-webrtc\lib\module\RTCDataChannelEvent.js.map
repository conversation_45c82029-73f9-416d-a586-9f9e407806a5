{"version": 3, "names": ["Event", "RTCDataChannelEvent", "constructor", "type", "eventInitDict", "_defineProperty", "channel"], "sources": ["RTCDataChannelEvent.ts"], "sourcesContent": ["import { Event } from 'event-target-shim/index';\n\nimport type RTCDataChannel from './RTCDataChannel';\n\ntype DATA_CHANNEL_EVENTS =  'open'| 'message'| 'bufferedamountlow'| 'closing'| 'close'| 'error' | 'datachannel';\n\ninterface IRTCDataChannelEventInitDict extends Event.EventInit {\n    channel: RTCDataChannel;\n}\n\n\n/**\n * @eventClass\n * This event is fired whenever the RTCDataChannel has changed in any way.\n * @param {DATA_CHANNEL_EVENTS} type - The type of event.\n * @param {IRTCDataChannelEventInitDict} eventInitDict - The event init properties.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/RTCDataChannel#events MDN} for details.\n */\nexport default class RTCDataChannelEvent<\nTEventType extends DATA_CHANNEL_EVENTS\n> extends Event<TEventType> {\n    /** @eventProperty */\n    channel: RTCDataChannel;\n    constructor(type: TEventType, eventInitDict: IRTCDataChannelEventInitDict) {\n        super(type, eventInitDict);\n        this.channel = eventInitDict.channel;\n    }\n}\n"], "mappings": ";AAAA,SAASA,KAAK,QAAQ,yBAAyB;AAW/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMC,mBAAmB,SAE9BD,KAAK,CAAa;EACxB;;EAEAE,WAAWA,CAACC,IAAgB,EAAEC,aAA2C,EAAE;IACvE,KAAK,CAACD,IAAI,EAAEC,aAAa,CAAC;IAACC,eAAA;IAC3B,IAAI,CAACC,OAAO,GAAGF,aAAa,CAACE,OAAO;EACxC;AACJ"}