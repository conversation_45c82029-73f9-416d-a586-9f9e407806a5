{"version": 3, "names": ["_RTCRtcpParameters", "_interopRequireDefault", "require", "_RTCRtpCodecParameters", "_RTCRtpHeaderExtension", "_RTCUtil", "obj", "__esModule", "default", "_defineProperty", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "RTCRtpParameters", "constructor", "init", "codec", "codecs", "push", "RTCRtpCodecParameters", "ext", "headerExtensions", "RTCRtpHeaderExtension", "rtcp", "RTCRtcpParameters", "toJSON", "map", "c", "deepClone", "he", "exports"], "sources": ["RTCRtpParameters.ts"], "sourcesContent": ["import RTCRtcpParameters, { RTCRtcpParametersInit } from './RTCRtcpParameters';\nimport RTCRtpCodecParameters, { RTCRtpCodecParametersInit } from './RTCRtpCodecParameters';\nimport RTCRtpHeaderExtension, { RTCRtpHeaderExtensionInit } from './RTCRtpHeaderExtension';\nimport { deepClone } from './RTCUtil';\n\n\nexport interface RTCRtpParametersInit {\n    codecs: RTCRtpCodecParametersInit[],\n    headerExtensions: RTCRtpHeaderExtensionInit[],\n    rtcp: RTCRtcpParametersInit\n}\n\nexport default class RTCRtpParameters {\n    codecs: (RTCRtpCodecParameters | RTCRtpCodecParametersInit)[] = [];\n    readonly headerExtensions: RTCRtpHeaderExtension[] = [];\n    readonly rtcp: RTCRtcpParameters;\n\n    constructor(init: RTCRtpParametersInit) {\n        for (const codec of init.codecs) {\n            this.codecs.push(new RTCRtpCodecParameters(codec));\n        }\n\n        for (const ext of init.headerExtensions) {\n            this.headerExtensions.push(new RTCRtpHeaderExtension(ext));\n        }\n\n        this.rtcp = new RTCRtcpParameters(init.rtcp);\n    }\n\n    toJSON() {\n        return {\n            codecs: this.codecs.map(c => deepClone(c)),\n            headerExtensions: this.headerExtensions.map(he => deepClone(he)),\n            rtcp: deepClone(this.rtcp)\n        };\n    }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,sBAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAAsC,SAAAD,uBAAAK,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,gBAAAH,GAAA,EAAAI,GAAA,EAAAC,KAAA,QAAAD,GAAA,IAAAJ,GAAA,IAAAM,MAAA,CAAAC,cAAA,CAAAP,GAAA,EAAAI,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAG,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAV,GAAA,CAAAI,GAAA,IAAAC,KAAA,WAAAL,GAAA;AASvB,MAAMW,gBAAgB,CAAC;EAKlCC,WAAWA,CAACC,IAA0B,EAAE;IAAAV,eAAA,iBAJwB,EAAE;IAAAA,eAAA,2BACb,EAAE;IAAAA,eAAA;IAInD,KAAK,MAAMW,KAAK,IAAID,IAAI,CAACE,MAAM,EAAE;MAC7B,IAAI,CAACA,MAAM,CAACC,IAAI,CAAC,IAAIC,8BAAqB,CAACH,KAAK,CAAC,CAAC;IACtD;IAEA,KAAK,MAAMI,GAAG,IAAIL,IAAI,CAACM,gBAAgB,EAAE;MACrC,IAAI,CAACA,gBAAgB,CAACH,IAAI,CAAC,IAAII,8BAAqB,CAACF,GAAG,CAAC,CAAC;IAC9D;IAEA,IAAI,CAACG,IAAI,GAAG,IAAIC,0BAAiB,CAACT,IAAI,CAACQ,IAAI,CAAC;EAChD;EAEAE,MAAMA,CAAA,EAAG;IACL,OAAO;MACHR,MAAM,EAAE,IAAI,CAACA,MAAM,CAACS,GAAG,CAACC,CAAC,IAAI,IAAAC,kBAAS,EAACD,CAAC,CAAC,CAAC;MAC1CN,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAACK,GAAG,CAACG,EAAE,IAAI,IAAAD,kBAAS,EAACC,EAAE,CAAC,CAAC;MAChEN,IAAI,EAAE,IAAAK,kBAAS,EAAC,IAAI,CAACL,IAAI;IAC7B,CAAC;EACL;AACJ;AAACO,OAAA,CAAA1B,OAAA,GAAAS,gBAAA"}