import { Platform } from 'react-native';
import Constants from 'expo-constants';

// Check if we're running in Expo Go
const isExpoGo = Constants.appOwnership === 'expo';

// Allow manual override for testing real streaming
const forceRealStreaming = process.env.EXPO_PUBLIC_FORCE_REAL_STREAMING === 'true';

// Use enhanced mock for better streaming simulation
const useEnhancedMock = process.env.EXPO_PUBLIC_USE_ENHANCED_MOCK !== 'false';

let AgoraService: any;
let RtcSurfaceView: any;
let ClientRoleType: any;

if (isExpoGo && !forceRealStreaming) {
  // Use mock implementations in Expo Go
  if (useEnhancedMock) {
    console.log('Using Enhanced Mock Agora Service for Expo Go');
    AgoraService = require('./EnhancedMockAgoraService').default;
    RtcSurfaceView = require('../components/EnhancedMockRtcSurfaceView').default;
  } else {
    console.log('Using Basic Mock Agora Service for Expo Go');
    AgoraService = require('./MockAgoraService').default;
    RtcSurfaceView = require('../components/MockRtcSurfaceView').default;
  }
  ClientRoleType = {
    ClientRoleBroadcaster: 1,
    ClientRoleAudience: 2,
  };
} else {
  // Use real Agora SDK in development builds or when forced
  console.log(forceRealStreaming ? 'Using Real Agora Service (Forced)' : 'Using Real Agora Service for Development Build');
  try {
    AgoraService = require('./AgoraService').default;
    const agoraModule = require('react-native-agora');
    RtcSurfaceView = agoraModule.RtcSurfaceView;
    ClientRoleType = agoraModule.ClientRoleType;
  } catch (error) {
    console.warn('Agora SDK not available, falling back to mock');
    AgoraService = require('./MockAgoraService').default;
    RtcSurfaceView = require('../components/MockRtcSurfaceView').default;
    ClientRoleType = {
      ClientRoleBroadcaster: 1,
      ClientRoleAudience: 2,
    };
  }
}

export { AgoraService, RtcSurfaceView, ClientRoleType };
