{"version": 3, "names": ["EventTarget", "defineEventAttribute", "NativeModules", "getDisplayMedia", "getUserMedia", "WebRTCModule", "MediaDevices", "enumerateDevices", "Promise", "resolve", "constraints", "proto", "prototype"], "sources": ["MediaDevices.ts"], "sourcesContent": ["import { EventTarget, Event, defineEventAttribute } from 'event-target-shim/index';\nimport { NativeModules } from 'react-native';\n\nimport getDisplayMedia from './getDisplayMedia';\nimport getUserMedia, { Constraints } from './getUserMedia';\n\nconst { WebRTCModule } = NativeModules;\n\ntype MediaDevicesEventMap = {\n    devicechange: Event<'devicechange'>\n}\n\nclass MediaDevices extends EventTarget<MediaDevicesEventMap> {\n    /**\n     * W3C \"Media Capture and Streams\" compatible {@code enumerateDevices}\n     * implementation.\n     */\n    enumerateDevices() {\n        return new Promise(resolve => WebRTCModule.enumerateDevices(resolve));\n    }\n\n    /**\n     * W3C \"Screen Capture\" compatible {@code getDisplayMedia} implementation.\n     * See: https://w3c.github.io/mediacapture-screen-share/\n     *\n     * @returns {Promise}\n     */\n    getDisplayMedia() {\n        return getDisplayMedia();\n    }\n\n    /**\n     * W3C \"Media Capture and Streams\" compatible {@code getUserMedia}\n     * implementation.\n     * See: https://www.w3.org/TR/mediacapture-streams/#dom-mediadevices-enumeratedevices\n     *\n     * @param {*} constraints\n     * @returns {Promise}\n     */\n    getUserMedia(constraints: Constraints) {\n        return getUserMedia(constraints);\n    }\n}\n\n/**\n * Define the `onxxx` event handlers.\n */\nconst proto = MediaDevices.prototype;\n\ndefineEventAttribute(proto, 'devicechange');\n\n\nexport default new MediaDevices();\n"], "mappings": "AAAA,SAASA,WAAW,EAASC,oBAAoB,QAAQ,yBAAyB;AAClF,SAASC,aAAa,QAAQ,cAAc;AAE5C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,YAAY,MAAuB,gBAAgB;AAE1D,MAAM;EAAEC;AAAa,CAAC,GAAGH,aAAa;AAMtC,MAAMI,YAAY,SAASN,WAAW,CAAuB;EACzD;AACJ;AACA;AACA;EACIO,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAIJ,YAAY,CAACE,gBAAgB,CAACE,OAAO,CAAC,CAAC;EACzE;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACIN,eAAeA,CAAA,EAAG;IACd,OAAOA,eAAe,CAAC,CAAC;EAC5B;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,YAAYA,CAACM,WAAwB,EAAE;IACnC,OAAON,YAAY,CAACM,WAAW,CAAC;EACpC;AACJ;;AAEA;AACA;AACA;AACA,MAAMC,KAAK,GAAGL,YAAY,CAACM,SAAS;AAEpCX,oBAAoB,CAACU,KAAK,EAAE,cAAc,CAAC;AAG3C,eAAe,IAAIL,YAAY,CAAC,CAAC"}