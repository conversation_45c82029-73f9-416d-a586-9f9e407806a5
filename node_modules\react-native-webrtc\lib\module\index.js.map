{"version": 3, "names": ["NativeModules", "Platform", "WebRTCModule", "Error", "OS", "setupNativeEvents", "<PERSON><PERSON>", "mediaDevices", "MediaStream", "MediaStreamTrack", "MediaStreamTrackEvent", "permissions", "RTCAudioSession", "RTCErrorEvent", "RTCIceCandidate", "RTCPIPView", "startIOSPIP", "stopIOSPIP", "RTCPeerConnection", "RTCRtpReceiver", "RTCRtpSender", "RTCRtpTransceiver", "RTCSessionDescription", "RTCView", "ScreenCapturePickerView", "enable", "ROOT_PREFIX", "registerGlobals", "global", "navigator", "getUserMedia", "bind", "getDisplayMedia", "enumerateDevices"], "sources": ["index.ts"], "sourcesContent": ["import { NativeModules, Platform } from 'react-native';\nconst { WebRTCModule } = NativeModules;\n\nif (WebRTCModule === null) {\n    throw new Error(`WebRTC native module not found.\\n${Platform.OS === 'ios' ?\n        'Try executing the \"pod install\" command inside your projects ios folder.' :\n        'Try executing the \"npm install\" command inside your projects folder.'\n    }`);\n}\n\nimport { setupNativeEvents } from './EventEmitter';\nimport Logger from './Logger';\nimport mediaDevices from './MediaDevices';\nimport MediaStream from './MediaStream';\nimport MediaStreamTrack, { type MediaTrackSettings } from './MediaStreamTrack';\nimport MediaStreamTrackEvent from './MediaStreamTrackEvent';\nimport permissions from './Permissions';\nimport RTCAudioSession from './RTCAudioSession';\nimport RTCErrorEvent from './RTCErrorEvent';\nimport RTCIceCandidate from './RTCIceCandidate';\nimport RTCPIPView, { startIOSPIP, stopIOSPIP } from './RTCPIPView';\nimport RTCPeerConnection from './RTCPeerConnection';\nimport RTCRtpReceiver from './RTCRtpReceiver';\nimport RTCRtpSender from './RTCRtpSender';\nimport RTCRtpTransceiver from './RTCRtpTransceiver';\nimport RTCSessionDescription from './RTCSessionDescription';\nimport RTCView, { type RTCVideoViewProps, type RTCIOSPIPOptions } from './RTCView';\nimport ScreenCapturePickerView from './ScreenCapturePickerView';\n\nLogger.enable(`${Logger.ROOT_PREFIX}:*`);\n\n// Add listeners for the native events early, since they are added asynchronously.\nsetupNativeEvents();\n\nexport {\n    RTCIceCandidate,\n    RTCPeerConnection,\n    RTCSessionDescription,\n    RTCView,\n    RTCPIPView,\n    ScreenCapturePickerView,\n    RTCRtpTransceiver,\n    RTCRtpReceiver,\n    RTCRtpSender,\n    RTCErrorEvent,\n    RTCAudioSession,\n    MediaStream,\n    MediaStreamTrack,\n    type MediaTrackSettings,\n    type RTCVideoViewProps,\n    type RTCIOSPIPOptions,\n    mediaDevices,\n    permissions,\n    registerGlobals,\n    startIOSPIP,\n    stopIOSPIP,\n};\n\ndeclare const global: any;\n\nfunction registerGlobals(): void {\n    // Should not happen. React Native has a global navigator object.\n    if (typeof global.navigator !== 'object') {\n        throw new Error('navigator is not an object');\n    }\n\n    if (!global.navigator.mediaDevices) {\n        global.navigator.mediaDevices = {};\n    }\n\n    global.navigator.mediaDevices.getUserMedia = mediaDevices.getUserMedia.bind(mediaDevices);\n    global.navigator.mediaDevices.getDisplayMedia = mediaDevices.getDisplayMedia.bind(mediaDevices);\n    global.navigator.mediaDevices.enumerateDevices = mediaDevices.enumerateDevices.bind(mediaDevices);\n\n    global.RTCIceCandidate = RTCIceCandidate;\n    global.RTCPeerConnection = RTCPeerConnection;\n    global.RTCRtpReceiver = RTCRtpReceiver;\n    global.RTCRtpSender = RTCRtpReceiver;\n    global.RTCSessionDescription = RTCSessionDescription;\n    global.MediaStream = MediaStream;\n    global.MediaStreamTrack = MediaStreamTrack;\n    global.MediaStreamTrackEvent = MediaStreamTrackEvent;\n    global.RTCRtpTransceiver = RTCRtpTransceiver;\n    global.RTCRtpReceiver = RTCRtpReceiver;\n    global.RTCRtpSender = RTCRtpSender;\n    global.RTCErrorEvent = RTCErrorEvent;\n}\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,QAAQ,QAAQ,cAAc;AACtD,MAAM;EAAEC;AAAa,CAAC,GAAGF,aAAa;AAEtC,IAAIE,YAAY,KAAK,IAAI,EAAE;EACvB,MAAM,IAAIC,KAAK,CAAE,oCAAmCF,QAAQ,CAACG,EAAE,KAAK,KAAK,GACrE,0EAA0E,GAC1E,sEACH,EAAC,CAAC;AACP;AAEA,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,gBAAgB,MAAmC,oBAAoB;AAC9E,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,UAAU,IAAIC,WAAW,EAAEC,UAAU,QAAQ,cAAc;AAClE,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,OAAO,MAAyD,WAAW;AAClF,OAAOC,uBAAuB,MAAM,2BAA2B;AAE/DlB,MAAM,CAACmB,MAAM,CAAE,GAAEnB,MAAM,CAACoB,WAAY,IAAG,CAAC;;AAExC;AACArB,iBAAiB,CAAC,CAAC;AAEnB,SACIS,eAAe,EACfI,iBAAiB,EACjBI,qBAAqB,EACrBC,OAAO,EACPR,UAAU,EACVS,uBAAuB,EACvBH,iBAAiB,EACjBF,cAAc,EACdC,YAAY,EACZP,aAAa,EACbD,eAAe,EACfJ,WAAW,EACXC,gBAAgB,EAIhBF,YAAY,EACZI,WAAW,EACXgB,eAAe,EACfX,WAAW,EACXC,UAAU;AAKd,SAASU,eAAeA,CAAA,EAAS;EAC7B;EACA,IAAI,OAAOC,MAAM,CAACC,SAAS,KAAK,QAAQ,EAAE;IACtC,MAAM,IAAI1B,KAAK,CAAC,4BAA4B,CAAC;EACjD;EAEA,IAAI,CAACyB,MAAM,CAACC,SAAS,CAACtB,YAAY,EAAE;IAChCqB,MAAM,CAACC,SAAS,CAACtB,YAAY,GAAG,CAAC,CAAC;EACtC;EAEAqB,MAAM,CAACC,SAAS,CAACtB,YAAY,CAACuB,YAAY,GAAGvB,YAAY,CAACuB,YAAY,CAACC,IAAI,CAACxB,YAAY,CAAC;EACzFqB,MAAM,CAACC,SAAS,CAACtB,YAAY,CAACyB,eAAe,GAAGzB,YAAY,CAACyB,eAAe,CAACD,IAAI,CAACxB,YAAY,CAAC;EAC/FqB,MAAM,CAACC,SAAS,CAACtB,YAAY,CAAC0B,gBAAgB,GAAG1B,YAAY,CAAC0B,gBAAgB,CAACF,IAAI,CAACxB,YAAY,CAAC;EAEjGqB,MAAM,CAACd,eAAe,GAAGA,eAAe;EACxCc,MAAM,CAACV,iBAAiB,GAAGA,iBAAiB;EAC5CU,MAAM,CAACT,cAAc,GAAGA,cAAc;EACtCS,MAAM,CAACR,YAAY,GAAGD,cAAc;EACpCS,MAAM,CAACN,qBAAqB,GAAGA,qBAAqB;EACpDM,MAAM,CAACpB,WAAW,GAAGA,WAAW;EAChCoB,MAAM,CAACnB,gBAAgB,GAAGA,gBAAgB;EAC1CmB,MAAM,CAAClB,qBAAqB,GAAGA,qBAAqB;EACpDkB,MAAM,CAACP,iBAAiB,GAAGA,iBAAiB;EAC5CO,MAAM,CAACT,cAAc,GAAGA,cAAc;EACtCS,MAAM,CAACR,YAAY,GAAGA,YAAY;EAClCQ,MAAM,CAACf,aAAa,GAAGA,aAAa;AACxC"}