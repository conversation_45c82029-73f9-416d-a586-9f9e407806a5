<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="io.agora.infra:aosl:1.2.13.1" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\f3f3000af211b98e49cc281c7ad9ba9c\transformed\jetified-aosl-1.2.13.1\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\f3f3000af211b98e49cc281c7ad9ba9c\transformed\jetified-aosl-1.2.13.1\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-video-av1-codec-dec:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\a69d63b0376604f95fced90eb394629a\transformed\jetified-full-video-av1-codec-dec-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\a69d63b0376604f95fced90eb394629a\transformed\jetified-full-video-av1-codec-dec-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-video-av1-codec-enc:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\708155913131d36c174ca16731d05d16\transformed\jetified-full-video-av1-codec-enc-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\708155913131d36c174ca16731d05d16\transformed\jetified-full-video-av1-codec-enc-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-video-codec-dec:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\eaf0a6d4fbd047364a61c7599676630c\transformed\jetified-full-video-codec-dec-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\eaf0a6d4fbd047364a61c7599676630c\transformed\jetified-full-video-codec-dec-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-video-codec-enc:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\0b4a2ba5094a8d47dee9d8ce4b8328ff\transformed\jetified-full-video-codec-enc-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\0b4a2ba5094a8d47dee9d8ce4b8328ff\transformed\jetified-full-video-codec-enc-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-voice-drive:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\407f54c3b8af7d534d4b5301847821f0\transformed\jetified-full-voice-drive-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\407f54c3b8af7d534d4b5301847821f0\transformed\jetified-full-voice-drive-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-face-capture:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\9107a790cdcc42e3fc1e953029f0fd2b\transformed\jetified-full-face-capture-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\9107a790cdcc42e3fc1e953029f0fd2b\transformed\jetified-full-face-capture-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-face-detect:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\3b4e50795acbe650a71a0dc304b02da5\transformed\jetified-full-face-detect-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\3b4e50795acbe650a71a0dc304b02da5\transformed\jetified-full-face-detect-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-vqa:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\0835ec7e8d0f4b5abd550cad07588cc2\transformed\jetified-full-vqa-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\0835ec7e8d0f4b5abd550cad07588cc2\transformed\jetified-full-vqa-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:aiaec-ll:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\eb299c535f5615c58711e9a22aed9f1e\transformed\jetified-aiaec-ll-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\eb299c535f5615c58711e9a22aed9f1e\transformed\jetified-aiaec-ll-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:aiaec:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\d4f92428583aa08795b140f0b8a39193\transformed\jetified-aiaec-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\d4f92428583aa08795b140f0b8a39193\transformed\jetified-aiaec-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:spatial-audio:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\f7c3c1a00c8f990764761ffc2f56fb7e\transformed\jetified-spatial-audio-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\f7c3c1a00c8f990764761ffc2f56fb7e\transformed\jetified-spatial-audio-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-virtual-background:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\9e70661ba3240f023a9f8ad67c68e796\transformed\jetified-full-virtual-background-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\9e70661ba3240f023a9f8ad67c68e796\transformed\jetified-full-virtual-background-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:screen-capture:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\698758565e3a81e9469725ca41172231\transformed\jetified-screen-capture-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\698758565e3a81e9469725ca41172231\transformed\jetified-screen-capture-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-content-inspect:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\7a3296c552c101399f559c9610266ff1\transformed\jetified-full-content-inspect-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\7a3296c552c101399f559c9610266ff1\transformed\jetified-full-content-inspect-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:clear-vision:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\36f46438d076f90584f30a81c2f11433\transformed\jetified-clear-vision-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\36f46438d076f90584f30a81c2f11433\transformed\jetified-clear-vision-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:audio-beauty:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\be7022e4b92559ac4554aa3b0116b33e\transformed\jetified-audio-beauty-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\be7022e4b92559ac4554aa3b0116b33e\transformed\jetified-audio-beauty-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:ains-ll:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\1d3874b1b7be03563c80fbd376f72077\transformed\jetified-ains-ll-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\1d3874b1b7be03563c80fbd376f72077\transformed\jetified-ains-ll-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:ains:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\eae9ab6f4437299fbbedca0d6d307075\transformed\jetified-ains-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\eae9ab6f4437299fbbedca0d6d307075\transformed\jetified-ains-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-rtc-basic:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\8afb5d148d56c8f90cd67c79e724d951\transformed\jetified-full-rtc-basic-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\8afb5d148d56c8f90cd67c79e724d951\transformed\jetified-full-rtc-basic-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:iris-rtc:4.5.2-build.1" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\977330be3d06d4e517554317a28b511b\transformed\jetified-iris-rtc-4.5.2-build.1\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\977330be3d06d4e517554317a28b511b\transformed\jetified-iris-rtc-4.5.2-build.1\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-sdk:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\eeecda4bf35182e5447bd9322e36cf26\transformed\jetified-full-sdk-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\transforms-3\eeecda4bf35182e5447bd9322e36cf26\transformed\jetified-full-sdk-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":expo-secure-store" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.trae\more\node_modules\expo-secure-store\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":expo-keep-awake" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.trae\more\node_modules\expo-keep-awake\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":expo-font" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.trae\more\node_modules\expo-font\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":expo-file-system" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.trae\more\node_modules\expo-constants\android\build\intermediates\library_assets\debug\out"><file name="app.config" path="C:\Users\<USER>\.trae\more\node_modules\expo-constants\android\build\intermediates\library_assets\debug\out\app.config"/></source></dataSet><dataSet config=":expo-application" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.trae\more\node_modules\expo-application\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-permissions" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.trae\more\node_modules\react-native-permissions\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-agora" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.trae\more\node_modules\expo\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.trae\more\android\app\src\main\assets"/><source path="C:\Users\<USER>\.trae\more\android\app\build\intermediates\shader_assets\debug\out"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.trae\more\android\app\src\debug\assets"/></dataSet></merger>