-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Stream sessions table
CREATE TABLE IF NOT EXISTS stream_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    channel_name VA<PERSON>HAR(255) NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    end_time TIMESTAMP WITH TIME ZONE,
    duration INTEGER, -- in seconds
    viewers INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'ended', 'failed')),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Viewer sessions table
CREATE TABLE IF NOT EXISTS viewer_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    stream_id UUID NOT NULL REFERENCES stream_sessions(id) ON DELETE CASCADE,
    join_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    leave_time TIMESTAMP WITH TIME ZONE,
    duration INTEGER, -- in seconds
    ip_address INET,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    notifications BOOLEAN DEFAULT true,
    analytics BOOLEAN DEFAULT false,
    marketing BOOLEAN DEFAULT false,
    data_sharing BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Parental consent requests table
CREATE TABLE IF NOT EXISTS parental_consent_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    parent_email VARCHAR(255) NOT NULL,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'denied')),
    responded_at TIMESTAMP WITH TIME ZONE,
    response_note TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_stream_sessions_user_id ON stream_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_stream_sessions_status ON stream_sessions(status);
CREATE INDEX IF NOT EXISTS idx_stream_sessions_start_time ON stream_sessions(start_time);
CREATE INDEX IF NOT EXISTS idx_viewer_sessions_stream_id ON viewer_sessions(stream_id);
CREATE INDEX IF NOT EXISTS idx_viewer_sessions_user_id ON viewer_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_parental_consent_requests_user_id ON parental_consent_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_parental_consent_requests_status ON parental_consent_requests(status);

-- Row Level Security (RLS) policies
-- Stream sessions policies
ALTER TABLE stream_sessions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own streams" ON stream_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own streams" ON stream_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own streams" ON stream_sessions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own streams" ON stream_sessions
    FOR DELETE USING (auth.uid() = user_id);

-- Viewer sessions policies
ALTER TABLE viewer_sessions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own viewer sessions" ON viewer_sessions
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can create viewer sessions" ON viewer_sessions
    FOR INSERT WITH CHECK (true); -- Allow anonymous viewers

CREATE POLICY "Users can update their own viewer sessions" ON viewer_sessions
    FOR UPDATE USING (auth.uid() = user_id);

-- User preferences policies
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own preferences" ON user_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own preferences" ON user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own preferences" ON user_preferences
    FOR UPDATE USING (auth.uid() = user_id);

-- Parental consent policies
ALTER TABLE parental_consent_requests ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own consent requests" ON parental_consent_requests
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own consent requests" ON parental_consent_requests
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own consent requests" ON parental_consent_requests
    FOR UPDATE USING (auth.uid() = user_id);

-- Functions and triggers
-- Update timestamps automatically
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_stream_sessions_updated_at BEFORE UPDATE ON stream_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_viewer_sessions_updated_at BEFORE UPDATE ON viewer_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON user_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_parental_consent_requests_updated_at BEFORE UPDATE ON parental_consent_requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to increment viewer count
CREATE OR REPLACE FUNCTION increment_viewer_count(session_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE stream_sessions
    SET viewers = viewers + 1
    WHERE id = session_id;
END;
$$ LANGUAGE plpgsql;

-- Function to decrement viewer count
CREATE OR REPLACE FUNCTION decrement_viewer_count(session_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE stream_sessions
    SET viewers = GREATEST(viewers - 1, 0)
    WHERE id = session_id;
END;
$$ LANGUAGE plpgsql;

-- Views for analytics
CREATE OR REPLACE VIEW user_stream_analytics AS
SELECT
    user_id,
    COUNT(*) as total_streams,
    SUM(viewers) as total_viewers,
    SUM(COALESCE(duration, 0)) as total_duration,
    AVG(viewers) as average_viewers_per_stream,
    MAX(start_time) as last_stream_time
FROM stream_sessions
WHERE status = 'ended'
GROUP BY user_id;

CREATE OR REPLACE VIEW daily_stream_stats AS
SELECT
    DATE(start_time) as stream_date,
    COUNT(*) as total_streams,
    SUM(viewers) as total_viewers,
    SUM(COALESCE(duration, 0)) as total_duration,
    AVG(viewers) as average_viewers_per_stream
FROM stream_sessions
WHERE status = 'ended'
GROUP BY DATE(start_time)
ORDER BY stream_date DESC;