{"logs": [{"outputFile": "com.streamingapp-mergeDebugResources-61:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9a204d336c633d95646e80dc3eba364\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "322", "startColumns": "4", "startOffsets": "19555", "endColumns": "53", "endOffsets": "19604"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ed667b43ae46cb0af977d67989c9eadf\\transformed\\jetified-react-android-0.72.10-debug\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,80,84,88,91,95,99,102,105,106,107,116,123,130,133,136,139,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,163,226,277,331,390,438,487,536,584,633,691,740,781,825,868,922,1103,1176,1299,1395,1484,1592,1709,1829,1949,2051,2154,2265,2372,2475,2586,2755,2923,3040,3150,3265,3378,3534,3642,3755,3846,3957,4126,4224,4351,4476,4571,4678,4758,4834,4907,4994,5065,5136,5214,5294,5380,5464,5536,5618,5702,5779,5866,5951,6030,6105,6178,6267,6344,6422,6495,6573,6821,7069,7337,7522,7724,7930,8131,8320,8346,8381,8919,9337,9715,9892,10071,10254,10619", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,79,83,87,90,94,98,101,104,105,106,115,122,129,132,135,138,144,154", "endColumns": "62,62,50,53,58,47,48,48,47,48,57,48,40,43,42,53,47,72,122,95,88,107,116,119,119,101,102,110,106,102,110,168,167,116,109,114,112,155,107,112,90,110,168,97,126,124,94,106,79,75,72,86,70,70,77,79,85,83,71,81,83,76,86,84,78,74,72,88,76,77,72,77,10,10,12,12,10,10,12,12,25,34,10,10,10,10,10,12,12,10", "endOffsets": "158,221,272,326,385,433,482,531,579,628,686,735,776,820,863,917,965,1171,1294,1390,1479,1587,1704,1824,1944,2046,2149,2260,2367,2470,2581,2750,2918,3035,3145,3260,3373,3529,3637,3750,3841,3952,4121,4219,4346,4471,4566,4673,4753,4829,4902,4989,5060,5131,5209,5289,5375,5459,5531,5613,5697,5774,5861,5946,6025,6100,6173,6262,6339,6417,6490,6568,6816,7064,7332,7517,7719,7925,8126,8315,8341,8376,8914,9332,9710,9887,10066,10249,10614,11055"}, "to": {"startLines": "64,65,241,242,243,276,277,278,279,280,281,282,291,294,297,318,319,359,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,405,406,407,408,409,410,411,412,413,414,415,417,418,419,420,421,422,423,424,426,427,428,429,435,439,1531,1535,1538,1542,1766,1769,1850,1900,1901,1910,1917,1924,1927,1930,1933,2085", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3304,3367,15166,15217,15271,17240,17288,17337,17386,17434,17483,17541,18012,18123,18263,19350,19404,22035,22242,22365,22461,22550,22658,22775,22895,23015,23117,23220,23331,23438,23541,23652,23821,23989,24106,24216,24331,24444,24600,24708,24821,24912,25023,25192,25290,25417,25542,25637,25744,26907,26983,27056,27143,27214,27285,27363,27443,27529,27613,27685,27820,27904,27981,28068,28153,28232,28307,28380,28540,28617,28695,28768,29288,29536,99077,99345,99530,99732,115355,115556,123027,126215,126250,126788,127206,127584,127761,127940,128123,139317", "endLines": "64,65,241,242,243,276,277,278,279,280,281,282,291,294,297,318,319,359,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,405,406,407,408,409,410,411,412,413,414,415,417,418,419,420,421,422,423,424,426,427,428,429,438,442,1534,1537,1541,1545,1768,1771,1850,1900,1909,1916,1923,1926,1929,1932,1938,2094", "endColumns": "62,62,50,53,58,47,48,48,47,48,57,48,40,43,42,53,47,72,122,95,88,107,116,119,119,101,102,110,106,102,110,168,167,116,109,114,112,155,107,112,90,110,168,97,126,124,94,106,79,75,72,86,70,70,77,79,85,83,71,81,83,76,86,84,78,74,72,88,76,77,72,77,10,10,12,12,10,10,12,12,25,34,10,10,10,10,10,12,12,10", "endOffsets": "3362,3425,15212,15266,15325,17283,17332,17381,17429,17478,17536,17585,18048,18162,18301,19399,19447,22103,22360,22456,22545,22653,22770,22890,23010,23112,23215,23326,23433,23536,23647,23816,23984,24101,24211,24326,24439,24595,24703,24816,24907,25018,25187,25285,25412,25537,25632,25739,25819,26978,27051,27138,27209,27280,27358,27438,27524,27608,27680,27762,27899,27976,28063,28148,28227,28302,28375,28464,28612,28690,28763,28841,29531,29779,99340,99525,99727,99933,115551,115740,123048,126245,126783,127201,127579,127756,127935,128118,128483,139753"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\218a52ab81d718d16ea6c9defe0a6b78\\transformed\\jetified-autofill-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,19,20,27,32,37,44,53", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,934,994,1376,1656,1938,2322,2820", "endLines": "2,18,19,26,31,36,43,52,66", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "118,929,989,1371,1651,1933,2317,2815,3867"}, "to": {"startLines": "193,1884,2046,2047,2054,2059,2064,2071,2736", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11917,125404,137147,137207,137589,137869,138151,138535,160752", "endLines": "193,1899,2046,2053,2058,2063,2070,2079,2749", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "11980,126210,137202,137584,137864,138146,138530,139028,161333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a3dd254264d8bd294a3c0b06f550a215\\transformed\\jetified-activity-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "298,321", "startColumns": "4,4", "startOffsets": "18306,19495", "endColumns": "41,59", "endOffsets": "18343,19550"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9fbca55ab621f16ef933b265a8c5d154\\transformed\\lifecycle-runtime-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "320", "startColumns": "4", "startOffsets": "19452", "endColumns": "42", "endOffsets": "19490"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\01b98716bd18db7c75d98a3148ec3aaa\\transformed\\core-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,89,90,94,95,96,97,103,113,146,167,200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,344,407,477,545,617,687,748,822,895,956,1017,1079,1143,1205,1266,1334,1434,1494,1560,1633,1702,1759,1811,1873,1945,2021,2086,2145,2204,2264,2324,2384,2444,2504,2564,2624,2684,2744,2804,2863,2923,2983,3043,3103,3163,3223,3283,3343,3403,3463,3522,3582,3642,3701,3760,3819,3878,3937,3996,4031,4066,4121,4184,4239,4297,4355,4416,4479,4536,4587,4637,4698,4755,4821,4855,4890,4925,4995,5066,5183,5384,5494,5695,5824,5896,5963,6166,6467,8198,8879,9561", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,88,89,93,94,95,96,102,112,145,166,199,205", "endColumns": "59,71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,339,402,472,540,612,682,743,817,890,951,1012,1074,1138,1200,1261,1329,1429,1489,1555,1628,1697,1754,1806,1868,1940,2016,2081,2140,2199,2259,2319,2379,2439,2499,2559,2619,2679,2739,2799,2858,2918,2978,3038,3098,3158,3218,3278,3338,3398,3458,3517,3577,3637,3696,3755,3814,3873,3932,3991,4026,4061,4116,4179,4234,4292,4350,4411,4474,4531,4582,4632,4693,4750,4816,4850,4885,4920,4990,5061,5178,5379,5489,5690,5819,5891,5958,6161,6462,8193,8874,9556,9723"}, "to": {"startLines": "38,49,50,88,89,194,195,196,197,198,199,200,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,238,239,240,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,292,293,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,331,425,1828,1829,1834,1837,1842,2080,2081,2754,2788,2934,2967,3131,3164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1409,2128,2200,4783,4852,11985,12055,12123,12195,12265,12326,12400,13310,13371,13432,13494,13558,13620,13681,13749,13849,13909,13975,14048,14117,14174,14226,14953,15025,15101,15330,15389,15448,15508,15568,15628,15688,15748,15808,15868,15928,15988,16048,16107,16167,16227,16287,16347,16407,16467,16527,16587,16647,16707,16766,16826,16886,16945,17004,17063,17122,17181,18053,18088,18459,18514,18577,18632,18690,18748,18809,18872,18929,18980,19030,19091,19148,19214,19248,19283,20099,28469,121459,121576,121843,122136,122403,139033,139105,161473,163046,170003,171734,176585,177267", "endLines": "38,49,50,88,89,194,195,196,197,198,199,200,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,238,239,240,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,292,293,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,331,425,1828,1832,1834,1840,1842,2080,2081,2759,2797,2966,2987,3163,3169", "endColumns": "59,71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1464,2195,2283,4847,4910,12050,12118,12190,12260,12321,12395,12468,13366,13427,13489,13553,13615,13676,13744,13844,13904,13970,14043,14112,14169,14221,14283,15020,15096,15161,15384,15443,15503,15563,15623,15683,15743,15803,15863,15923,15983,16043,16102,16162,16222,16282,16342,16402,16462,16522,16582,16642,16702,16761,16821,16881,16940,16999,17058,17117,17176,17235,18083,18118,18509,18572,18627,18685,18743,18804,18867,18924,18975,19025,19086,19143,19209,19243,19278,19313,20164,28535,121571,121772,121948,122332,122527,139100,139167,161671,163342,171729,172410,177262,177429"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\33515c94ab0dd71e7cd5cdadd943ba7f\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "90,226,227,228,229,1833,1835,1836,1841,1843", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4915,14288,14341,14394,14447,121777,121953,122075,122337,122532", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "4999,14336,14389,14442,14495,121838,122070,122131,122398,122594"}}, {"source": "C:\\Users\\<USER>\\.trae\\more\\android\\app\\build\\generated\\res\\resValues\\debug\\values\\gradleResValues.xml", "from": {"startLines": "6,8", "startColumns": "4,4", "startOffsets": "159,265", "endColumns": "63,68", "endOffsets": "218,329"}, "to": {"startLines": "329,330", "startColumns": "4,4", "startOffsets": "19966,20030", "endColumns": "63,68", "endOffsets": "20025,20094"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9e680dbaa0842d78fb3d305a20874afa\\transformed\\lifecycle-viewmodel-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "323", "startColumns": "4", "startOffsets": "19609", "endColumns": "49", "endOffsets": "19654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d289b368d1c76f56212ad7fb41afa591\\transformed\\fragment-1.5.7\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "289,299,324,2988,2993", "startColumns": "4,4,4,4,4", "startOffsets": "17921,18348,19659,172415,172585", "endLines": "289,299,324,2992,2996", "endColumns": "56,64,63,24,24", "endOffsets": "17973,18408,19718,172580,172729"}}, {"source": "C:\\Users\\<USER>\\.trae\\more\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "1,6,11", "startColumns": "2,2,2", "startOffsets": "14,307,550", "endLines": "5,10,13", "endColumns": "10,10,10", "endOffsets": "304,547,688"}, "to": {"startLines": "443,1706,1851", "startColumns": "4,4,4", "startOffsets": "29784,111855,123053", "endLines": "447,1710,1853", "endColumns": "10,10,10", "endOffsets": "30074,112095,123191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bd18304b20e6fdaf3e3425e5d35bd51e\\transformed\\jetified-flipper-0.182.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,124", "endColumns": "68,56", "endOffsets": "119,176"}, "to": {"startLines": "287,288", "startColumns": "4,4", "startOffsets": "17795,17864", "endColumns": "68,56", "endOffsets": "17859,17916"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4beecdf7ece4b29549030e66145e4634\\transformed\\jetified-drawee-2.6.0\\res\\values\\values.xml", "from": {"startLines": "2,141", "startColumns": "4,4", "startOffsets": "55,6080", "endLines": "140,228", "endColumns": "22,22", "endOffsets": "6075,9685"}, "to": {"startLines": "2997,3422", "startColumns": "4,4", "startOffsets": "172734,185588", "endLines": "3130,3504", "endColumns": "22,22", "endOffsets": "176580,187028"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7c5e03387f63f3788dbd5294d085d57\\transformed\\appcompat-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,709,735,907,933,964,972,978,994,1016,1021,1026,1036,1045,1054,1058,1065,1084,1091,1092,1101,1104,1107,1111,1115,1119,1122,1123,1128,1133,1143,1148,1155,1161,1162,1165,1169,1174,1176,1178,1181,1184,1186,1190,1193,1200,1203,1206,1210,1212,1216,1218,1220,1222,1226,1234,1242,1254,1260,1269,1272,1283,1286,1287,1292,1293,1298,1367,1437,1438,1448,1457,1458,1460,1464,1467,1470,1473,1476,1479,1482,1485,1489,1492,1495,1498,1502,1505,1509,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1535,1537,1538,1539,1540,1541,1542,1543,1544,1546,1547,1549,1550,1552,1554,1555,1557,1558,1559,1560,1561,1562,1564,1565,1566,1567,1568,1569,1571,1573,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1589,1590,1591,1592,1593,1594,1595,1597,1601,1605,1606,1607,1608,1609,1610,1614,1615,1616,1617,1619,1621,1623,1625,1627,1628,1629,1630,1632,1634,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1650,1651,1652,1653,1655,1657,1658,1660,1661,1663,1665,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1680,1681,1682,1683,1685,1686,1687,1688,1689,1691,1693,1695,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1795,1798,1801,1804,1818,1829,1839,1842,1872,1899,1908,1985,2388,2393,2421,2449,2467,2503,2509,2515,2538,2679,2699,2705,2709,2715,2752,2764,2830,2854,2923,2942,2968", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29889,29989,30241,30665,30920,31014,31103,31340,33392,33634,33736,33989,36173,47446,48962,60249,61777,63534,64160,64580,65841,67106,67362,67598,68145,68639,69244,69442,70022,71390,71765,71883,72421,72578,72774,73047,73303,73473,73614,73678,74043,74410,75086,75350,75688,76041,76135,76321,76627,76889,77014,77141,77380,77591,77710,77903,78080,78535,78716,78838,79097,79210,79397,79499,79606,79735,80010,80518,81014,81891,82185,82755,82904,83636,83808,83892,84228,84320,84598,89992,95526,95588,96218,96832,96923,97036,97265,97425,97577,97748,97914,98083,98250,98413,98656,98826,98999,99170,99444,99643,99848,100178,100262,100358,100454,100552,100652,100754,100856,100958,101060,101162,101262,101358,101470,101599,101722,101853,101984,102082,102196,102290,102430,102564,102660,102772,102872,102988,103084,103196,103296,103436,103572,103736,103866,104024,104174,104315,104459,104594,104706,104856,104984,105112,105248,105380,105510,105640,105752,105892,106038,106182,106320,106386,106476,106552,106656,106746,106848,106956,107064,107164,107244,107336,107434,107544,107596,107674,107780,107872,107976,108086,108208,108371,108528,108608,108708,108798,108908,108998,109239,109333,109439,109531,109631,109743,109857,109973,110089,110183,110297,110409,110511,110631,110753,110835,110939,111059,111185,111283,111377,111465,111577,111693,111815,111927,112102,112218,112304,112396,112508,112632,112699,112825,112893,113021,113165,113293,113362,113457,113572,113685,113784,113893,114004,114115,114216,114321,114421,114551,114642,114765,114859,114971,115057,115161,115257,115345,115463,115567,115671,115797,115885,115993,116093,116183,116293,116377,116479,116563,116617,116681,116787,116873,116983,117067,117187,122331,122449,122564,122696,123411,124103,124620,124698,126297,127830,128218,133109,153772,154032,155563,157073,158106,160119,160381,160737,161567,168349,169483,169777,170000,170327,172377,173025,176876,178078,182157,183372,184781", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,708,734,906,932,963,971,977,993,1015,1020,1025,1035,1044,1053,1057,1064,1083,1090,1091,1100,1103,1106,1110,1114,1118,1121,1122,1127,1132,1142,1147,1154,1160,1161,1164,1168,1173,1175,1177,1180,1183,1185,1189,1192,1199,1202,1205,1209,1211,1215,1217,1219,1221,1225,1233,1241,1253,1259,1268,1271,1282,1285,1286,1291,1292,1297,1366,1436,1437,1447,1456,1457,1459,1463,1466,1469,1472,1475,1478,1481,1484,1488,1491,1494,1497,1501,1504,1508,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1534,1536,1537,1538,1539,1540,1541,1542,1543,1545,1546,1548,1549,1551,1553,1554,1556,1557,1558,1559,1560,1561,1563,1564,1565,1566,1567,1568,1570,1572,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1588,1589,1590,1591,1592,1593,1594,1596,1600,1604,1605,1606,1607,1608,1609,1613,1614,1615,1616,1618,1620,1622,1624,1626,1627,1628,1629,1631,1633,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1649,1650,1651,1652,1654,1656,1657,1659,1660,1662,1664,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1679,1680,1681,1682,1684,1685,1686,1687,1688,1690,1692,1694,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1794,1797,1800,1803,1817,1828,1838,1841,1871,1898,1907,1984,2387,2392,2420,2448,2466,2502,2508,2514,2537,2678,2698,2704,2708,2714,2751,2763,2829,2853,2922,2941,2967,2976", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29884,29984,30236,30660,30915,31009,31098,31335,33387,33629,33731,33984,36168,47441,48957,60244,61772,63529,64155,64575,65836,67101,67357,67593,68140,68634,69239,69437,70017,71385,71760,71878,72416,72573,72769,73042,73298,73468,73609,73673,74038,74405,75081,75345,75683,76036,76130,76316,76622,76884,77009,77136,77375,77586,77705,77898,78075,78530,78711,78833,79092,79205,79392,79494,79601,79730,80005,80513,81009,81886,82180,82750,82899,83631,83803,83887,84223,84315,84593,89987,95521,95583,96213,96827,96918,97031,97260,97420,97572,97743,97909,98078,98245,98408,98651,98821,98994,99165,99439,99638,99843,100173,100257,100353,100449,100547,100647,100749,100851,100953,101055,101157,101257,101353,101465,101594,101717,101848,101979,102077,102191,102285,102425,102559,102655,102767,102867,102983,103079,103191,103291,103431,103567,103731,103861,104019,104169,104310,104454,104589,104701,104851,104979,105107,105243,105375,105505,105635,105747,105887,106033,106177,106315,106381,106471,106547,106651,106741,106843,106951,107059,107159,107239,107331,107429,107539,107591,107669,107775,107867,107971,108081,108203,108366,108523,108603,108703,108793,108903,108993,109234,109328,109434,109526,109626,109738,109852,109968,110084,110178,110292,110404,110506,110626,110748,110830,110934,111054,111180,111278,111372,111460,111572,111688,111810,111922,112097,112213,112299,112391,112503,112627,112694,112820,112888,113016,113160,113288,113357,113452,113567,113680,113779,113888,113999,114110,114211,114316,114416,114546,114637,114760,114854,114966,115052,115156,115252,115340,115458,115562,115666,115792,115880,115988,116088,116178,116288,116372,116474,116558,116612,116676,116782,116868,116978,117062,117182,122326,122444,122559,122691,123406,124098,124615,124693,126292,127825,128213,133104,153767,154027,155558,157068,158101,160114,160376,160732,161562,168344,169478,169772,169995,170322,172372,173020,176871,178073,182152,183367,184776,185250"}, "to": {"startLines": "35,36,37,39,40,41,42,43,44,45,46,47,48,51,52,53,54,56,57,58,59,60,61,62,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,201,202,204,205,206,207,208,209,210,230,231,232,233,234,235,236,237,283,284,285,286,290,295,296,300,317,325,326,327,328,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,416,430,431,432,433,434,448,456,457,461,465,469,474,480,487,491,495,500,504,508,512,516,520,524,530,534,540,544,550,554,559,563,566,570,576,580,586,590,596,599,603,607,611,615,619,620,621,622,625,628,631,634,638,639,640,641,642,645,647,649,651,656,657,661,667,671,672,674,686,687,691,697,701,702,703,707,734,738,739,743,771,943,969,1140,1166,1197,1205,1211,1227,1249,1254,1259,1269,1278,1287,1291,1298,1317,1324,1325,1334,1337,1340,1344,1348,1352,1355,1356,1361,1366,1376,1381,1388,1394,1395,1398,1402,1407,1409,1411,1414,1417,1419,1423,1426,1433,1436,1439,1443,1445,1449,1451,1453,1455,1459,1467,1475,1487,1493,1502,1505,1516,1519,1520,1525,1526,1546,1615,1685,1686,1696,1705,1711,1713,1717,1720,1723,1726,1729,1732,1735,1738,1742,1745,1748,1751,1755,1758,1762,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1794,1796,1797,1798,1799,1800,1801,1802,1803,1805,1806,1808,1809,1811,1813,1814,1816,1817,1818,1819,1820,1821,1823,1824,1825,1826,1827,1844,1846,1848,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1868,1869,1870,1871,1872,1873,1874,1876,1880,1939,1940,1941,1942,1943,1944,1948,1949,1950,1951,1953,1955,1957,1959,1961,1962,1963,1964,1966,1968,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1984,1985,1986,1987,1989,1991,1992,1994,1995,1997,1999,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2014,2015,2016,2017,2019,2020,2021,2022,2023,2025,2027,2029,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2095,2170,2173,2176,2179,2193,2199,2241,2244,2273,2300,2309,2373,2750,2760,2798,2916,3170,3194,3200,3206,3227,3351,3371,3377,3381,3387,3505,3537,3603,3623,3678,3690,3716", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1260,1315,1360,1469,1510,1565,1627,1691,1761,1822,1897,1973,2050,2288,2373,2455,2531,2663,2740,2818,2924,3030,3109,3189,3246,3430,3504,3579,3644,3710,3770,3831,3903,3976,4043,4111,4170,4229,4288,4347,4406,4460,4514,4567,4621,4675,4729,5004,5078,5157,5230,5304,5375,5447,5519,5592,5649,5707,5780,5854,5928,6061,6133,6206,6276,6347,6407,6468,6537,6606,6676,6750,6826,6890,6967,7043,7120,7185,7254,7331,7406,7475,7543,7620,7686,7747,7844,7909,7978,8077,8148,8207,8265,8322,8381,8445,8516,8588,8660,8732,8804,8871,8939,9007,9066,9129,9193,9283,9374,9434,9500,9567,9633,9703,9767,9820,9887,9948,10015,10128,10186,10249,10314,10379,10454,10527,10599,10643,10690,10736,10785,10846,10907,10968,11030,11094,11158,11222,11287,11350,11410,11471,11537,11596,11656,11718,11789,11849,12473,12559,12699,12789,12876,12964,13046,13129,13219,14500,14552,14610,14655,14721,14785,14842,14899,17590,17647,17695,17744,17978,18167,18214,18413,19318,19723,19787,19849,19909,20169,20243,20313,20391,20445,20515,20600,20648,20694,20755,20818,20884,20948,21019,21082,21147,21211,21272,21333,21385,21458,21532,21601,21676,21750,21824,21965,27767,28846,28924,29014,29102,29198,30079,30661,30750,30997,31278,31530,31815,32208,32685,32907,33129,33405,33632,33862,34092,34322,34552,34779,35198,35424,35849,36079,36507,36726,37009,37217,37348,37575,38001,38226,38653,38874,39299,39419,39695,39996,40320,40611,40925,41062,41193,41298,41540,41707,41911,42119,42390,42502,42614,42719,42836,43050,43196,43336,43422,43770,43858,44104,44522,44771,44853,44951,45608,45708,45960,46384,46639,46733,46822,47059,49083,49325,49427,49680,51836,62517,64033,74728,76256,78013,78639,79059,80320,81585,81841,82077,82624,83118,83723,83921,84501,85869,86244,86362,86900,87057,87253,87526,87782,87952,88093,88157,88522,88889,89565,89829,90167,90520,90614,90800,91106,91368,91493,91620,91859,92070,92189,92382,92559,93014,93195,93317,93576,93689,93876,93978,94085,94214,94489,94997,95493,96370,96664,97234,97383,98115,98287,98371,98707,98799,99938,105169,110540,110602,111180,111764,112100,112213,112442,112602,112754,112925,113091,113260,113427,113590,113833,114003,114176,114347,114621,114820,115025,115745,115829,115925,116021,116119,116219,116321,116423,116525,116627,116729,116829,116925,117037,117166,117289,117420,117551,117649,117763,117857,117997,118131,118227,118339,118439,118555,118651,118763,118863,119003,119139,119303,119433,119591,119741,119882,120026,120161,120273,120423,120551,120679,120815,120947,121077,121207,121319,122599,122745,122889,123196,123262,123352,123428,123532,123622,123724,123832,123940,124040,124120,124212,124310,124420,124472,124550,124656,124748,124852,124962,125084,125247,128488,128568,128668,128758,128868,128958,129199,129293,129399,129491,129591,129703,129817,129933,130049,130143,130257,130369,130471,130591,130713,130795,130899,131019,131145,131243,131337,131425,131537,131653,131775,131887,132062,132178,132264,132356,132468,132592,132659,132785,132853,132981,133125,133253,133322,133417,133532,133645,133744,133853,133964,134075,134176,134281,134381,134511,134602,134725,134819,134931,135017,135121,135217,135305,135423,135527,135631,135757,135845,135953,136053,136143,136253,136337,136439,136523,136577,136641,136747,136833,136943,137027,139758,142374,142492,142607,142687,143048,143281,144685,144763,146107,147468,147856,150699,161338,161676,163347,169431,177434,178185,178447,178647,179026,183304,183910,184139,184290,184505,187033,187883,190909,191653,193784,194124,195435", "endLines": "35,36,37,39,40,41,42,43,44,45,46,47,48,51,52,53,54,56,57,58,59,60,61,62,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,201,202,204,205,206,207,208,209,210,230,231,232,233,234,235,236,237,283,284,285,286,290,295,296,300,317,325,326,327,328,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,416,430,431,432,433,434,455,456,460,464,468,473,479,486,490,494,499,503,507,511,515,519,523,529,533,539,543,549,553,558,562,565,569,575,579,585,589,595,598,602,606,610,614,618,619,620,621,624,627,630,633,637,638,639,640,641,644,646,648,650,655,656,660,666,670,671,673,685,686,690,696,700,701,702,706,733,737,738,742,770,942,968,1139,1165,1196,1204,1210,1226,1248,1253,1258,1268,1277,1286,1290,1297,1316,1323,1324,1333,1336,1339,1343,1347,1351,1354,1355,1360,1365,1375,1380,1387,1393,1394,1397,1401,1406,1408,1410,1413,1416,1418,1422,1425,1432,1435,1438,1442,1444,1448,1450,1452,1454,1458,1466,1474,1486,1492,1501,1504,1515,1518,1519,1524,1525,1530,1614,1684,1685,1695,1704,1705,1712,1716,1719,1722,1725,1728,1731,1734,1737,1741,1744,1747,1750,1754,1757,1761,1765,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1793,1795,1796,1797,1798,1799,1800,1801,1802,1804,1805,1807,1808,1810,1812,1813,1815,1816,1817,1818,1819,1820,1822,1823,1824,1825,1826,1827,1845,1847,1849,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1867,1868,1869,1870,1871,1872,1873,1875,1879,1883,1939,1940,1941,1942,1943,1947,1948,1949,1950,1952,1954,1956,1958,1960,1961,1962,1963,1965,1967,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1983,1984,1985,1986,1988,1990,1991,1993,1994,1996,1998,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2013,2014,2015,2016,2018,2019,2020,2021,2022,2024,2026,2028,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2169,2172,2175,2178,2192,2198,2208,2243,2272,2299,2308,2372,2735,2753,2787,2825,2933,3193,3199,3205,3226,3350,3370,3376,3380,3386,3421,3516,3602,3622,3677,3689,3715,3722", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1310,1355,1404,1505,1560,1622,1686,1756,1817,1892,1968,2045,2123,2368,2450,2526,2602,2735,2813,2919,3025,3104,3184,3241,3299,3499,3574,3639,3705,3765,3826,3898,3971,4038,4106,4165,4224,4283,4342,4401,4455,4509,4562,4616,4670,4724,4778,5073,5152,5225,5299,5370,5442,5514,5587,5644,5702,5775,5849,5923,5998,6128,6201,6271,6342,6402,6463,6532,6601,6671,6745,6821,6885,6962,7038,7115,7180,7249,7326,7401,7470,7538,7615,7681,7742,7839,7904,7973,8072,8143,8202,8260,8317,8376,8440,8511,8583,8655,8727,8799,8866,8934,9002,9061,9124,9188,9278,9369,9429,9495,9562,9628,9698,9762,9815,9882,9943,10010,10123,10181,10244,10309,10374,10449,10522,10594,10638,10685,10731,10780,10841,10902,10963,11025,11089,11153,11217,11282,11345,11405,11466,11532,11591,11651,11713,11784,11844,11912,12554,12641,12784,12871,12959,13041,13124,13214,13305,14547,14605,14650,14716,14780,14837,14894,14948,17642,17690,17739,17790,18007,18209,18258,18454,19345,19782,19844,19904,19961,20238,20308,20386,20440,20510,20595,20643,20689,20750,20813,20879,20943,21014,21077,21142,21206,21267,21328,21380,21453,21527,21596,21671,21745,21819,21960,22030,27815,28919,29009,29097,29193,29283,30656,30745,30992,31273,31525,31810,32203,32680,32902,33124,33400,33627,33857,34087,34317,34547,34774,35193,35419,35844,36074,36502,36721,37004,37212,37343,37570,37996,38221,38648,38869,39294,39414,39690,39991,40315,40606,40920,41057,41188,41293,41535,41702,41906,42114,42385,42497,42609,42714,42831,43045,43191,43331,43417,43765,43853,44099,44517,44766,44848,44946,45603,45703,45955,46379,46634,46728,46817,47054,49078,49320,49422,49675,51831,62512,64028,74723,76251,78008,78634,79054,80315,81580,81836,82072,82619,83113,83718,83916,84496,85864,86239,86357,86895,87052,87248,87521,87777,87947,88088,88152,88517,88884,89560,89824,90162,90515,90609,90795,91101,91363,91488,91615,91854,92065,92184,92377,92554,93009,93190,93312,93571,93684,93871,93973,94080,94209,94484,94992,95488,96365,96659,97229,97378,98110,98282,98366,98702,98794,99072,105164,110535,110597,111175,111759,111850,112208,112437,112597,112749,112920,113086,113255,113422,113585,113828,113998,114171,114342,114616,114815,115020,115350,115824,115920,116016,116114,116214,116316,116418,116520,116622,116724,116824,116920,117032,117161,117284,117415,117546,117644,117758,117852,117992,118126,118222,118334,118434,118550,118646,118758,118858,118998,119134,119298,119428,119586,119736,119877,120021,120156,120268,120418,120546,120674,120810,120942,121072,121202,121314,121454,122740,122884,123022,123257,123347,123423,123527,123617,123719,123827,123935,124035,124115,124207,124305,124415,124467,124545,124651,124743,124847,124957,125079,125242,125399,128563,128663,128753,128863,128953,129194,129288,129394,129486,129586,129698,129812,129928,130044,130138,130252,130364,130466,130586,130708,130790,130894,131014,131140,131238,131332,131420,131532,131648,131770,131882,132057,132173,132259,132351,132463,132587,132654,132780,132848,132976,133120,133248,133317,133412,133527,133640,133739,133848,133959,134070,134171,134276,134376,134506,134597,134720,134814,134926,135012,135116,135212,135300,135418,135522,135626,135752,135840,135948,136048,136138,136248,136332,136434,136518,136572,136636,136742,136828,136938,137022,137142,142369,142487,142602,142682,143043,143276,143793,144758,146102,147463,147851,150694,160747,161468,163041,164699,169998,178180,178442,178642,179021,183299,183905,184134,184285,184500,185583,187340,190904,191648,193779,194119,195430,195633"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3fe3eeca0055649b8a8e25c3243a726d\\transformed\\jetified-appcompat-resources-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2209,2225,2231,3517,3533", "startColumns": "4,4,4,4,4", "startOffsets": "143798,144223,144401,187345,187756", "endLines": "2224,2230,2240,3532,3536", "endColumns": "24,24,24,24,24", "endOffsets": "144218,144396,144680,187751,187878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\808043cb4cf2ad49d6936bcf600e8cf8\\transformed\\biometric-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,6,8,11,15,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,277,388,523,1701,1757,1810,1886,1946,2035,2134,2242,2339,2427,2527,2597,2694,2804", "endLines": "5,7,10,14,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "189,272,383,518,1696,1752,1805,1881,1941,2030,2129,2237,2334,2422,2522,2592,2689,2799,2888"}, "to": {"startLines": "2,6,8,11,15,55,203,393,394,395,396,397,398,399,400,401,402,403,404", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,289,372,483,618,2607,12646,25824,25900,25960,26049,26148,26256,26353,26441,26541,26611,26708,26818", "endLines": "5,7,10,14,33,55,203,393,394,395,396,397,398,399,400,401,402,403,404", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "284,367,478,613,1194,2658,12694,25895,25955,26044,26143,26251,26348,26436,26536,26606,26703,26813,26902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2660846b8c17c3774a74d166e48ca0f3\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "360", "startColumns": "4", "startOffsets": "22108", "endColumns": "82", "endOffsets": "22186"}}, {"source": "C:\\Users\\<USER>\\.trae\\more\\android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "50", "endOffsets": "62"}, "to": {"startLines": "361", "startColumns": "4", "startOffsets": "22191", "endColumns": "50", "endOffsets": "22237"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5ea14cbe6648367a1a132ad366b227d4\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "34,2082,2826,2832", "startColumns": "4,4,4,4", "startOffsets": "1199,139172,164704,164915", "endLines": "34,2084,2831,2915", "endColumns": "60,12,24,24", "endOffsets": "1255,139312,164910,169426"}}, {"source": "C:\\Users\\<USER>\\.trae\\more\\android\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "2", "startColumns": "2", "startOffsets": "70", "endColumns": "55", "endOffsets": "123"}, "to": {"startLines": "105", "startColumns": "4", "startOffsets": "6003", "endColumns": "57", "endOffsets": "6056"}}]}]}