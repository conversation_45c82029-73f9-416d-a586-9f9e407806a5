apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'maven-publish'

group = 'host.exp.exponent'
version = '0.7.2'

buildscript {
  def expoModulesCorePlugin = new File(project(":expo-modules-core").projectDir.absolutePath, "ExpoModulesCorePlugin.gradle")
  if (expoModulesCorePlugin.exists()) {
    apply from: expoModulesCorePlugin
    applyKotlinExpoModulesCorePlugin()
  }

  // Simple helper that allows the root project to override versions declared by this library.
  ext.safeExtGet = { prop, fallback ->
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
  }

  // Ensures backward compatibility
  ext.getKotlinVersion = {
    if (ext.has("kotlinVersion")) {
      ext.kotlinVersion()
    } else {
      ext.safeExtGet("kotlinVersion", "1.8.10")
    }
  }

  repositories {
    mavenCentral()
  }

  dependencies {
    classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:${getKotlinVersion()}")
  }
}

afterEvaluate {
  publishing {
    publications {
      release(MavenPublication) {
        from components.release
      }
    }
    repositories {
      maven {
        url = mavenLocal().url
      }
    }
  }
}

android {
  compileSdkVersion safeExtGet("compileSdkVersion", 33)

  compileOptions {
    sourceCompatibility JavaVersion.VERSION_11
    targetCompatibility JavaVersion.VERSION_11
  }

  kotlinOptions {
    jvmTarget = JavaVersion.VERSION_11.majorVersion
  }

  namespace "expo.modules.manifests"
  defaultConfig {
    minSdkVersion safeExtGet("minSdkVersion", 21)
    targetSdkVersion safeExtGet("targetSdkVersion", 33)
    versionCode 31
    versionName '0.7.2'
    testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
  }
  lintOptions {
    abortOnError false
  }
  testOptions {
    unitTests.includeAndroidResources = true
  }
  sourceSets {
    androidTest.assets.srcDirs += files("$projectDir/src/androidTest/schemas".toString())
  }
  publishing {
    singleVariant("release") {
      withSourcesJar()
    }
  }
}

dependencies {
  implementation project(':expo-json-utils')

  testImplementation 'junit:junit:4.13.2'
  testImplementation 'androidx.test:core:1.5.0'
  testImplementation 'org.mockito:mockito-core:4.0.0'
  testImplementation 'io.mockk:mockk:1.13.5'
  testImplementation 'org.json:json:20230227'
  testImplementation "com.google.truth:truth:1.1.2"

  implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:${getKotlinVersion()}"
}
repositories {
  mavenCentral()
}
