apply plugin: 'com.android.library'

def safeExtGet(prop, fallback) {
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

android {
    def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
    if (agpVersion.tokenize('.')[0].toInteger() >= 7) {
      namespace "com.oney.WebRTCModule"
    }

    compileSdkVersion safeExtGet('compileSdkVersion', 24)
    buildToolsVersion safeExtGet('buildToolsVersion', "23.0.1")

    defaultConfig {
        minSdkVersion 24
        targetSdkVersion safeExtGet('targetSdkVersion', 24)
        versionCode 1
        versionName "1.0"
        consumerProguardFiles 'consumer-rules.pro'
    }

    // WebRTC requires Java 8 features
    // https://groups.google.com/forum/?utm_medium=email&utm_source=footer#!msg/discuss-webrtc/V1h2uQMDCkA/RA-uzncVAAAJ
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation "com.facebook.react:react-android:+"
    api 'org.jitsi:webrtc:124.+'
    implementation "androidx.core:core:1.7.0"
}
