{"version": 3, "names": ["_reactNative", "require", "WebRTCModule", "NativeModules", "RTCAudioSession", "audioSessionDidActivate", "Platform", "OS", "audioSessionDidDeactivate", "exports", "default"], "sources": ["RTCAudioSession.ts"], "sourcesContent": ["import { NativeModules, Platform } from 'react-native';\n\nconst { WebRTCModule } = NativeModules;\n\nexport default class RTCAudioSession {\n    /**\n     * To be called when CallKit activates the audio session.\n     */\n    static audioSessionDidActivate() {\n        // Only valid for iOS\n        if (Platform.OS === 'ios') {\n            WebRTCModule.audioSessionDidActivate();\n        }\n    }\n\n    /**\n     * To be called when CallKit deactivates the audio session.\n     */\n    static audioSessionDidDeactivate() {\n        // Only valid for iOS\n        if (Platform.OS === 'ios') {\n            WebRTCModule.audioSessionDidDeactivate();\n        }\n    }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEA,MAAM;EAAEC;AAAa,CAAC,GAAGC,0BAAa;AAEvB,MAAMC,eAAe,CAAC;EACjC;AACJ;AACA;EACI,OAAOC,uBAAuBA,CAAA,EAAG;IAC7B;IACA,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MACvBL,YAAY,CAACG,uBAAuB,CAAC,CAAC;IAC1C;EACJ;;EAEA;AACJ;AACA;EACI,OAAOG,yBAAyBA,CAAA,EAAG;IAC/B;IACA,IAAIF,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MACvBL,YAAY,CAACM,yBAAyB,CAAC,CAAC;IAC5C;EACJ;AACJ;AAACC,OAAA,CAAAC,OAAA,GAAAN,eAAA"}