{"name": "expo-constants", "version": "14.4.2", "description": "Provides system information that remains constant throughout the lifetime of your app.", "main": "build/Constants.js", "types": "build/Constants.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "constants"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-constants"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/constants/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"@expo/config": "~8.1.0", "uuid": "^3.3.2"}, "devDependencies": {"expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "gitHead": "0efde1d91bcf609c94a9f9b57f47afd9ef315e2d"}