{"logs": [{"outputFile": "com.streamingapp-mergeDebugResources-61:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\808043cb4cf2ad49d6936bcf600e8cf8\\transformed\\biometric-1.1.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,257,378,508,636,757,877,1024,1118,1248,1378", "endColumns": "112,88,120,129,127,120,119,146,93,129,129,119", "endOffsets": "163,252,373,503,631,752,872,1019,1113,1243,1373,1493"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2813,2926,3015,3136,3266,3394,3515,3635,3782,3876,4006,4136", "endColumns": "112,88,120,129,127,120,119,146,93,129,129,119", "endOffsets": "2921,3010,3131,3261,3389,3510,3630,3777,3871,4001,4131,4251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\01b98716bd18db7c75d98a3148ec3aaa\\transformed\\core-1.8.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "42", "startColumns": "4", "startOffsets": "4340", "endColumns": "100", "endOffsets": "4436"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7c5e03387f63f3788dbd5294d085d57\\transformed\\appcompat-1.4.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,2813", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,2892"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,41", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,4256", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,4335"}}]}]}