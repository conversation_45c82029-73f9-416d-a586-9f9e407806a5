{"testRunner": "jest", "runnerConfig": "e2e/config.json", "skipLegacyWorkersInjection": true, "apps": {"ios": {"type": "ios.app", "binaryPath": "ios/build/Build/Products/Debug-iphonesimulator/LiveStreamApp.app", "build": "xcodebuild -workspace ios/LiveStreamApp.xcworkspace -scheme LiveStreamApp -configuration Debug -sdk iphonesimulator -derivedDataPath ios/build"}, "android": {"type": "android.apk", "binaryPath": "android/app/build/outputs/apk/debug/app-debug.apk", "build": "cd android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug"}}, "devices": {"simulator": {"type": "ios.simulator", "device": {"type": "iPhone 14"}}, "emulator": {"type": "android.emulator", "device": {"avdName": "Pixel_4_API_30"}}}, "configurations": {"ios": {"device": "simulator", "app": "ios"}, "android": {"device": "emulator", "app": "android"}}}