{"version": 3, "names": ["NativeModules", "MediaStream", "MediaStreamError", "WebRTCModule", "getDisplayMedia", "Promise", "resolve", "reject", "then", "data", "streamId", "track", "info", "streamReactTag", "tracks", "stream", "error"], "sources": ["getDisplayMedia.ts"], "sourcesContent": ["\nimport { NativeModules } from 'react-native';\n\nimport MediaStream from './MediaStream';\nimport MediaStreamError from './MediaStreamError';\n\nconst { WebRTCModule } = NativeModules;\n\nexport default function getDisplayMedia(): Promise<MediaStream> {\n    return new Promise((resolve, reject) => {\n        WebRTCModule.getDisplayMedia().then(\n            data => {\n                const { streamId, track } = data;\n\n                const info = {\n                    streamId: streamId,\n                    streamReactTag: streamId,\n                    tracks: [ track ]\n                };\n\n                const stream = new MediaStream(info);\n\n                resolve(stream);\n            },\n            error => {\n                reject(new MediaStreamError(error));\n            }\n        );\n    });\n}\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,cAAc;AAE5C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,gBAAgB,MAAM,oBAAoB;AAEjD,MAAM;EAAEC;AAAa,CAAC,GAAGH,aAAa;AAEtC,eAAe,SAASI,eAAeA,CAAA,EAAyB;EAC5D,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACpCJ,YAAY,CAACC,eAAe,CAAC,CAAC,CAACI,IAAI,CAC/BC,IAAI,IAAI;MACJ,MAAM;QAAEC,QAAQ;QAAEC;MAAM,CAAC,GAAGF,IAAI;MAEhC,MAAMG,IAAI,GAAG;QACTF,QAAQ,EAAEA,QAAQ;QAClBG,cAAc,EAAEH,QAAQ;QACxBI,MAAM,EAAE,CAAEH,KAAK;MACnB,CAAC;MAED,MAAMI,MAAM,GAAG,IAAId,WAAW,CAACW,IAAI,CAAC;MAEpCN,OAAO,CAACS,MAAM,CAAC;IACnB,CAAC,EACDC,KAAK,IAAI;MACLT,MAAM,CAAC,IAAIL,gBAAgB,CAACc,KAAK,CAAC,CAAC;IACvC,CACJ,CAAC;EACL,CAAC,CAAC;AACN"}