{"version": 3, "names": ["debug", "<PERSON><PERSON>", "enable", "ns", "constructor", "prefix", "_defineProperty", "_prefix", "ROOT_PREFIX", "_debug", "_info", "_warn", "_error", "log", "console", "bind", "msg", "info", "warn", "error", "err", "_err$stack", "trace", "stack"], "sources": ["Logger.ts"], "sourcesContent": ["import debug from 'debug';\n\n\nexport default class Logger {\n    static ROOT_PREFIX = 'rn-webrtc';\n\n    private _debug: debug.Debugger;\n    private _info: debug.Debugger;\n    private _warn: debug.Debugger;\n    private _error: debug.Debugger;\n\n    static enable(ns: string): void {\n        debug.enable(ns);\n    }\n\n    constructor(prefix: string) {\n        const _prefix = `${Logger.ROOT_PREFIX}:${prefix}`;\n\n        this._debug = debug(`${_prefix}:DEBUG`);\n        this._info = debug(`${_prefix}:INFO`);\n        this._warn = debug(`${_prefix}:WARN`);\n        this._error = debug(`${_prefix}:ERROR`);\n\n        const log = console.log.bind(console);\n\n        this._debug.log = log;\n        this._info.log = log;\n        this._warn.log = log;\n        this._error.log = log;\n    }\n\n    debug(msg: string): void {\n        this._debug(msg);\n    }\n\n    info(msg: string): void {\n        this._info(msg);\n    }\n\n    warn(msg: string): void {\n        this._warn(msg);\n    }\n\n    error(msg: string, err?: Error): void {\n        const trace = err?.stack ?? 'N/A';\n\n        this._error(`${msg} Trace: ${trace}`);\n    }\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAGzB,eAAe,MAAMC,MAAM,CAAC;EAQxB,OAAOC,MAAMA,CAACC,EAAU,EAAQ;IAC5BH,KAAK,CAACE,MAAM,CAACC,EAAE,CAAC;EACpB;EAEAC,WAAWA,CAACC,MAAc,EAAE;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACxB,MAAMC,OAAO,GAAI,GAAEN,MAAM,CAACO,WAAY,IAAGH,MAAO,EAAC;IAEjD,IAAI,CAACI,MAAM,GAAGT,KAAK,CAAE,GAAEO,OAAQ,QAAO,CAAC;IACvC,IAAI,CAACG,KAAK,GAAGV,KAAK,CAAE,GAAEO,OAAQ,OAAM,CAAC;IACrC,IAAI,CAACI,KAAK,GAAGX,KAAK,CAAE,GAAEO,OAAQ,OAAM,CAAC;IACrC,IAAI,CAACK,MAAM,GAAGZ,KAAK,CAAE,GAAEO,OAAQ,QAAO,CAAC;IAEvC,MAAMM,GAAG,GAAGC,OAAO,CAACD,GAAG,CAACE,IAAI,CAACD,OAAO,CAAC;IAErC,IAAI,CAACL,MAAM,CAACI,GAAG,GAAGA,GAAG;IACrB,IAAI,CAACH,KAAK,CAACG,GAAG,GAAGA,GAAG;IACpB,IAAI,CAACF,KAAK,CAACE,GAAG,GAAGA,GAAG;IACpB,IAAI,CAACD,MAAM,CAACC,GAAG,GAAGA,GAAG;EACzB;EAEAb,KAAKA,CAACgB,GAAW,EAAQ;IACrB,IAAI,CAACP,MAAM,CAACO,GAAG,CAAC;EACpB;EAEAC,IAAIA,CAACD,GAAW,EAAQ;IACpB,IAAI,CAACN,KAAK,CAACM,GAAG,CAAC;EACnB;EAEAE,IAAIA,CAACF,GAAW,EAAQ;IACpB,IAAI,CAACL,KAAK,CAACK,GAAG,CAAC;EACnB;EAEAG,KAAKA,CAACH,GAAW,EAAEI,GAAW,EAAQ;IAAA,IAAAC,UAAA;IAClC,MAAMC,KAAK,IAAAD,UAAA,GAAGD,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEG,KAAK,cAAAF,UAAA,cAAAA,UAAA,GAAI,KAAK;IAEjC,IAAI,CAACT,MAAM,CAAE,GAAEI,GAAI,WAAUM,KAAM,EAAC,CAAC;EACzC;AACJ;AAAChB,eAAA,CA7CoBL,MAAM,iBACF,WAAW"}