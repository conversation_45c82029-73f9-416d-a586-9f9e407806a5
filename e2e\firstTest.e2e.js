const { device, expect, element, by, waitFor } = require('detox');

describe('Live Stream App E2E Tests', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  beforeEach(async () => {
    await device.reloadReactNative();
  });

  describe('Authentication Flow', () => {
    it('should register a new user with COPPA compliance', async () => {
      await element(by.id('register-button')).tap();
      
      // Fill registration form
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('password-input')).typeText('Test@123');
      await element(by.id('age-input')).typeText('15');
      
      // COPPA compliance check
      await element(by.id('coppa-checkbox')).tap();
      await element(by.id('parent-email-input')).typeText('<EMAIL>');
      
      await element(by.id('submit-registration')).tap();
      
      // Wait for registration to complete
      await waitFor(element(by.id('home-screen')))
        .toBeVisible()
        .withTimeout(10000);
    });

    it('should login existing user', async () => {
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('password-input')).typeText('Test@123');
      await element(by.id('login-button')).tap();
      
      await waitFor(element(by.id('home-screen')))
        .toBeVisible()
        .withTimeout(5000);
    });
  });

  describe('Streaming Features', () => {
    it('should start a broadcast stream', async () => {
      // Navigate to broadcast screen
      await element(by.id('broadcast-tab')).tap();
      
      // Grant permissions
      await device.enablePermission('ios.permission.CAMERA');
      await device.enablePermission('ios.permission.MICROPHONE');
      
      // Start stream
      await element(by.id('channel-name-input')).typeText('test-channel');
      await element(by.id('start-stream-button')).tap();
      
      // Verify stream started
      await waitFor(element(by.id('stream-active')))
        .toBeVisible()
        .withTimeout(10000);
      
      // Check viewer count
      await expect(element(by.id('viewer-count'))).toHaveText('0 viewers');
    });

    it('should join a viewer stream', async () => {
      // Navigate to viewer screen
      await element(by.id('viewer-tab')).tap();
      
      // Enter channel name
      await element(by.id('channel-name-input')).typeText('test-channel');
      await element(by.id('join-stream-button')).tap();
      
      // Verify stream joined
      await waitFor(element(by.id('stream-view-active')))
        .toBeVisible()
        .withTimeout(10000);
    });

    it('should handle stream controls', async () => {
      // Test mute/unmute
      await element(by.id('mute-button')).tap();
      await expect(element(by.id('mute-indicator'))).toBeVisible();
      
      // Test quality change
      await element(by.id('quality-selector')).tap();
      await element(by.id('quality-720p')).tap();
      await expect(element(by.id('current-quality'))).toHaveText('720p');
    });
  });

  describe('Performance Monitoring', () => {
    it('should monitor stream performance', async () => {
      await element(by.id('broadcast-tab')).tap();
      await element(by.id('channel-name-input')).typeText('performance-test');
      await element(by.id('start-stream-button')).tap();
      
      // Wait for performance metrics to load
      await waitFor(element(by.id('performance-metrics')))
        .toBeVisible()
        .withTimeout(5000);
      
      // Check key metrics are displayed
      await expect(element(by.id('memory-usage'))).toBeVisible();
      await expect(element(by.id('cpu-usage'))).toBeVisible();
      await expect(element(by.id('fps-counter'))).toBeVisible();
    });

    it('should handle network interruptions gracefully', async () => {
      await element(by.id('viewer-tab')).tap();
      await element(by.id('channel-name-input')).typeText('test-channel');
      await element(by.id('join-stream-button')).tap();
      
      // Simulate network interruption
      await device.disableSynchronization();
      await new Promise(resolve => setTimeout(resolve, 5000));
      await device.enableSynchronization();
      
      // App should recover
      await waitFor(element(by.id('stream-view-active')))
        .toBeVisible()
        .withTimeout(10000);
    });
  });

  describe('Notification System', () => {
    it('should display notifications', async () => {
      await element(by.id('notifications-tab')).tap();
      
      // Check notification list loads
      await waitFor(element(by.id('notification-list')))
        .toBeVisible()
        .withTimeout(5000);
      
      // Test marking notification as read
      await element(by.id('notification-item-0')).tap();
      await expect(element(by.id('notification-read-indicator'))).toBeVisible();
    });

    it('should handle parental consent notifications', async () => {
      // For under-13 users
      await element(by.id('register-button')).tap();
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('password-input')).typeText('Test@123');
      await element(by.id('age-input')).typeText('12');
      
      await element(by.id('coppa-checkbox')).tap();
      await element(by.id('parent-email-input')).typeText('<EMAIL>');
      await element(by.id('submit-registration')).tap();
      
      // Check for parental consent notification
      await waitFor(element(by.id('consent-pending')))
        .toBeVisible()
        .withTimeout(5000);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid channel names', async () => {
      await element(by.id('broadcast-tab')).tap();
      await element(by.id('channel-name-input')).typeText('');
      await element(by.id('start-stream-button')).tap();
      
      await expect(element(by.id('channel-error'))).toBeVisible();
    });

    it('should handle permission denials gracefully', async () => {
      await device.disablePermission('ios.permission.CAMERA');
      
      await element(by.id('broadcast-tab')).tap();
      await element(by.id('channel-name-input')).typeText('test-channel');
      await element(by.id('start-stream-button')).tap();
      
      await expect(element(by.id('permission-error'))).toBeVisible();
    });
  });

  describe('Analytics', () => {
    it('should track user engagement metrics', async () => {
      await element(by.id('analytics-tab')).tap();
      
      // Check analytics dashboard loads
      await waitFor(element(by.id('analytics-dashboard')))
        .toBeVisible()
        .withTimeout(5000);
      
      // Verify key metrics
      await expect(element(by.id('total-streams'))).toBeVisible();
      await expect(element(by.id('total-viewers'))).toBeVisible();
      await expect(element(by.id('avg-watch-time'))).toBeVisible();
    });
  });
});