{"version": 3, "names": ["Event", "RTCTrackEvent", "constructor", "type", "eventInitDict", "_defineProperty", "streams", "transceiver", "receiver", "track"], "sources": ["RTCTrackEvent.ts"], "sourcesContent": ["import { Event } from 'event-target-shim/index';\n\nimport MediaStream from './MediaStream';\nimport type MediaStreamTrack from './MediaStreamTrack';\nimport RTCRtpReceiver from './RTCRtpReceiver';\nimport RTCRtpTransceiver from './RTCRtpTransceiver';\n\ntype TRACK_EVENTS = 'track'\n\ninterface IRTCTrackEventInitDict extends Event.EventInit {\n    streams: MediaStream[]\n    transceiver: RTCRtpTransceiver\n}\n\n/**\n * @eventClass\n * This event is fired whenever the Track is changed in PeerConnection.\n * @param {TRACK_EVENTS} type - The type of event.\n * @param {IRTCTrackEventInitDict} eventInitDict - The event init properties.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/RTCPeerConnection/track_event MDN} for details.\n */\nexport default class RTCTrackEvent<TEventType extends TRACK_EVENTS> extends Event<TEventType> {\n    /** @eventProperty */\n    readonly streams: MediaStream[] = [];\n\n    /** @eventProperty */\n    readonly transceiver: RTCRtpTransceiver;\n\n    /** @eventProperty */\n    readonly receiver: RTCRtpReceiver | null;\n\n    /** @eventProperty */\n    readonly track: MediaStreamTrack | null;\n\n    constructor(type: TEventType, eventInitDict: IRTCTrackEventInitDict) {\n        super(type, eventInitDict);\n        this.streams = eventInitDict.streams;\n        this.transceiver = eventInitDict.transceiver;\n        this.receiver = eventInitDict.transceiver.receiver;\n        this.track = eventInitDict.transceiver.receiver ? eventInitDict.transceiver.receiver.track : null;\n    }\n}\n"], "mappings": ";AAAA,SAASA,KAAK,QAAQ,yBAAyB;AAc/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMC,aAAa,SAA0CD,KAAK,CAAa;EAC1F;;EAGA;;EAGA;;EAGA;;EAGAE,WAAWA,CAACC,IAAgB,EAAEC,aAAqC,EAAE;IACjE,KAAK,CAACD,IAAI,EAAEC,aAAa,CAAC;IAACC,eAAA,kBAZG,EAAE;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAahC,IAAI,CAACC,OAAO,GAAGF,aAAa,CAACE,OAAO;IACpC,IAAI,CAACC,WAAW,GAAGH,aAAa,CAACG,WAAW;IAC5C,IAAI,CAACC,QAAQ,GAAGJ,aAAa,CAACG,WAAW,CAACC,QAAQ;IAClD,IAAI,CAACC,KAAK,GAAGL,aAAa,CAACG,WAAW,CAACC,QAAQ,GAAGJ,aAAa,CAACG,WAAW,CAACC,QAAQ,CAACC,KAAK,GAAG,IAAI;EACrG;AACJ"}