{"version": 3, "names": ["RTCSessionDescription", "constructor", "info", "arguments", "length", "undefined", "type", "sdp", "_defineProperty", "_sdp", "_type", "toJSON", "exports", "default"], "sources": ["RTCSessionDescription.ts"], "sourcesContent": ["\nexport interface RTCSessionDescriptionInit {\n    sdp: string;\n    type: string | null;\n}\n\nexport default class RTCSessionDescription {\n    _sdp: string;\n    _type: string | null;\n\n    constructor(info: RTCSessionDescriptionInit = { type: null, sdp: '' }) {\n        this._sdp = info.sdp;\n        this._type = info.type;\n    }\n\n    get sdp(): string {\n        return this._sdp;\n    }\n\n    get type(): string | null {\n        return this._type;\n    }\n\n    toJSON(): RTCSessionDescriptionInit {\n        return {\n            sdp: this._sdp,\n            type: this._type\n        };\n    }\n}\n"], "mappings": ";;;;;;;AAMe,MAAMA,qBAAqB,CAAC;EAIvCC,WAAWA,CAAA,EAA4D;IAAA,IAA3DC,IAA+B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG;MAAEG,IAAI,EAAE,IAAI;MAAEC,GAAG,EAAE;IAAG,CAAC;IAAAC,eAAA;IAAAA,eAAA;IACjE,IAAI,CAACC,IAAI,GAAGP,IAAI,CAACK,GAAG;IACpB,IAAI,CAACG,KAAK,GAAGR,IAAI,CAACI,IAAI;EAC1B;EAEA,IAAIC,GAAGA,CAAA,EAAW;IACd,OAAO,IAAI,CAACE,IAAI;EACpB;EAEA,IAAIH,IAAIA,CAAA,EAAkB;IACtB,OAAO,IAAI,CAACI,KAAK;EACrB;EAEAC,MAAMA,CAAA,EAA8B;IAChC,OAAO;MACHJ,GAAG,EAAE,IAAI,CAACE,IAAI;MACdH,IAAI,EAAE,IAAI,CAACI;IACf,CAAC;EACL;AACJ;AAACE,OAAA,CAAAC,OAAA,GAAAb,qBAAA"}