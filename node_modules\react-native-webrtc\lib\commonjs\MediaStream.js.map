{"version": 3, "names": ["_index", "require", "_reactNative", "_MediaStreamTrack", "_interopRequireDefault", "_RTCUtil", "obj", "__esModule", "default", "_defineProperty", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "WebRTCModule", "NativeModules", "MediaStream", "EventTarget", "constructor", "arg", "_id", "uniqueID", "_reactTag", "mediaStreamCreate", "id", "track", "getTracks", "addTrack", "Array", "isArray", "streamId", "streamReactTag", "tracks", "trackInfo", "_tracks", "push", "MediaStreamTrack", "TypeError", "active", "index", "indexOf", "mediaStreamAddTrack", "remote", "_peerConnectionId", "removeTrack", "splice", "mediaStreamRemoveTrack", "slice", "getTrackById", "trackId", "find", "getAudioTracks", "filter", "kind", "getVideoTracks", "clone", "Error", "toURL", "release", "releaseTracks", "arguments", "length", "undefined", "mediaStreamRelease", "exports", "proto", "prototype", "defineEventAttribute"], "sources": ["MediaStream.ts"], "sourcesContent": ["import { EventTarget, defineEventAttribute } from 'event-target-shim/index';\nimport { NativeModules } from 'react-native';\n\nimport MediaStreamTrack, { MediaStreamTrackInfo } from './MediaStreamTrack';\nimport MediaStreamTrackEvent from './MediaStreamTrackEvent';\nimport { uniqueID } from './RTCUtil';\n\nconst { WebRTCModule } = NativeModules;\n\ntype MediaStreamEventMap = {\n    addtrack: MediaStreamTrackEvent<'addtrack'>\n    removetrack: MediaStreamTrackEvent<'removetrack'>\n}\n\nexport default class MediaStream extends EventTarget<MediaStreamEventMap> {\n    _tracks: MediaStreamTrack[] = [];\n    _id: string;\n\n    /**\n     * The identifier of this MediaStream unique within the associated\n     * WebRTCModule instance. As the id of a remote MediaStream instance is unique\n     * only within the associated RTCPeerConnection, it is not sufficiently unique\n     * to identify this MediaStream across multiple RTCPeerConnections and to\n     * unambiguously differentiate it from a local MediaStream instance not added\n     * to an RTCPeerConnection.\n     */\n    _reactTag: string;\n\n    /**\n     * A MediaStream can be constructed in several ways, depending on the parameters\n     * that are passed here.\n     *\n     * - undefined: just a new stream, with no tracks.\n     * - MediaStream instance: a new stream, with a copy of the tracks of the passed stream.\n     * - Array of MediaStreamTrack: a new stream with a copy of the tracks in the array.\n     * - object: a new stream instance, represented by the passed info object, this is always\n     *   done internally, when the stream is first created in native and the JS wrapper is\n     *   built afterwards.\n     */\n    constructor(arg?:\n        MediaStream |\n        MediaStreamTrack[] |\n        { streamId: string, streamReactTag: string, tracks: MediaStreamTrackInfo[] }\n    ) {\n        super();\n\n        // Assign a UUID to start with. It will get overridden for remote streams.\n        this._id = uniqueID();\n\n        // Local MediaStreams are created by WebRTCModule to have their id and\n        // reactTag equal because WebRTCModule follows the respective standard's\n        // recommendation for id generation i.e. uses UUID which is unique enough\n        // for the purposes of reactTag.\n        this._reactTag = this._id;\n\n        if (typeof arg === 'undefined') {\n            WebRTCModule.mediaStreamCreate(this.id);\n        } else if (arg instanceof MediaStream) {\n            WebRTCModule.mediaStreamCreate(this.id);\n\n            for (const track of arg.getTracks()) {\n                this.addTrack(track);\n            }\n        } else if (Array.isArray(arg)) {\n            WebRTCModule.mediaStreamCreate(this.id);\n\n            for (const track of arg) {\n                this.addTrack(track);\n            }\n        } else if (typeof arg === 'object' && arg.streamId && arg.streamReactTag && arg.tracks) {\n            this._id = arg.streamId;\n            this._reactTag = arg.streamReactTag;\n\n            for (const trackInfo of arg.tracks) {\n                // We are not using addTrack here because the track is already part of the\n                // stream, so there is no need to add it on the native side.\n                this._tracks.push(new MediaStreamTrack(trackInfo));\n            }\n        } else {\n            throw new TypeError(`invalid type: ${typeof arg}`);\n        }\n    }\n\n    get id(): string {\n        return this._id;\n    }\n\n    get active(): boolean {\n        // TODO: can we reliably report this value?\n\n        return true;\n    }\n\n    addTrack(track: MediaStreamTrack): void {\n        const index = this._tracks.indexOf(track);\n\n        if (index !== -1) {\n            return;\n        }\n\n        this._tracks.push(track);\n        WebRTCModule.mediaStreamAddTrack(this._reactTag, track.remote ? track._peerConnectionId : -1, track.id);\n    }\n\n    removeTrack(track: MediaStreamTrack): void {\n        const index = this._tracks.indexOf(track);\n\n        if (index === -1) {\n            return;\n        }\n\n        this._tracks.splice(index, 1);\n        WebRTCModule.mediaStreamRemoveTrack(this._reactTag, track.remote ? track._peerConnectionId : -1, track.id);\n    }\n\n    getTracks(): MediaStreamTrack[] {\n        return this._tracks.slice();\n    }\n\n    getTrackById(trackId): MediaStreamTrack | undefined {\n        return this._tracks.find(track => track.id === trackId);\n    }\n\n    getAudioTracks(): MediaStreamTrack[] {\n        return this._tracks.filter(track => track.kind === 'audio');\n    }\n\n    getVideoTracks(): MediaStreamTrack[] {\n        return this._tracks.filter(track => track.kind === 'video');\n    }\n\n    clone(): never {\n        throw new Error('Not implemented.');\n    }\n\n    toURL(): string {\n        return this._reactTag;\n    }\n\n    release(releaseTracks = true): void {\n        const tracks = [ ...this._tracks ];\n\n        for (const track of tracks) {\n            this.removeTrack(track);\n\n            if (releaseTracks) {\n                track.release();\n            }\n        }\n\n        WebRTCModule.mediaStreamRelease(this._reactTag);\n    }\n}\n\n/**\n * Define the `onxxx` event handlers.\n */\nconst proto = MediaStream.prototype;\n\ndefineEventAttribute(proto, 'addtrack');\ndefineEventAttribute(proto, 'removetrack');\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,iBAAA,GAAAC,sBAAA,CAAAH,OAAA;AAEA,IAAAI,QAAA,GAAAJ,OAAA;AAAqC,SAAAG,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,gBAAAH,GAAA,EAAAI,GAAA,EAAAC,KAAA,QAAAD,GAAA,IAAAJ,GAAA,IAAAM,MAAA,CAAAC,cAAA,CAAAP,GAAA,EAAAI,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAG,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAV,GAAA,CAAAI,GAAA,IAAAC,KAAA,WAAAL,GAAA;AAErC,MAAM;EAAEW;AAAa,CAAC,GAAGC,0BAAa;AAOvB,MAAMC,WAAW,SAASC,kBAAW,CAAsB;EAItE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;EAGI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACC,GAGoE,EAC9E;IACE,KAAK,CAAC,CAAC;;IAEP;IAAAb,eAAA,kBA/B0B,EAAE;IAAAA,eAAA;IAAAA,eAAA;IAgC5B,IAAI,CAACc,GAAG,GAAG,IAAAC,iBAAQ,EAAC,CAAC;;IAErB;IACA;IACA;IACA;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACF,GAAG;IAEzB,IAAI,OAAOD,GAAG,KAAK,WAAW,EAAE;MAC5BL,YAAY,CAACS,iBAAiB,CAAC,IAAI,CAACC,EAAE,CAAC;IAC3C,CAAC,MAAM,IAAIL,GAAG,YAAYH,WAAW,EAAE;MACnCF,YAAY,CAACS,iBAAiB,CAAC,IAAI,CAACC,EAAE,CAAC;MAEvC,KAAK,MAAMC,KAAK,IAAIN,GAAG,CAACO,SAAS,CAAC,CAAC,EAAE;QACjC,IAAI,CAACC,QAAQ,CAACF,KAAK,CAAC;MACxB;IACJ,CAAC,MAAM,IAAIG,KAAK,CAACC,OAAO,CAACV,GAAG,CAAC,EAAE;MAC3BL,YAAY,CAACS,iBAAiB,CAAC,IAAI,CAACC,EAAE,CAAC;MAEvC,KAAK,MAAMC,KAAK,IAAIN,GAAG,EAAE;QACrB,IAAI,CAACQ,QAAQ,CAACF,KAAK,CAAC;MACxB;IACJ,CAAC,MAAM,IAAI,OAAON,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACW,QAAQ,IAAIX,GAAG,CAACY,cAAc,IAAIZ,GAAG,CAACa,MAAM,EAAE;MACpF,IAAI,CAACZ,GAAG,GAAGD,GAAG,CAACW,QAAQ;MACvB,IAAI,CAACR,SAAS,GAAGH,GAAG,CAACY,cAAc;MAEnC,KAAK,MAAME,SAAS,IAAId,GAAG,CAACa,MAAM,EAAE;QAChC;QACA;QACA,IAAI,CAACE,OAAO,CAACC,IAAI,CAAC,IAAIC,yBAAgB,CAACH,SAAS,CAAC,CAAC;MACtD;IACJ,CAAC,MAAM;MACH,MAAM,IAAII,SAAS,CAAE,iBAAgB,OAAOlB,GAAI,EAAC,CAAC;IACtD;EACJ;EAEA,IAAIK,EAAEA,CAAA,EAAW;IACb,OAAO,IAAI,CAACJ,GAAG;EACnB;EAEA,IAAIkB,MAAMA,CAAA,EAAY;IAClB;;IAEA,OAAO,IAAI;EACf;EAEAX,QAAQA,CAACF,KAAuB,EAAQ;IACpC,MAAMc,KAAK,GAAG,IAAI,CAACL,OAAO,CAACM,OAAO,CAACf,KAAK,CAAC;IAEzC,IAAIc,KAAK,KAAK,CAAC,CAAC,EAAE;MACd;IACJ;IAEA,IAAI,CAACL,OAAO,CAACC,IAAI,CAACV,KAAK,CAAC;IACxBX,YAAY,CAAC2B,mBAAmB,CAAC,IAAI,CAACnB,SAAS,EAAEG,KAAK,CAACiB,MAAM,GAAGjB,KAAK,CAACkB,iBAAiB,GAAG,CAAC,CAAC,EAAElB,KAAK,CAACD,EAAE,CAAC;EAC3G;EAEAoB,WAAWA,CAACnB,KAAuB,EAAQ;IACvC,MAAMc,KAAK,GAAG,IAAI,CAACL,OAAO,CAACM,OAAO,CAACf,KAAK,CAAC;IAEzC,IAAIc,KAAK,KAAK,CAAC,CAAC,EAAE;MACd;IACJ;IAEA,IAAI,CAACL,OAAO,CAACW,MAAM,CAACN,KAAK,EAAE,CAAC,CAAC;IAC7BzB,YAAY,CAACgC,sBAAsB,CAAC,IAAI,CAACxB,SAAS,EAAEG,KAAK,CAACiB,MAAM,GAAGjB,KAAK,CAACkB,iBAAiB,GAAG,CAAC,CAAC,EAAElB,KAAK,CAACD,EAAE,CAAC;EAC9G;EAEAE,SAASA,CAAA,EAAuB;IAC5B,OAAO,IAAI,CAACQ,OAAO,CAACa,KAAK,CAAC,CAAC;EAC/B;EAEAC,YAAYA,CAACC,OAAO,EAAgC;IAChD,OAAO,IAAI,CAACf,OAAO,CAACgB,IAAI,CAACzB,KAAK,IAAIA,KAAK,CAACD,EAAE,KAAKyB,OAAO,CAAC;EAC3D;EAEAE,cAAcA,CAAA,EAAuB;IACjC,OAAO,IAAI,CAACjB,OAAO,CAACkB,MAAM,CAAC3B,KAAK,IAAIA,KAAK,CAAC4B,IAAI,KAAK,OAAO,CAAC;EAC/D;EAEAC,cAAcA,CAAA,EAAuB;IACjC,OAAO,IAAI,CAACpB,OAAO,CAACkB,MAAM,CAAC3B,KAAK,IAAIA,KAAK,CAAC4B,IAAI,KAAK,OAAO,CAAC;EAC/D;EAEAE,KAAKA,CAAA,EAAU;IACX,MAAM,IAAIC,KAAK,CAAC,kBAAkB,CAAC;EACvC;EAEAC,KAAKA,CAAA,EAAW;IACZ,OAAO,IAAI,CAACnC,SAAS;EACzB;EAEAoC,OAAOA,CAAA,EAA6B;IAAA,IAA5BC,aAAa,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACxB,MAAM5B,MAAM,GAAG,CAAE,GAAG,IAAI,CAACE,OAAO,CAAE;IAElC,KAAK,MAAMT,KAAK,IAAIO,MAAM,EAAE;MACxB,IAAI,CAACY,WAAW,CAACnB,KAAK,CAAC;MAEvB,IAAIkC,aAAa,EAAE;QACflC,KAAK,CAACiC,OAAO,CAAC,CAAC;MACnB;IACJ;IAEA5C,YAAY,CAACiD,kBAAkB,CAAC,IAAI,CAACzC,SAAS,CAAC;EACnD;AACJ;;AAEA;AACA;AACA;AAFA0C,OAAA,CAAA3D,OAAA,GAAAW,WAAA;AAGA,MAAMiD,KAAK,GAAGjD,WAAW,CAACkD,SAAS;AAEnC,IAAAC,2BAAoB,EAACF,KAAK,EAAE,UAAU,CAAC;AACvC,IAAAE,2BAAoB,EAACF,KAAK,EAAE,aAAa,CAAC"}