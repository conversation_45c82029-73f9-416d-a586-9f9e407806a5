@echo off
"C:\\Program Files\\Java\\jdk-17\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.0.0\\f2702b5ca13df54e3ca92f29d6b403fb6285d8df\\cli-2.0.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  arm64-v8a ^
  --os-version ^
  21 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  23 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging5469095538533328661\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ed667b43ae46cb0af977d67989c9eadf\\transformed\\jetified-react-android-0.72.10-debug\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8b6cc7607ffe5936bd42bb311e7e5848\\transformed\\jetified-fbjni-0.3.0\\prefab"
