{"version": 3, "names": ["_index", "require", "_defineProperty", "obj", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "RTCIceCandidateEvent", "Event", "constructor", "type", "eventInitDict", "_eventInitDict$candid", "candidate", "exports", "default"], "sources": ["RTCIceCandidateEvent.ts"], "sourcesContent": ["import { Event } from 'event-target-shim/index';\n\nimport type RTCIceCandidate from './RTCIceCandidate';\n\ntype RTC_ICECANDIDATE_EVENTS = 'icecandidate' | 'icecandidateerror'\n\ninterface IRTCDataChannelEventInitDict extends Event.EventInit {\n    candidate: RTCIceCandidate | null\n}\n\n/**\n * @eventClass\n * This event is fired whenever the icecandidate related RTC_EVENTS changed.\n * @type {RTCIceCandidateEvent} for icecandidate related.\n * @param {RTC_ICECANDIDATE_EVENTS} type - The type of event.\n * @param {IRTCDataChannelEventInitDict} eventInitDict - The event init properties.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/RTCPeerConnection#events MDN} for details.\n */\nexport default class RTCIceCandidateEvent<TEventType extends RTC_ICECANDIDATE_EVENTS> extends Event<TEventType> {\n    /** @eventProperty */\n    candidate: RTCIceCandidate | null;\n    constructor(type: TEventType, eventInitDict: IRTCDataChannelEventInitDict) {\n        super(type, eventInitDict);\n        this.candidate = eventInitDict?.candidate ?? null;\n    }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAAgD,SAAAC,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,QAAAD,GAAA,IAAAD,GAAA,IAAAG,MAAA,CAAAC,cAAA,CAAAJ,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAG,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAP,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAUhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,MAAMQ,oBAAoB,SAAqDC,YAAK,CAAa;EAC5G;;EAEAC,WAAWA,CAACC,IAAgB,EAAEC,aAA2C,EAAE;IAAA,IAAAC,qBAAA;IACvE,KAAK,CAACF,IAAI,EAAEC,aAAa,CAAC;IAACb,eAAA;IAC3B,IAAI,CAACe,SAAS,IAAAD,qBAAA,GAAGD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEE,SAAS,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,IAAI;EACrD;AACJ;AAACE,OAAA,CAAAC,OAAA,GAAAR,oBAAA"}