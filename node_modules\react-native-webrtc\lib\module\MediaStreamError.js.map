{"version": 3, "names": ["MediaStreamError", "constructor", "error", "_defineProperty", "name", "message", "constraintName"], "sources": ["MediaStreamError.ts"], "sourcesContent": ["\nexport default class MediaStreamError {\n    name: string;\n    message?: string;\n    constraintName?: string;\n\n    constructor(error) {\n        this.name = error.name;\n        this.message = error.message;\n        this.constraintName = error.constraintName;\n    }\n}\n"], "mappings": ";AACA,eAAe,MAAMA,gBAAgB,CAAC;EAKlCC,WAAWA,CAACC,KAAK,EAAE;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACf,IAAI,CAACC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACtB,IAAI,CAACC,OAAO,GAAGH,KAAK,CAACG,OAAO;IAC5B,IAAI,CAACC,cAAc,GAAGJ,KAAK,CAACI,cAAc;EAC9C;AACJ"}