{"version": 3, "names": ["NativeModules", "NativeEventEmitter", "EventEmitter", "WebRTCModule", "nativeEmitter", "NATIVE_EVENTS", "eventEmitter", "setupNativeEvents", "eventName", "addListener", "_len", "arguments", "length", "args", "Array", "_key", "emit", "_subscriptions", "Map", "listener", "<PERSON><PERSON><PERSON><PERSON>", "_subscriptions$get", "includes", "Error", "has", "set", "get", "push", "removeListener", "_subscriptions$get2", "for<PERSON>ach", "sub", "remove", "delete"], "sources": ["EventEmitter.ts"], "sourcesContent": ["import { NativeModules, NativeEventEmitter, EmitterSubscription } from 'react-native';\n// @ts-ignore\nimport EventEmitter from 'react-native/Libraries/vendor/emitter/EventEmitter';\n\nconst { WebRTCModule } = NativeModules;\n\n// This emitter is going to be used to listen to all the native events (once) and then\n// re-emit them on a JS-only emitter.\nconst nativeEmitter = new NativeEventEmitter(WebRTCModule);\n\nconst NATIVE_EVENTS = [\n    'peerConnectionSignalingStateChanged',\n    'peerConnectionStateChanged',\n    'peerConnectionOnRenegotiationNeeded',\n    'peerConnectionIceConnectionChanged',\n    'peerConnectionIceGatheringChanged',\n    'peerConnectionGotICECandidate',\n    'peerConnectionDidOpenDataChannel',\n    'peerConnectionOnRemoveTrack',\n    'peerConnectionOnTrack',\n    'dataChannelStateChanged',\n    'dataChannelReceiveMessage',\n    'dataChannelDidChangeBufferedAmount',\n    'mediaStreamTrackMuteChanged',\n    'mediaStreamTrackEnded',\n];\n\nconst eventEmitter = new EventEmitter();\n\nexport function setupNativeEvents() {\n    for (const eventName of NATIVE_EVENTS) {\n        nativeEmitter.addListener(eventName, (...args) => {\n            eventEmitter.emit(eventName, ...args);\n        });\n    }\n}\n\ntype EventHandler = (event: unknown) => void;\ntype Listener = unknown;\n\nconst _subscriptions: Map<Listener, EmitterSubscription[]> = new Map();\n\nexport function addListener(listener: Listener, eventName: string, eventHandler: EventHandler): void {\n    if (!NATIVE_EVENTS.includes(eventName)) {\n        throw new Error(`Invalid event: ${eventName}`);\n    }\n\n    if (!_subscriptions.has(listener)) {\n        _subscriptions.set(listener, []);\n    }\n\n    _subscriptions.get(listener)?.push(eventEmitter.addListener(eventName, eventHandler));\n}\n\nexport function removeListener(listener: Listener): void {\n    _subscriptions.get(listener)?.forEach(sub => {\n        sub.remove();\n    });\n\n    _subscriptions.delete(listener);\n}\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,kBAAkB,QAA6B,cAAc;AACrF;AACA,OAAOC,YAAY,MAAM,oDAAoD;AAE7E,MAAM;EAAEC;AAAa,CAAC,GAAGH,aAAa;;AAEtC;AACA;AACA,MAAMI,aAAa,GAAG,IAAIH,kBAAkB,CAACE,YAAY,CAAC;AAE1D,MAAME,aAAa,GAAG,CAClB,qCAAqC,EACrC,4BAA4B,EAC5B,qCAAqC,EACrC,oCAAoC,EACpC,mCAAmC,EACnC,+BAA+B,EAC/B,kCAAkC,EAClC,6BAA6B,EAC7B,uBAAuB,EACvB,yBAAyB,EACzB,2BAA2B,EAC3B,oCAAoC,EACpC,6BAA6B,EAC7B,uBAAuB,CAC1B;AAED,MAAMC,YAAY,GAAG,IAAIJ,YAAY,CAAC,CAAC;AAEvC,OAAO,SAASK,iBAAiBA,CAAA,EAAG;EAChC,KAAK,MAAMC,SAAS,IAAIH,aAAa,EAAE;IACnCD,aAAa,CAACK,WAAW,CAACD,SAAS,EAAE,YAAa;MAAA,SAAAE,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAATC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;MAAA;MACzCT,YAAY,CAACU,IAAI,CAACR,SAAS,EAAE,GAAGK,IAAI,CAAC;IACzC,CAAC,CAAC;EACN;AACJ;AAKA,MAAMI,cAAoD,GAAG,IAAIC,GAAG,CAAC,CAAC;AAEtE,OAAO,SAAST,WAAWA,CAACU,QAAkB,EAAEX,SAAiB,EAAEY,YAA0B,EAAQ;EAAA,IAAAC,kBAAA;EACjG,IAAI,CAAChB,aAAa,CAACiB,QAAQ,CAACd,SAAS,CAAC,EAAE;IACpC,MAAM,IAAIe,KAAK,CAAE,kBAAiBf,SAAU,EAAC,CAAC;EAClD;EAEA,IAAI,CAACS,cAAc,CAACO,GAAG,CAACL,QAAQ,CAAC,EAAE;IAC/BF,cAAc,CAACQ,GAAG,CAACN,QAAQ,EAAE,EAAE,CAAC;EACpC;EAEA,CAAAE,kBAAA,GAAAJ,cAAc,CAACS,GAAG,CAACP,QAAQ,CAAC,cAAAE,kBAAA,uBAA5BA,kBAAA,CAA8BM,IAAI,CAACrB,YAAY,CAACG,WAAW,CAACD,SAAS,EAAEY,YAAY,CAAC,CAAC;AACzF;AAEA,OAAO,SAASQ,cAAcA,CAACT,QAAkB,EAAQ;EAAA,IAAAU,mBAAA;EACrD,CAAAA,mBAAA,GAAAZ,cAAc,CAACS,GAAG,CAACP,QAAQ,CAAC,cAAAU,mBAAA,uBAA5BA,mBAAA,CAA8BC,OAAO,CAACC,GAAG,IAAI;IACzCA,GAAG,CAACC,MAAM,CAAC,CAAC;EAChB,CAAC,CAAC;EAEFf,cAAc,CAACgB,MAAM,CAACd,QAAQ,CAAC;AACnC"}