import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  PermissionsAndroid,
  Platform,
  ActivityIndicator,
  Alert,
  TextInput,
  AccessibilityInfo,
  Dimensions,
} from 'react-native';
import { ClientRoleType, RtcSurfaceView } from 'react-native-agora';
import AgoraService from '../services/AgoraService';
import { CONFIG } from '../config/index';
import BackendService from '../services/BackendService';

const StreamBroadcastScreen = ({ route }: any) => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [cameraPermission, setCameraPermission] = useState(false);
  const [audioPermission, setAudioPermission] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [streamSessionId, setStreamSessionId] = useState<string | null>(null);
  const [channelName, setChannelName] = useState(route?.params?.channelName || 'test-channel');
  const [streamTitle, setStreamTitle] = useState('');
  const [token] = useState(route?.params?.token || '');
  const [isMuted, setIsMuted] = useState(false);
  const [isFrontCamera, setIsFrontCamera] = useState(true);
  const agoraService = useRef(new AgoraService()).current;
  const backendService = useRef(new BackendService()).current;
  const [uid] = useState(Math.floor(Math.random() * 1000));

  useEffect(() => {
    const initAgora = async () => {
      try {
        setIsLoading(true);
        setError(null);

        await agoraService.initialize(CONFIG.AGORA_APP_ID);
        await requestPermissions();

        setIsLoading(false);
      } catch (error) {
        console.error('Failed to initialize Agora:', error);
        setError('Failed to initialize streaming service');
        setIsLoading(false);
      }
    };

    initAgora();

    return () => {
      if (isStreaming) {
        stopStream();
      }
    };
  }, []);

  const requestPermissions = async () => {
    try {
      if (Platform.OS === 'android') {
        const cameraGranted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'Camera Permission',
            message: 'This app needs access to your camera to start streaming.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );

        const audioGranted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
          {
            title: 'Audio Permission',
            message: 'This app needs access to your microphone to start streaming.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );

        setCameraPermission(cameraGranted === PermissionsAndroid.RESULTS.GRANTED);
        setAudioPermission(audioGranted === PermissionsAndroid.RESULTS.GRANTED);

        if (cameraGranted !== PermissionsAndroid.RESULTS.GRANTED ||
            audioGranted !== PermissionsAndroid.RESULTS.GRANTED) {
          setError('Camera and audio permissions are required to start streaming');
        }
      } else {
        // iOS permissions are handled in Info.plist
        setCameraPermission(true);
        setAudioPermission(true);
      }
    } catch (err) {
      console.warn(err);
      setError('Failed to request permissions');
    }
  };

  const startStream = async () => {
    try {
      if (!cameraPermission || !audioPermission) {
        Alert.alert(
          'Permission Required',
          'Please grant camera and audio permissions to start streaming'
        );
        return;
      }

      if (!channelName.trim()) {
        Alert.alert('Channel Required', 'Please enter a channel name');
        return;
      }

      setIsLoading(true);
      setError(null);

      // Create stream session
      const { session, error: sessionError } = await backendService.startStreamSession(
        channelName.trim(),
        {
          title: streamTitle.trim() || 'Live Stream',
          description: 'Live streaming session'
        }
      );

      if (sessionError || !session) {
        throw new Error(sessionError || 'Failed to create stream session');
      }

      setStreamSessionId(session.id);

      // Configure video quality
      agoraService.setVideoQuality('720p');

      // Start preview for broadcaster
      await agoraService.startPreview();

      // Join Agora channel as broadcaster
      await agoraService.joinChannel(
        token,
        channelName.trim(),
        uid,
        ClientRoleType.ClientRoleBroadcaster
      );

      setIsStreaming(true);
      setIsLoading(false);

      AccessibilityInfo.announceForAccessibility('Stream started successfully');
    } catch (error) {
      console.error('Failed to start stream:', error);
      setError('Failed to start stream. Please check your connection and try again.');
      setIsLoading(false);
    }
  };

  const stopStream = async () => {
    try {
      setIsLoading(true);

      await agoraService.leaveChannel();
      setIsStreaming(false);

      // End stream session
      if (streamSessionId) {
        await backendService.endStreamSession(streamSessionId);
      }

      setStreamSessionId(null);
      setIsLoading(false);

      AccessibilityInfo.announceForAccessibility('Stream ended successfully');
    } catch (error) {
      console.error('Failed to stop stream:', error);
      setError('Failed to stop stream');
      setIsLoading(false);
    }
  };

  const toggleMute = () => {
    agoraService.toggleMute();
    setIsMuted(!isMuted);
    AccessibilityInfo.announceForAccessibility(isMuted ? 'Microphone unmuted' : 'Microphone muted');
  };

  const switchCamera = () => {
    agoraService.switchCamera();
    setIsFrontCamera(!isFrontCamera);
    AccessibilityInfo.announceForAccessibility('Camera switched');
  };

  const renderStreamDisplay = () => {
    if (!isStreaming) {
      return (
        <View style={styles.previewContainer}>
          <Text style={styles.previewText}>Camera Preview</Text>
          <Text style={styles.instructionText}>
            Configure your stream settings and press Start Stream
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.videoContainer}>
        <RtcSurfaceView style={styles.videoView} canvas={{ uid: uid }} />
        <View style={styles.overlay}>
          <View style={styles.streamHeader}>
            <Text style={styles.streamTitleLive}>LIVE</Text>
            <Text style={styles.streamTitle}>{streamTitle || channelName}</Text>
          </View>
          <View style={styles.streamFooter}>
            <Text style={styles.viewerCount}>Viewers: 0</Text>
          </View>
        </View>
      </View>
    );
  };

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => setError(null)}
          accessibilityLabel="Retry"
        >
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title} accessibilityRole="header">
        Stream Broadcasting
      </Text>

      {!isStreaming && (
        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            placeholder="Channel Name"
            value={channelName}
            onChangeText={setChannelName}
            editable={!isStreaming}
            accessibilityLabel="Channel Name"
          />
          <TextInput
            style={styles.input}
            placeholder="Stream Title (optional)"
            value={streamTitle}
            onChangeText={setStreamTitle}
            editable={!isStreaming}
            accessibilityLabel="Stream Title"
          />
        </View>
      )}

      {renderStreamDisplay()}

      {isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Processing...</Text>
        </View>
      )}

      <View style={styles.controls}>
        <TouchableOpacity
          style={[styles.button, isStreaming ? styles.stopButton : styles.startButton]}
          onPress={isStreaming ? stopStream : startStream}
          disabled={isLoading}
          accessibilityLabel={isStreaming ? 'Stop Stream' : 'Start Stream'}
        >
          <Text style={styles.buttonText}>
            {isStreaming ? 'Stop Stream' : 'Start Stream'}
          </Text>
        </TouchableOpacity>

        {isStreaming && (
          <View style={styles.controlButtons}>
            <TouchableOpacity
              style={[styles.controlButton, isMuted && styles.mutedButton]}
              onPress={toggleMute}
              accessibilityLabel={isMuted ? 'Unmute microphone' : 'Mute microphone'}
            >
              <Text style={styles.controlButtonText}>
                {isMuted ? '🔇' : '🎤'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.controlButton}
              onPress={switchCamera}
              accessibilityLabel="Switch Camera"
            >
              <Text style={styles.controlButtonText}>📷</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333',
  },
  inputContainer: {
    width: '100%',
    marginBottom: 20,
  },
  input: {
    height: 50,
    borderColor: '#ddd',
    borderWidth: 1,
    borderRadius: 10,
    paddingHorizontal: 15,
    marginBottom: 10,
    backgroundColor: '#fff',
    fontSize: 16,
  },
  previewContainer: {
    width: '100%',
    height: 300,
    backgroundColor: '#000',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  previewText: {
    color: '#fff',
    fontSize: 18,
    marginBottom: 10,
  },
  instructionText: {
    color: '#aaa',
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  videoContainer: {
    width: '100%',
    height: 300,
    borderRadius: 10,
    overflow: 'hidden',
    marginBottom: 20,
    position: 'relative',
  },
  videoView: {
    flex: 1,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  streamHeader: {
    position: 'absolute',
    top: 10,
    left: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  streamTitleLive: {
    backgroundColor: '#FF3B30',
    color: '#fff',
    padding: 5,
    borderRadius: 3,
    fontSize: 12,
    fontWeight: 'bold',
    marginRight: 10,
  },
  streamTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textShadowColor: 'rgba(0,0,0,0.8)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  streamFooter: {
    position: 'absolute',
    bottom: 10,
    left: 10,
  },
  viewerCount: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    color: '#fff',
    padding: 5,
    borderRadius: 5,
    fontSize: 12,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  button: {
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    minWidth: 120,
    alignItems: 'center',
  },
  startButton: {
    backgroundColor: '#34C759',
  },
  stopButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  controlButtons: {
    flexDirection: 'row',
    marginLeft: 10,
  },
  controlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
  mutedButton: {
    backgroundColor: '#FF3B30',
  },
  controlButtonText: {
    fontSize: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#FF3B30',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#007AFF',
    borderRadius: 5,
  },
  retryText: {
    color: '#fff',
    fontSize: 16,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.8)',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
});

export default StreamBroadcastScreen;