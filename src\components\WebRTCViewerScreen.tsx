/**
 * WebRTC-based Stream Viewer Screen
 * Real streaming without native dependencies
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  StatusBar,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { webRTCStreamingService } from '../services/WebRTCStreamingService';

interface Props {
  channelName: string;
  onLeave: () => void;
}

export default function WebRTCViewerScreen({ channelName, onLeave }: Props) {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [connectionState, setConnectionState] = useState('connecting');
  const [error, setError] = useState<string | null>(null);
  const [streamInfo, setStreamInfo] = useState({
    title: 'Live Stream',
    viewerCount: 0,
  });

  const remoteVideoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    initializeViewing();
    return () => {
      webRTCStreamingService.stopStreaming();
    };
  }, []);

  const initializeViewing = async () => {
    try {
      setIsLoading(true);
      
      await webRTCStreamingService.initialize({
        channelName,
        isHost: false,
        onRemoteStream: (stream) => {
          console.log('Received remote stream');
          setIsConnected(true);
          setIsLoading(false);
          
          // Display remote stream
          if (remoteVideoRef.current) {
            remoteVideoRef.current.srcObject = stream;
          }
        },
        onConnectionStateChange: (state) => {
          setConnectionState(state);
          if (state === 'connected') {
            setIsConnected(true);
            setIsLoading(false);
          } else if (state === 'disconnected') {
            setIsConnected(false);
            setError('Stream disconnected');
          }
        },
        onError: (error) => {
          setError(error.message);
          setIsLoading(false);
          Alert.alert('Streaming Error', error.message);
        },
      });

      // Join the stream
      await webRTCStreamingService.joinStream();
      
    } catch (error) {
      console.error('Failed to initialize viewer:', error);
      setError('Failed to connect to stream');
      setIsLoading(false);
    }
  };

  const leaveStream = async () => {
    try {
      await webRTCStreamingService.stopStreaming();
      onLeave();
    } catch (error) {
      console.error('Failed to leave stream:', error);
      onLeave();
    }
  };

  const renderStreamDisplay = () => {
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Connecting to stream...</Text>
          <Text style={styles.channelText}>Channel: {channelName}</Text>
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorIcon}>⚠️</Text>
          <Text style={styles.errorTitle}>Connection Failed</Text>
          <Text style={styles.errorMessage}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={initializeViewing}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }

    if (!isConnected) {
      return (
        <View style={styles.waitingContainer}>
          <Text style={styles.waitingIcon}>📡</Text>
          <Text style={styles.waitingTitle}>Waiting for Stream</Text>
          <Text style={styles.waitingMessage}>
            The broadcaster hasn't started streaming yet
          </Text>
          <Text style={styles.channelText}>Channel: {channelName}</Text>
        </View>
      );
    }

    if (Platform.OS === 'web') {
      // Web implementation with HTML video element
      return (
        <div style={{ flex: 1, position: 'relative' }}>
          <video
            ref={remoteVideoRef}
            autoPlay
            playsInline
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              backgroundColor: '#000',
            }}
          />
          {renderOverlay()}
        </div>
      );
    } else {
      // Mobile implementation - would need custom video component
      return (
        <View style={styles.videoContainer}>
          <Text style={styles.mobileNotice}>
            📱 Mobile WebRTC video display requires custom implementation
          </Text>
          {renderOverlay()}
        </View>
      );
    }
  };

  const renderOverlay = () => (
    <View style={styles.overlay}>
      <View style={styles.topControls}>
        <TouchableOpacity style={styles.backButton} onPress={leaveStream}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>

        <View style={styles.liveIndicator}>
          <View style={[styles.liveDot, { backgroundColor: isConnected ? '#ff4444' : '#666' }]} />
          <Text style={styles.liveText}>
            {isConnected ? 'LIVE' : connectionState.toUpperCase()}
          </Text>
          <Text style={styles.viewerText}>{streamInfo.viewerCount} watching</Text>
        </View>
      </View>

      <View style={styles.centerInfo}>
        <Text style={styles.streamTitle}>{streamInfo.title}</Text>
        <Text style={styles.channelText}>Channel: {channelName}</Text>
        <Text style={styles.connectionText}>
          Connection: {connectionState}
        </Text>
      </View>

      <View style={styles.bottomControls}>
        <TouchableOpacity style={styles.controlButton}>
          <Text style={styles.controlButtonText}>💬</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.controlButton}>
          <Text style={styles.controlButtonText}>⛶</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.controlButton}>
          <Text style={styles.controlButtonText}>⚙️</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar hidden />
      {renderStreamDisplay()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
  },
  loadingText: {
    color: '#fff',
    fontSize: 18,
    marginTop: 20,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
    padding: 20,
  },
  errorIcon: {
    fontSize: 48,
    marginBottom: 20,
  },
  errorTitle: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  errorMessage: {
    color: '#ff4444',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 30,
    paddingVertical: 12,
    borderRadius: 25,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  waitingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
    padding: 20,
  },
  waitingIcon: {
    fontSize: 48,
    marginBottom: 20,
  },
  waitingTitle: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  waitingMessage: {
    color: '#ccc',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  videoContainer: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mobileNotice: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
    padding: 20,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
    padding: 20,
  },
  topControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  backButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  liveIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  liveDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  liveText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginRight: 8,
  },
  viewerText: {
    color: '#fff',
    fontSize: 12,
  },
  centerInfo: {
    alignItems: 'center',
  },
  streamTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  channelText: {
    color: '#fff',
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.8,
    marginBottom: 4,
  },
  connectionText: {
    color: '#ccc',
    fontSize: 12,
    textAlign: 'center',
    opacity: 0.6,
  },
  bottomControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  controlButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  controlButtonText: {
    fontSize: 20,
  },
});
