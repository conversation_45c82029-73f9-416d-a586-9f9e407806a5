import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  PermissionsAndroid,
  Platform,
  ActivityIndicator,
  Alert,
  AccessibilityInfo,
  Dimensions,
} from 'react-native';
import { ClientRoleType, RtcSurfaceView } from 'react-native-agora';
import AgoraService from '../services/AgoraService';
import { AGORA_CONFIG, API_CONFIG } from '../config/supabase';
import BackendService from '../services/BackendService';
import { CONFIG } from '../config/index';

const StreamViewerScreen = ({ route }: { route?: any }) => {
  const [isWatching, setIsWatching] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [audioPermission, setAudioPermission] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [viewerSessionId, setViewerSessionId] = useState<string | null>(null);
  const [streamInfo, setStreamInfo] = useState<any>(null);
  const [isMuted, setIsMuted] = useState(false);
  const [remoteUid, setRemoteUid] = useState<number | null>(null);
  const [isJoined, setIsJoined] = useState(false);
  const agoraService = useRef(new AgoraService()).current;
  const backendService = useRef(new BackendService()).current;
  const [channelName] = useState(route?.params?.channelName || 'test-channel');
  const [token] = useState(route?.params?.token || '');
  const [uid] = useState(Math.floor(Math.random() * 1000));
  const [broadcasterName] = useState(route?.params?.broadcasterName || 'Unknown');

  useEffect(() => {
    const initAgora = async () => {
      try {
        setIsLoading(true);
        setError(null);

        await agoraService.initialize(CONFIG.AGORA_APP_ID);
        await requestPermissions();

        // Check if stream is active
        const activeStream = await backendService.getActiveStream(channelName);
        if (activeStream) {
          setStreamInfo(activeStream);
        }

        // Set up event listeners for remote users
        const engine = agoraService.getEngine();
        if (engine) {
          engine.addListener('onJoinChannelSuccess', (connection, elapsed) => {
            console.log('Viewer joined channel successfully:', connection.channel);
            setIsJoined(true);
          });

          engine.addListener('onUserJoined', (connection, remoteUid, elapsed) => {
            console.log('Remote user joined:', remoteUid);
            setRemoteUid(remoteUid);
          });

          engine.addListener('onUserOffline', (connection, remoteUid, reason) => {
            console.log('Remote user left:', remoteUid);
            setRemoteUid(null);
          });
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Failed to initialize Agora:', error);
        setError('Failed to initialize viewer service');
        setIsLoading(false);
      }
    };

    initAgora();

    return () => {
      if (isWatching) {
        leaveChannel();
      }
    };
  }, []);

  const requestPermissions = async () => {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
          {
            title: 'Audio Permission',
            message: 'This app needs access to your microphone to watch streams with audio.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );

        setAudioPermission(granted === PermissionsAndroid.RESULTS.GRANTED);

        if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
          setError('Audio permission is required to watch streams');
        }
      } else {
        // iOS permissions are handled in Info.plist
        setAudioPermission(true);
      }
    } catch (err) {
      console.warn(err);
      setError('Failed to request permissions');
    }
  };

  const joinChannel = async () => {
    try {
      if (!audioPermission) {
        Alert.alert('Permission Required', 'Please grant audio permission to watch streams');
        return;
      }

      setIsLoading(true);
      setError(null);

      // Get active stream by channel name
      const activeStream = await backendService.getActiveStream(channelName);
      if (!activeStream.id) {
        setError('No active stream found for this channel');
        setIsLoading(false);
        return;
      }

      // Record viewer join
      const session = await backendService.recordViewerJoin(activeStream.id);
      setViewerSessionId(session.viewerId);

      // Join Agora channel with proper token
      await agoraService.joinChannel(token, channelName, uid, ClientRoleType.ClientRoleAudience);

      setIsWatching(true);
      setIsJoined(true);
      setIsLoading(false);

      AccessibilityInfo.announceForAccessibility('Joined stream successfully');
    } catch (error) {
      console.error('Failed to join channel:', error);
      setError('Failed to join stream. Please check the channel name, token, and your connection.');
      setIsLoading(false);
    }
  };

  const leaveChannel = async () => {
    try {
      setIsLoading(true);

      await agoraService.leaveChannel();
      setIsWatching(false);
      setIsJoined(false);
      setRemoteUid(null);

      // Record viewer leave
      if (viewerSessionId) {
        await backendService.recordViewerLeave(viewerSessionId);
      }

      setViewerSessionId(null);
      setIsLoading(false);

      AccessibilityInfo.announceForAccessibility('Left stream successfully');
    } catch (error) {
      console.error('Failed to leave channel:', error);
      setError('Failed to leave stream');
      setIsLoading(false);
    }
  };

  const toggleMute = () => {
    agoraService.toggleMute();
    setIsMuted(!isMuted);
    AccessibilityInfo.announceForAccessibility(isMuted ? 'Audio unmuted' : 'Audio muted');
  };

  const renderStreamDisplay = () => {
    if (!isWatching) {
      return (
        <View style={styles.previewContainer}>
          <Text style={styles.previewText}>Stream Preview</Text>
          {streamInfo ? (
            <View style={styles.streamInfo}>
              <Text style={styles.streamTitle}>{streamInfo.title || 'Live Stream'}</Text>
              <Text style={styles.streamerName}>By: {broadcasterName}</Text>
              <Text style={styles.viewerCount}>{streamInfo.viewerCount || 0} viewers</Text>
            </View>
          ) : (
            <Text style={styles.instructionText}>Press Join Stream to start watching</Text>
          )}
        </View>
      );
    }

    if (!remoteUid) {
      return (
        <View style={styles.videoContainer}>
          <View style={styles.waitingContainer}>
            <ActivityIndicator size="large" color="#007AFF" />
            <Text style={styles.waitingText}>Waiting for broadcaster to join...</Text>
          </View>
        </View>
      );
    }

    return (
      <View style={styles.videoContainer}>
        <RtcSurfaceView style={styles.videoView} canvas={{ uid: remoteUid }} />
        <View style={styles.overlay}>
          <View style={styles.streamHeader}>
            <Text style={styles.streamTitleLive}>LIVE</Text>
            <Text style={styles.streamTitle}>{streamInfo?.title || channelName}</Text>
          </View>
          <View style={styles.streamFooter}>
            <Text style={styles.viewerCountLive}>{streamInfo?.viewerCount || 0} watching</Text>
          </View>
        </View>
      </View>
    );
  };

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => setError(null)}
          accessibilityLabel="Retry"
        >
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title} accessibilityRole="header">
        Stream Viewer
      </Text>
      <Text style={styles.channelText}>Channel: {channelName}</Text>

      {renderStreamDisplay()}

      {isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Processing...</Text>
        </View>
      )}

      <View style={styles.controls}>
        <TouchableOpacity
          style={[styles.button, isWatching ? styles.leaveButton : styles.joinButton]}
          onPress={isWatching ? leaveChannel : joinChannel}
          disabled={isLoading}
          accessibilityLabel={isWatching ? 'Leave Stream' : 'Join Stream'}
        >
          <Text style={styles.buttonText}>
            {isWatching ? 'Leave Stream' : 'Join Stream'}
          </Text>
        </TouchableOpacity>

        {isWatching && (
          <TouchableOpacity
            style={[styles.controlButton, isMuted && styles.mutedButton]}
            onPress={toggleMute}
            accessibilityLabel={isMuted ? 'Unmute audio' : 'Mute audio'}
          >
            <Text style={styles.controlButtonText}>
              {isMuted ? '🔇' : '🔊'}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Start Stream Button */}
      <View style={styles.startStreamContainer}>
        <TouchableOpacity
          style={styles.startStreamButton}
          onPress={() => {
            // Navigate to broadcast screen with same channel
            Alert.alert(
              'Start Broadcasting',
              'Do you want to start your own stream on this channel?',
              [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'Start Stream',
                  onPress: () => {
                    // In a real app with navigation, you would do:
                    // navigation.navigate('StreamBroadcast', { channelName });
                    Alert.alert(
                      'Start Broadcasting',
                      `Ready to start broadcasting on channel: ${channelName}`,
                      [
                        { text: 'OK', style: 'default' }
                      ]
                    );
                  }
                }
              ]
            );
          }}
          accessibilityLabel="Start your own stream"
        >
          <Text style={styles.startStreamButtonText}>📹 Start Your Stream</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  channelText: {
    fontSize: 16,
    marginBottom: 20,
    color: '#666',
  },
  previewContainer: {
    width: '100%',
    height: 300,
    backgroundColor: '#000',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  previewText: {
    color: '#fff',
    fontSize: 18,
    marginBottom: 10,
  },
  instructionText: {
    color: '#aaa',
    fontSize: 14,
  },
  videoContainer: {
    width: '100%',
    height: 300,
    borderRadius: 10,
    overflow: 'hidden',
    marginBottom: 20,
    position: 'relative',
  },
  videoView: {
    flex: 1,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  streamHeader: {
    position: 'absolute',
    top: 10,
    left: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  streamTitleLive: {
    backgroundColor: '#FF3B30',
    color: '#fff',
    padding: 5,
    borderRadius: 3,
    fontSize: 12,
    fontWeight: 'bold',
    marginRight: 10,
  },
  streamTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textShadowColor: 'rgba(0,0,0,0.8)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  streamFooter: {
    position: 'absolute',
    bottom: 10,
    left: 10,
  },
  viewerCountLive: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    color: '#fff',
    padding: 5,
    borderRadius: 5,
    fontSize: 12,
  },
  streamInfo: {
    alignItems: 'center',
  },
  streamerName: {
    color: '#fff',
    fontSize: 16,
    marginBottom: 5,
  },
  viewerCount: {
    color: '#aaa',
    fontSize: 14,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  button: {
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    minWidth: 120,
    alignItems: 'center',
  },
  joinButton: {
    backgroundColor: '#007AFF',
  },
  leaveButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  controlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
  mutedButton: {
    backgroundColor: '#FF3B30',
  },
  controlButtonText: {
    fontSize: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#FF3B30',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#007AFF',
    borderRadius: 5,
  },
  retryText: {
    color: '#fff',
    fontSize: 16,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.8)',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  waitingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  waitingText: {
    color: '#fff',
    fontSize: 16,
    marginTop: 10,
  },
  startStreamContainer: {
    marginTop: 20,
    width: '100%',
    alignItems: 'center',
  },
  startStreamButton: {
    backgroundColor: '#FF6B35',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  startStreamButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default StreamViewerScreen;