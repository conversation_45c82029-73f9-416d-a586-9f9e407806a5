import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  TextInput,
  Alert,
  StatusBar,
  Dimensions
} from 'react-native';
import { Camera } from 'expo-camera';
import WebRTCBroadcastScreen from './src/components/WebRTCBroadcastScreen';
import WebRTCViewerScreen from './src/components/WebRTCViewerScreen';

const { width, height } = Dimensions.get('window');

export default function App() {
  const [currentScreen, setCurrentScreen] = useState('home'); // 'home', 'viewer', 'broadcaster'
  const [channelName, setChannelName] = useState('');
  const [hasPermission, setHasPermission] = useState(null);
  const [cameraType, setCameraType] = useState(Camera.Constants.Type.back);
  const [isStreaming, setIsStreaming] = useState(false);
  const [viewerCount, setViewerCount] = useState(Math.floor(Math.random() * 1000) + 1);

  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
  }, []);

  // Simulate viewer count updates
  useEffect(() => {
    if (currentScreen === 'viewer' || isStreaming) {
      const interval = setInterval(() => {
        setViewerCount(prev => prev + Math.floor(Math.random() * 5) - 2);
      }, 3000);
      return () => clearInterval(interval);
    }
  }, [currentScreen, isStreaming]);

  const startStreaming = () => {
    if (!channelName.trim()) {
      Alert.alert('Error', 'Please enter a channel name');
      return;
    }
    setCurrentScreen('webrtc-broadcaster');
  };

  const joinStream = () => {
    if (!channelName.trim()) {
      Alert.alert('Error', 'Please enter a channel name');
      return;
    }
    setCurrentScreen('webrtc-viewer');
  };

  const stopStreaming = () => {
    setIsStreaming(false);
    setCurrentScreen('home');
  };

  const leaveStream = () => {
    setCurrentScreen('home');
  };

  const toggleCamera = () => {
    setCameraType(
      cameraType === Camera.Constants.Type.back
        ? Camera.Constants.Type.front
        : Camera.Constants.Type.back
    );
  };

  // Home Screen
  if (currentScreen === 'home') {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#1a1a1a" />

        <View style={styles.header}>
          <Text style={styles.appTitle}>📺 Live Stream</Text>
          <Text style={styles.subtitle}>Real-time video streaming</Text>
        </View>

        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            placeholder="Enter channel name"
            placeholderTextColor="#666"
            value={channelName}
            onChangeText={setChannelName}
          />
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.broadcastButton} onPress={startStreaming}>
            <Text style={styles.buttonIcon}>📹</Text>
            <Text style={styles.buttonText}>Start Broadcasting</Text>
            <Text style={styles.buttonSubtext}>Go live now</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.viewButton} onPress={joinStream}>
            <Text style={styles.buttonIcon}>👁️</Text>
            <Text style={styles.buttonText}>Watch Stream</Text>
            <Text style={styles.buttonSubtext}>Join live stream</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>🔴 Live streaming powered by React Native</Text>
        </View>
      </View>
    );
  }

  // Stream Viewer Screen
  if (currentScreen === 'viewer') {
    return (
      <View style={styles.fullScreenContainer}>
        <StatusBar hidden />

        {/* Mock Video Stream */}
        <View style={styles.videoContainer}>
          <View style={styles.mockVideo}>
            <Text style={styles.mockVideoText}>📺</Text>
            <Text style={styles.mockVideoLabel}>Live Stream</Text>
            <Text style={styles.channelLabel}>Channel: {channelName}</Text>
          </View>
        </View>

        {/* Stream Controls Overlay */}
        <View style={styles.overlay}>
          <View style={styles.topControls}>
            <TouchableOpacity style={styles.backButton} onPress={leaveStream}>
              <Text style={styles.backButtonText}>← Back</Text>
            </TouchableOpacity>

            <View style={styles.liveIndicator}>
              <View style={styles.liveDot} />
              <Text style={styles.liveText}>LIVE</Text>
              <Text style={styles.viewerText}>{viewerCount} viewers</Text>
            </View>
          </View>

          <View style={styles.bottomControls}>
            <TouchableOpacity style={styles.fullScreenButton}>
              <Text style={styles.controlButtonText}>⛶</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.chatButton}>
              <Text style={styles.controlButtonText}>💬</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  // Stream Broadcaster Screen
  if (currentScreen === 'broadcaster') {
    if (hasPermission === null) {
      return (
        <View style={styles.container}>
          <Text style={styles.permissionText}>Requesting camera permission...</Text>
        </View>
      );
    }

    if (hasPermission === false) {
      return (
        <View style={styles.container}>
          <Text style={styles.permissionText}>No access to camera</Text>
          <TouchableOpacity style={styles.backButton} onPress={stopStreaming}>
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.fullScreenContainer}>
        <StatusBar hidden />

        {/* Camera View */}
        <Camera style={styles.camera} type={cameraType}>
          <View style={styles.cameraOverlay}>
            <View style={styles.topControls}>
              <TouchableOpacity style={styles.backButton} onPress={stopStreaming}>
                <Text style={styles.backButtonText}>← Stop</Text>
              </TouchableOpacity>

              <View style={styles.liveIndicator}>
                <View style={styles.liveDot} />
                <Text style={styles.liveText}>LIVE</Text>
                <Text style={styles.viewerText}>{viewerCount} viewers</Text>
              </View>
            </View>

            <View style={styles.centerInfo}>
              <Text style={styles.streamingText}>🔴 Broadcasting Live</Text>
              <Text style={styles.channelText}>Channel: {channelName}</Text>
            </View>

            <View style={styles.bottomControls}>
              <TouchableOpacity style={styles.flipButton} onPress={toggleCamera}>
                <Text style={styles.controlButtonText}>🔄</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.stopButton} onPress={stopStreaming}>
                <Text style={styles.stopButtonText}>⏹️ Stop</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.settingsButton}>
                <Text style={styles.controlButtonText}>⚙️</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Camera>
      </View>
    );
  }

  // WebRTC Broadcast Screen
  if (currentScreen === 'webrtc-broadcaster') {
    return (
      <WebRTCBroadcastScreen
        channelName={channelName}
        onStop={stopStreaming}
      />
    );
  }

  // WebRTC Viewer Screen
  if (currentScreen === 'webrtc-viewer') {
    return (
      <WebRTCViewerScreen
        channelName={channelName}
        onLeave={leaveStream}
      />
    );
  }

  return null;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenContainer: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    alignItems: 'center',
    marginBottom: 50,
  },
  appTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#888',
  },
  inputContainer: {
    width: '80%',
    marginBottom: 40,
  },
  input: {
    backgroundColor: '#2a2a2a',
    color: '#fff',
    padding: 15,
    borderRadius: 10,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#444',
  },
  buttonContainer: {
    width: '80%',
    gap: 20,
  },
  broadcastButton: {
    backgroundColor: '#ff4444',
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
  },
  viewButton: {
    backgroundColor: '#4444ff',
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
  },
  buttonIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  buttonSubtext: {
    color: '#ccc',
    fontSize: 14,
  },
  footer: {
    position: 'absolute',
    bottom: 50,
  },
  footerText: {
    color: '#666',
    fontSize: 12,
  },
  videoContainer: {
    flex: 1,
  },
  mockVideo: {
    flex: 1,
    backgroundColor: '#333',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mockVideoText: {
    fontSize: 80,
    marginBottom: 20,
  },
  mockVideoLabel: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  channelLabel: {
    color: '#ccc',
    fontSize: 16,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
  },
  camera: {
    flex: 1,
  },
  cameraOverlay: {
    flex: 1,
    justifyContent: 'space-between',
  },
  topControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 50,
  },
  backButton: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  liveIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  liveDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ff4444',
    marginRight: 6,
  },
  liveText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    marginRight: 8,
  },
  viewerText: {
    color: '#ccc',
    fontSize: 12,
  },
  centerInfo: {
    alignItems: 'center',
  },
  streamingText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  channelText: {
    color: '#ccc',
    fontSize: 14,
  },
  bottomControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 40,
  },
  fullScreenButton: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  chatButton: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  flipButton: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stopButton: {
    backgroundColor: '#ff4444',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 25,
  },
  stopButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  settingsButton: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlButtonText: {
    color: '#fff',
    fontSize: 20,
  },
  permissionText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
});
