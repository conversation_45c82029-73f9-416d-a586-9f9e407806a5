import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Button, TextInput, Alert, ActivityIndicator, Text, TouchableOpacity } from 'react-native';
import SignInScreen from './src/components/SignInScreen';
import SignUpScreen from './src/components/SignUpScreen';
import StreamBroadcastScreen from './src/components/StreamBroadcastScreen';
import StreamViewerScreen from './src/components/StreamViewerScreen';
import FullScreenStreamViewer from './src/components/FullScreenStreamViewer';
import FullScreenStreamBroadcast from './src/components/FullScreenStreamBroadcast';
import AgoraService from './src/services/AgoraService';
import AuthManager, { User } from './src/services/AuthManager';
import { CONFIG } from './src/config/index';

export default function App() {
  const [user, setUser] = useState<User | null>(null);
  const [showSignUp, setShowSignUp] = useState(false);
  const [isBroadcasting, setIsBroadcasting] = useState(false);
  const [isJoined, setIsJoined] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [token, setToken] = useState('');
  const [channelName, setChannelName] = useState('');
  const [showFullScreenViewer, setShowFullScreenViewer] = useState(false);
  const [showFullScreenBroadcast, setShowFullScreenBroadcast] = useState(false);
  const agoraService = useRef(new AgoraService()).current;

  useEffect(() => {
    const initializeApp = async () => {
      try {
        checkAuthStatus();
        await agoraService.initialize(CONFIG.AGORA_APP_ID);
      } catch (error) {
        console.error('Failed to initialize Agora:', error);
        setError('Failed to initialize streaming service');
      }
    };

    initializeApp();

    return () => {
      agoraService.destroy();
    };
  }, []);

  const checkAuthStatus = async () => {
    try {
      console.log('Checking auth status...');
      const { user: currentUser } = await AuthManager.getInstance().getCurrentUser();
      console.log('Auth check result:', currentUser);
      setUser(currentUser);
    } catch (error) {
      console.error('Auth check failed:', error);
      setError('Failed to check authentication status');
    } finally {
      setLoading(false);
    }
  };

  const handleSignIn = (signedInUser: User) => {
    setUser(signedInUser);
  };

  const handleSignUp = (signedUpUser: User) => {
    setUser(signedUpUser);
    setShowSignUp(false);
  };

  const handleSignOut = async () => {
    try {
      await AuthManager.getInstance().signOut();
      setUser(null);
      setIsJoined(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to sign out');
    }
  };

  const joinChannel = async () => {
    if (token && channelName) {
      try {
        await agoraService.joinChannel(token, channelName, 0, 2); // ClientRoleType.ClientRoleAudience
        setIsJoined(true);
      } catch (error) {
        Alert.alert('Error', 'Failed to join channel');
      }
    }
  };

  const leaveChannel = async () => {
    try {
      await agoraService.leaveChannel();
      setIsJoined(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to leave channel');
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>{error}</Text>
        <Button title="Retry" onPress={() => {
          setLoading(true);
          setError(null);
          checkAuthStatus();
        }} />
      </View>
    );
  }

  if (!user) {
    return (
      <View style={styles.container}>
        {showSignUp ? (
          <SignUpScreen onSignUp={handleSignUp} />
        ) : (
          <SignInScreen onSignIn={handleSignIn} />
        )}
        <Button
          title={showSignUp ? 'Already have an account? Sign In' : 'Need an account? Sign Up'}
          onPress={() => setShowSignUp(!showSignUp)}
        />
      </View>
    );
  }

  // Show full-screen components if requested
  if (showFullScreenViewer) {
    return (
      <FullScreenStreamViewer
        channelName={channelName || 'test-channel'}
        onClose={() => setShowFullScreenViewer(false)}
        onStartBroadcast={() => {
          setShowFullScreenViewer(false);
          setShowFullScreenBroadcast(true);
        }}
      />
    );
  }

  if (showFullScreenBroadcast) {
    return (
      <FullScreenStreamBroadcast
        channelName={channelName}
        onClose={() => setShowFullScreenBroadcast(false)}
      />
    );
  }

  if (!isJoined) {
    return (
      <View style={styles.container}>
        <TextInput
          style={styles.input}
          placeholder="Token"
          value={token}
          onChangeText={setToken}
        />
        <TextInput
          style={styles.input}
          placeholder="Channel Name"
          value={channelName}
          onChangeText={setChannelName}
        />
        <Button
          title="Join Channel"
          onPress={joinChannel}
          disabled={!token || !channelName}
        />

        {/* Full-Screen Options */}
        <View style={styles.fullScreenOptions}>
          <Text style={styles.sectionTitle}>Full-Screen Experience</Text>

          <TouchableOpacity
            style={styles.fullScreenButton}
            onPress={() => setShowFullScreenViewer(true)}
          >
            <Text style={styles.fullScreenButtonText}>📺 Watch Stream (Full-Screen)</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.fullScreenButton, styles.broadcastButton]}
            onPress={() => setShowFullScreenBroadcast(true)}
          >
            <Text style={styles.fullScreenButtonText}>📹 Start Broadcasting (Full-Screen)</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {isBroadcasting ?
        <StreamBroadcastScreen route={{ params: { channelName, token } }} /> :
        <StreamViewerScreen route={{ params: { channelName, token } }} />
      }
      <Button title="Leave Channel" onPress={leaveChannel} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorText: {
    fontSize: 16,
    color: '#ff0000',
    textAlign: 'center',
    margin: 20,
  },
  input: {
    height: 40,
    borderColor: 'gray',
    borderWidth: 1,
    marginBottom: 10,
    paddingHorizontal: 10,
    width: '80%',
  },
  fullScreenOptions: {
    marginTop: 30,
    width: '80%',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  fullScreenButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderRadius: 10,
    marginBottom: 10,
    width: '100%',
    alignItems: 'center',
  },
  broadcastButton: {
    backgroundColor: '#FF6B35',
  },
  fullScreenButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});