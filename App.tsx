import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';

export default function App() {
  console.log('🎉 App component is rendering!');
  
  // Simple test to verify app is working
  return (
    <View style={styles.container}>
      <Text style={styles.testTitle}>🎉 Streaming App is Working!</Text>
      <Text style={styles.testSubtitle}>Connection successful</Text>
      <TouchableOpacity style={styles.testButton}>
        <Text style={styles.testButtonText}>App is running perfectly!</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f8ff',
  },
  testTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 10,
    textAlign: 'center',
  },
  testSubtitle: {
    fontSize: 18,
    color: '#666',
    marginBottom: 40,
    textAlign: 'center',
  },
  testButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    marginVertical: 10,
  },
  testButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
