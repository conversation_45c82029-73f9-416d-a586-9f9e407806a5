{"version": 3, "names": ["NativeModules", "PermissionsAndroid", "Platform", "WebRTCModule", "Permissions", "constructor", "_defineProperty", "DENIED", "GRANTED", "PROMPT", "Promise", "resolve", "_requestPermissionAndroid", "perm", "request", "then", "granted", "RESULTS", "_validatePermissionDescriptior", "permissionDesc", "TypeError", "name", "VALID_PERMISSIONS", "indexOf", "query", "e", "reject", "OS", "PERMISSIONS", "CAMERA", "RECORD_AUDIO", "check", "RESULT", "checkPermission", "requestPermission", "_lastReq"], "sources": ["Permissions.ts"], "sourcesContent": ["\nimport { NativeModules, Permission, PermissionsAndroid, Platform } from 'react-native';\n\nconst { WebRTCModule } = NativeModules;\n\n/**\n * Type declaration for a permissions descriptor.\n */\ntype PermissionDescriptor = {\n    name: string;\n};\n\n/**\n * Class implementing a subset of W3C's Permissions API as defined by:\n * https://www.w3.org/TR/permissions/\n */\nclass Permissions {\n    /**\n     * Possible result values for {@link query}, in accordance with:\n     * https://www.w3.org/TR/permissions/#status-of-a-permission\n     */\n    RESULT = {\n        DENIED: 'denied',\n        GRANTED: 'granted',\n        PROMPT: 'prompt'\n    };\n\n    /**\n     * This implementation only supports requesting these permissions, a subset\n     * of: https://www.w3.org/TR/permissions/#permission-registry\n     */\n    VALID_PERMISSIONS = [ 'camera', 'microphone' ];\n\n    _lastReq: Promise<unknown> = Promise.resolve();\n\n    /**\n     * Helper for requesting Android permissions. On Android only one permission\n     * can be requested at a time (unless the multi-permission API is used,\n     * but we are not using that for symmetry with the W3C API for querying)\n     * so we'll queue them up.\n     *\n     * @param perm - The requested permission from\n     * {@link PermissionsAndroid.PERMISSIONS}\n     * https://facebook.github.io/react-native/docs/permissionsandroid#permissions-that-require-prompting-the-user\n     */\n    _requestPermissionAndroid(perm: Permission) {\n        return new Promise(resolve => {\n            PermissionsAndroid.request(perm).then(\n                granted => resolve(granted === PermissionsAndroid.RESULTS.GRANTED),\n                () => resolve(false)\n            );\n        });\n    }\n\n    /**\n     * Validates the given permission descriptor.\n     */\n    _validatePermissionDescriptior(permissionDesc) {\n        if (typeof permissionDesc !== 'object') {\n            throw new TypeError('Argument 1 of Permissions.query is not an object.');\n        }\n\n        if (typeof permissionDesc.name === 'undefined') {\n            throw new TypeError('Missing required \\'name\\' member of PermissionDescriptor.');\n        }\n\n        if (this.VALID_PERMISSIONS.indexOf(permissionDesc.name) === -1) {\n            throw new TypeError(\n                '\\'name\\' member of PermissionDescriptor is not a valid value for enumeration PermissionName.'\n            );\n        }\n    }\n\n    /**\n     * Method for querying the status of a permission, according to:\n     * https://www.w3.org/TR/permissions/#permissions-interface\n     */\n    query(permissionDesc: PermissionDescriptor) {\n        try {\n            this._validatePermissionDescriptior(permissionDesc);\n        } catch (e) {\n            return Promise.reject(e);\n        }\n\n        if (Platform.OS === 'android') {\n            const perm =\n                permissionDesc.name === 'camera'\n                    ? PermissionsAndroid.PERMISSIONS.CAMERA\n                    : PermissionsAndroid.PERMISSIONS.RECORD_AUDIO;\n\n            return new Promise(resolve => {\n                PermissionsAndroid.check(perm).then(\n                    granted => resolve(granted ? this.RESULT.GRANTED : this.RESULT.PROMPT),\n                    () => resolve(this.RESULT.PROMPT)\n                );\n            });\n        } else if (Platform.OS === 'ios' || Platform.OS === 'macos') {\n            return WebRTCModule.checkPermission(permissionDesc.name);\n        } else {\n            return Promise.reject(new TypeError('Unsupported platform.'));\n        }\n    }\n\n    /**\n     * Custom method NOT defined by W3C's permissions API, which allows the\n     * caller to request a permission.\n     */\n    request(permissionDesc: PermissionDescriptor) {\n        try {\n            this._validatePermissionDescriptior(permissionDesc);\n        } catch (e) {\n            return Promise.reject(e);\n        }\n\n        if (Platform.OS === 'android') {\n            const perm =\n                permissionDesc.name === 'camera'\n                    ? PermissionsAndroid.PERMISSIONS.CAMERA\n                    : PermissionsAndroid.PERMISSIONS.RECORD_AUDIO;\n            const requestPermission = () => this._requestPermissionAndroid(perm);\n\n            this._lastReq = this._lastReq.then(requestPermission, requestPermission);\n\n            return this._lastReq;\n        } else if (Platform.OS === 'ios' || Platform.OS === 'macos') {\n            return WebRTCModule.requestPermission(permissionDesc.name);\n        } else {\n            return Promise.reject(new TypeError('Unsupported platform.'));\n        }\n    }\n}\n\nexport default new Permissions();\n"], "mappings": ";AACA,SAASA,aAAa,EAAcC,kBAAkB,EAAEC,QAAQ,QAAQ,cAAc;AAEtF,MAAM;EAAEC;AAAa,CAAC,GAAGH,aAAa;;AAEtC;AACA;AACA;;AAKA;AACA;AACA;AACA;AACA,MAAMI,WAAW,CAAC;EAAAC,YAAA;IAAAC,eAAA,iBAKL;MACLC,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE;IACZ,CAAC;IAAAH,eAAA,4BAMmB,CAAE,QAAQ,EAAE,YAAY,CAAE;IAAAA,eAAA,mBAEjBI,OAAO,CAACC,OAAO,CAAC,CAAC;EAAA;EAhB9C;AACJ;AACA;AACA;EAOI;AACJ;AACA;AACA;EAKI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,yBAAyBA,CAACC,IAAgB,EAAE;IACxC,OAAO,IAAIH,OAAO,CAACC,OAAO,IAAI;MAC1BV,kBAAkB,CAACa,OAAO,CAACD,IAAI,CAAC,CAACE,IAAI,CACjCC,OAAO,IAAIL,OAAO,CAACK,OAAO,KAAKf,kBAAkB,CAACgB,OAAO,CAACT,OAAO,CAAC,EAClE,MAAMG,OAAO,CAAC,KAAK,CACvB,CAAC;IACL,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;EACIO,8BAA8BA,CAACC,cAAc,EAAE;IAC3C,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE;MACpC,MAAM,IAAIC,SAAS,CAAC,mDAAmD,CAAC;IAC5E;IAEA,IAAI,OAAOD,cAAc,CAACE,IAAI,KAAK,WAAW,EAAE;MAC5C,MAAM,IAAID,SAAS,CAAC,2DAA2D,CAAC;IACpF;IAEA,IAAI,IAAI,CAACE,iBAAiB,CAACC,OAAO,CAACJ,cAAc,CAACE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;MAC5D,MAAM,IAAID,SAAS,CACf,8FACJ,CAAC;IACL;EACJ;;EAEA;AACJ;AACA;AACA;EACII,KAAKA,CAACL,cAAoC,EAAE;IACxC,IAAI;MACA,IAAI,CAACD,8BAA8B,CAACC,cAAc,CAAC;IACvD,CAAC,CAAC,OAAOM,CAAC,EAAE;MACR,OAAOf,OAAO,CAACgB,MAAM,CAACD,CAAC,CAAC;IAC5B;IAEA,IAAIvB,QAAQ,CAACyB,EAAE,KAAK,SAAS,EAAE;MAC3B,MAAMd,IAAI,GACNM,cAAc,CAACE,IAAI,KAAK,QAAQ,GAC1BpB,kBAAkB,CAAC2B,WAAW,CAACC,MAAM,GACrC5B,kBAAkB,CAAC2B,WAAW,CAACE,YAAY;MAErD,OAAO,IAAIpB,OAAO,CAACC,OAAO,IAAI;QAC1BV,kBAAkB,CAAC8B,KAAK,CAAClB,IAAI,CAAC,CAACE,IAAI,CAC/BC,OAAO,IAAIL,OAAO,CAACK,OAAO,GAAG,IAAI,CAACgB,MAAM,CAACxB,OAAO,GAAG,IAAI,CAACwB,MAAM,CAACvB,MAAM,CAAC,EACtE,MAAME,OAAO,CAAC,IAAI,CAACqB,MAAM,CAACvB,MAAM,CACpC,CAAC;MACL,CAAC,CAAC;IACN,CAAC,MAAM,IAAIP,QAAQ,CAACyB,EAAE,KAAK,KAAK,IAAIzB,QAAQ,CAACyB,EAAE,KAAK,OAAO,EAAE;MACzD,OAAOxB,YAAY,CAAC8B,eAAe,CAACd,cAAc,CAACE,IAAI,CAAC;IAC5D,CAAC,MAAM;MACH,OAAOX,OAAO,CAACgB,MAAM,CAAC,IAAIN,SAAS,CAAC,uBAAuB,CAAC,CAAC;IACjE;EACJ;;EAEA;AACJ;AACA;AACA;EACIN,OAAOA,CAACK,cAAoC,EAAE;IAC1C,IAAI;MACA,IAAI,CAACD,8BAA8B,CAACC,cAAc,CAAC;IACvD,CAAC,CAAC,OAAOM,CAAC,EAAE;MACR,OAAOf,OAAO,CAACgB,MAAM,CAACD,CAAC,CAAC;IAC5B;IAEA,IAAIvB,QAAQ,CAACyB,EAAE,KAAK,SAAS,EAAE;MAC3B,MAAMd,IAAI,GACNM,cAAc,CAACE,IAAI,KAAK,QAAQ,GAC1BpB,kBAAkB,CAAC2B,WAAW,CAACC,MAAM,GACrC5B,kBAAkB,CAAC2B,WAAW,CAACE,YAAY;MACrD,MAAMI,iBAAiB,GAAGA,CAAA,KAAM,IAAI,CAACtB,yBAAyB,CAACC,IAAI,CAAC;MAEpE,IAAI,CAACsB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACpB,IAAI,CAACmB,iBAAiB,EAAEA,iBAAiB,CAAC;MAExE,OAAO,IAAI,CAACC,QAAQ;IACxB,CAAC,MAAM,IAAIjC,QAAQ,CAACyB,EAAE,KAAK,KAAK,IAAIzB,QAAQ,CAACyB,EAAE,KAAK,OAAO,EAAE;MACzD,OAAOxB,YAAY,CAAC+B,iBAAiB,CAACf,cAAc,CAACE,IAAI,CAAC;IAC9D,CAAC,MAAM;MACH,OAAOX,OAAO,CAACgB,MAAM,CAAC,IAAIN,SAAS,CAAC,uBAAuB,CAAC,CAAC;IACjE;EACJ;AACJ;AAEA,eAAe,IAAIhB,WAAW,CAAC,CAAC"}