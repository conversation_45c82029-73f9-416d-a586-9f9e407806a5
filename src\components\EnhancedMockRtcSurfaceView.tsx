import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Dimensions, Animated } from 'react-native';
import { Camera } from 'expo-camera';

interface EnhancedMockRtcSurfaceViewProps {
  style?: any;
  canvas?: {
    uid: number;
    renderMode?: number;
    mirrorMode?: number;
  };
  isLocal?: boolean;
}

const EnhancedMockRtcSurfaceView: React.FC<EnhancedMockRtcSurfaceViewProps> = ({
  style,
  canvas,
  isLocal = false
}) => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [cameraType, setCameraType] = useState(Camera.Constants.Type.front);
  const [showCamera, setShowCamera] = useState(false);
  const fadeAnim = new Animated.Value(0);

  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
      
      if (status === 'granted' && isLocal) {
        // Show camera for local streams (broadcaster)
        setShowCamera(true);
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }).start();
      }
    })();
  }, [isLocal]);

  const renderLocalCamera = () => {
    if (!hasPermission) {
      return (
        <View style={[styles.container, style]}>
          <Text style={styles.permissionText}>Camera permission required</Text>
        </View>
      );
    }

    if (hasPermission === null) {
      return (
        <View style={[styles.container, style]}>
          <Text style={styles.loadingText}>Requesting camera permission...</Text>
        </View>
      );
    }

    return (
      <Animated.View style={[styles.container, style, { opacity: fadeAnim }]}>
        <Camera
          style={styles.camera}
          type={cameraType}
          ratio="16:9"
        >
          <View style={styles.overlay}>
            <View style={styles.liveIndicator}>
              <Text style={styles.liveText}>● LIVE</Text>
            </View>
            <Text style={styles.uidText}>You (Broadcasting)</Text>
          </View>
        </Camera>
      </Animated.View>
    );
  };

  const renderRemoteStream = () => {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.mockVideoContainer}>
          {/* Animated background to simulate video */}
          <View style={styles.animatedBackground} />
          
          <View style={styles.overlay}>
            <View style={styles.liveIndicator}>
              <Text style={styles.liveText}>● LIVE</Text>
            </View>
            <Text style={styles.uidText}>📹 User {canvas?.uid || 'Unknown'}</Text>
          </View>
          
          {/* Simulate video content */}
          <View style={styles.videoSimulation}>
            <Text style={styles.videoText}>Live Stream</Text>
            <Text style={styles.qualityText}>HD • Real-time</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderPlaceholder = () => {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.placeholderContainer}>
          <Text style={styles.placeholderText}>
            {isLocal ? 'Initializing camera...' : 'Connecting to stream...'}
          </Text>
        </View>
      </View>
    );
  };

  // Determine what to render based on props
  if (isLocal && showCamera) {
    return renderLocalCamera();
  } else if (!isLocal && canvas?.uid) {
    return renderRemoteStream();
  } else {
    return renderPlaceholder();
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  camera: {
    flex: 1,
    width: '100%',
  },
  overlay: {
    position: 'absolute',
    top: 20,
    left: 20,
    right: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  liveIndicator: {
    backgroundColor: 'rgba(255, 0, 0, 0.8)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  liveText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  uidText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  mockVideoContainer: {
    flex: 1,
    width: '100%',
    position: 'relative',
  },
  animatedBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#1a1a1a',
  },
  videoSimulation: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  qualityText: {
    color: '#ccc',
    fontSize: 16,
  },
  placeholderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  placeholderText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
  },
  permissionText: {
    color: '#ff6b6b',
    fontSize: 16,
    textAlign: 'center',
  },
  loadingText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
  },
});

export default EnhancedMockRtcSurfaceView;
