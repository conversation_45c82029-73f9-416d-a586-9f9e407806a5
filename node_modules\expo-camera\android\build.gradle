apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'maven-publish'

group = 'host.exp.exponent'
version = '13.4.4'

buildscript {
  def expoModulesCorePlugin = new File(project(":expo-modules-core").projectDir.absolutePath, "ExpoModulesCorePlugin.gradle")
  if (expoModulesCorePlugin.exists()) {
    apply from: expoModulesCorePlugin
    applyKotlinExpoModulesCorePlugin()
  }

  // Simple helper that allows the root project to override versions declared by this library.
  ext.safeExtGet = { prop, fallback ->
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
  }

  // Ensures backward compatibility
  ext.getKotlinVersion = {
    if (ext.has("kotlinVersion")) {
      ext.kotlinVersion()
    } else {
      ext.safeExtGet("kotlinVersion", "1.8.10")
    }
  }

  repositories {
    mavenCentral()
  }

  dependencies {
    classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:${getKotlinVersion()}")
  }
}

afterEvaluate {
  publishing {
    publications {
      release(MavenPublication) {
        from components.release
      }
    }
    repositories {
      maven {
        url = mavenLocal().url
      }
    }
  }
}

android {
  compileSdkVersion safeExtGet("compileSdkVersion", 33)

  compileOptions {
    sourceCompatibility JavaVersion.VERSION_11
    targetCompatibility JavaVersion.VERSION_11
  }

  kotlinOptions {
    jvmTarget = JavaVersion.VERSION_11.majorVersion
  }

  namespace "expo.modules.camera"
  defaultConfig {
    minSdkVersion safeExtGet("minSdkVersion", 21)
    targetSdkVersion safeExtGet("targetSdkVersion", 33)
    versionCode 32
    versionName "13.4.4"
  }

  lintOptions {
    abortOnError false
  }
  publishing {
    singleVariant("release") {
      withSourcesJar()
    }
  }
}

repositories {
  mavenCentral()
  maven {
    url "$projectDir/maven"
  }
}

dependencies {
  implementation project(':expo-modules-core')

  api "androidx.exifinterface:exifinterface:1.0.0"
  api 'com.google.android:cameraview:1.0.0'

  implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:${getKotlinVersion()}"
}
