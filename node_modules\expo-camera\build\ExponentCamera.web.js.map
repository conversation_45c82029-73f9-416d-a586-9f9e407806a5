{"version": 3, "file": "ExponentCamera.web.js", "sourceRoot": "", "sources": ["../src/ExponentCamera.web.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAC/C,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAa,UAAU,EAAE,IAAI,EAAa,MAAM,cAAc,CAAC;AACtE,OAAO,aAAa,MAAM,6CAA6C,CAAC;AAExE,OAAO,EAIL,UAAU,GACX,MAAM,gBAAgB,CAAC;AACxB,OAAO,aAAa,MAAM,6BAA6B,CAAC;AACxD,OAAO,EAAE,OAAO,EAAE,MAAM,kBAAkB,CAAC;AAC3C,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AASpD,MAAM,cAAc,GAAG,KAAK,CAAC,UAAU,CACrC,CACE,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,KAAK,EAAsD,EAC3F,GAAiC,EACjC,EAAE;IACF,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAA0B,IAAI,CAAC,CAAC;IAE1D,MAAM,MAAM,GAAG,kBAAkB,CAAC,KAAK,EAAE,IAAkB,EAAE,KAAK,EAAE;QAClE,aAAa;YACX,IAAI,KAAK,CAAC,aAAa,EAAE;gBACvB,KAAK,CAAC,aAAa,EAAE,CAAC;aACvB;QACH,CAAC;QACD,YAAY,EAAE,KAAK,CAAC,YAAY;KACjC,CAAC,CAAC;IAEH,MAAM,kBAAkB,GAAG,KAAK,CAAC,OAAO,CAAU,GAAG,EAAE;QACrD,OAAO,CAAC,CAAC,CACP,KAAK,CAAC,sBAAsB,EAAE,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,gBAAgB,CACvF,CAAC;IACJ,CAAC,EAAE,CAAC,KAAK,CAAC,sBAAsB,EAAE,YAAY,EAAE,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAEzE,eAAe,CAAC,KAAK,EAAE;QACrB,QAAQ,EAAE,KAAK,CAAC,sBAAsB,EAAE,QAAQ;QAChD,SAAS,EAAE,kBAAkB;QAC7B,cAAc,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,aAAa,EAAE,MAAM,CAAC,IAAI,KAAK,UAAU,CAAC,KAAK,EAAE;QAC7E,SAAS,CAAC,KAAK;YACb,IAAI,KAAK,CAAC,gBAAgB,EAAE;gBAC1B,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;aAC/B;QACH,CAAC;QACD,+BAA+B;KAChC,CAAC,CAAC;IAEH,2BAA2B;IAE3B,KAAK,CAAC,mBAAmB,CACvB,GAAG,EACH,GAAG,EAAE,CAAC,CAAC;QACL,KAAK,CAAC,wBAAwB,CAAC,KAAa;YAC1C,OAAO,YAAY,CAAC;QACtB,CAAC;QACD,KAAK,CAAC,WAAW,CAAC,OAA6B;YAC7C,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE,UAAU,KAAK,KAAK,CAAC,OAAO,EAAE,gBAAgB,EAAE;gBACnF,MAAM,IAAI,UAAU,CAClB,sBAAsB,EACtB,8EAA8E,CAC/E,CAAC;aACH;YACD,MAAM,QAAQ,GAAG,MAAM,CAAC,kBAAkB,CAAC;YAC3C,IAAI,CAAC,QAAQ,EAAE;gBACb,MAAM,IAAI,UAAU,CAAC,sBAAsB,EAAE,+BAA+B,CAAC,CAAC;aAC/E;YAED,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE;gBACtC,GAAG,OAAO;gBACV,6IAA6I;gBAC7I,cAAc,CAAC,OAAO;oBACpB,IAAI,OAAO,CAAC,cAAc,EAAE;wBAC1B,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;qBACjC;oBACD,IAAI,KAAK,CAAC,cAAc,EAAE;wBACxB,KAAK,CAAC,cAAc,CAAC,EAAE,WAAW,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;qBAClE;gBACH,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,aAAa;YACjB,IAAI,KAAK,CAAC,OAAO,EAAE;gBACjB,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;aACtB;QACH,CAAC;QACD,KAAK,CAAC,YAAY;YAChB,IAAI,KAAK,CAAC,OAAO,EAAE;gBACjB,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;aACvB;QACH,CAAC;KACF,CAAC,EACF,CAAC,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC,cAAc,CAAC,CAClD,CAAC;IAEF,qGAAqG;IACrG,iHAAiH;IACjH,MAAM,OAAO,GAAG,IAAI,CAAC;IAErB,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAuB,GAAG,EAAE;QACrD,MAAM,mBAAmB,GAAG,MAAM,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;QACrE,OAAO;YACL,UAAU,CAAC,YAAY;YACvB,MAAM,CAAC,KAAK;YACZ;gBACE,kBAAkB;gBAClB,SAAS,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS;aAC9D;SACF,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAElB,OAAO,CACL,oBAAC,IAAI,IAAC,aAAa,EAAC,UAAU,EAAC,KAAK,EAAE,CAAC,MAAM,CAAC,YAAY,EAAE,KAAK,CAAC,KAAK,CAAC;QACtE,oBAAC,KAAK,IACJ,QAAQ,QACR,WAAW,QACX,KAAK,EAAE,OAAO,EACd,MAAM,EAAE,MAAM;YACd,oBAAoB;YACpB,aAAa,EAAE,KAAK,CAAC,aAAa,EAClC,GAAG,EAAE,KAAK,EACV,KAAK,EAAE,KAAK,GACZ;QACD,KAAK,CAAC,QAAQ,CACV,CACR,CAAC;AACJ,CAAC,CACF,CAAC;AAEF,eAAe,cAAc,CAAC;AAE9B,MAAM,KAAK,GAAG,KAAK,CAAC,UAAU,CAC5B,CACE,KAKC,EACD,GAAgC,EAChC,EAAE,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,GAAG,KAAK,EAAE,GAAG,EAAE,CAAC,CAC/C,CAAC;AAEF,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IAC/B,YAAY,EAAE;QACZ,IAAI,EAAE,CAAC;QACP,UAAU,EAAE,SAAS;KACtB;IACD,KAAK,EAAE;QACL,KAAK,EAAE,MAAM;QACb,MAAM,EAAE,MAAM;QACd,SAAS,EAAE,OAAO;KACnB;CACF,CAAC,CAAC", "sourcesContent": ["import { CodedError } from 'expo-modules-core';\nimport * as React from 'react';\nimport { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';\nimport createElement from 'react-native-web/dist/exports/createElement';\n\nimport {\n  CameraCapturedPicture,\n  CameraNativeProps,\n  CameraPictureOptions,\n  CameraType,\n} from './Camera.types';\nimport CameraManager from './ExponentCameraManager.web';\nimport { capture } from './WebCameraUtils';\nimport { PictureSizes } from './WebConstants';\nimport { useWebCameraStream } from './useWebCameraStream';\nimport { useWebQRScanner } from './useWebQRScanner';\n\nexport interface ExponentCameraRef {\n  getAvailablePictureSizes: (ratio: string) => Promise<string[]>;\n  takePicture: (options: CameraPictureOptions) => Promise<CameraCapturedPicture>;\n  resumePreview: () => Promise<void>;\n  pausePreview: () => Promise<void>;\n}\n\nconst ExponentCamera = React.forwardRef(\n  (\n    { type, pictureSize, poster, ...props }: CameraNativeProps & { children?: React.ReactNode },\n    ref: React.Ref<ExponentCameraRef>\n  ) => {\n    const video = React.useRef<HTMLVideoElement | null>(null);\n\n    const native = useWebCameraStream(video, type as CameraType, props, {\n      onCameraReady() {\n        if (props.onCameraReady) {\n          props.onCameraReady();\n        }\n      },\n      onMountError: props.onMountError,\n    });\n\n    const isQRScannerEnabled = React.useMemo<boolean>(() => {\n      return !!(\n        props.barCodeScannerSettings?.barCodeTypes?.includes('qr') && !!props.onBarCodeScanned\n      );\n    }, [props.barCodeScannerSettings?.barCodeTypes, props.onBarCodeScanned]);\n\n    useWebQRScanner(video, {\n      interval: props.barCodeScannerSettings?.interval,\n      isEnabled: isQRScannerEnabled,\n      captureOptions: { scale: 1, isImageMirror: native.type === CameraType.front },\n      onScanned(event) {\n        if (props.onBarCodeScanned) {\n          props.onBarCodeScanned(event);\n        }\n      },\n      // onError: props.onMountError,\n    });\n\n    // const [pause, setPaused]\n\n    React.useImperativeHandle(\n      ref,\n      () => ({\n        async getAvailablePictureSizes(ratio: string): Promise<string[]> {\n          return PictureSizes;\n        },\n        async takePicture(options: CameraPictureOptions): Promise<CameraCapturedPicture> {\n          if (!video.current || video.current?.readyState !== video.current?.HAVE_ENOUGH_DATA) {\n            throw new CodedError(\n              'ERR_CAMERA_NOT_READY',\n              'HTMLVideoElement does not have enough camera data to construct an image yet.'\n            );\n          }\n          const settings = native.mediaTrackSettings;\n          if (!settings) {\n            throw new CodedError('ERR_CAMERA_NOT_READY', 'MediaStream is not ready yet.');\n          }\n\n          return capture(video.current, settings, {\n            ...options,\n            // This will always be defined, the option gets added to a queue in the upper-level. We should replace the original so it isn't called twice.\n            onPictureSaved(picture) {\n              if (options.onPictureSaved) {\n                options.onPictureSaved(picture);\n              }\n              if (props.onPictureSaved) {\n                props.onPictureSaved({ nativeEvent: { data: picture, id: -1 } });\n              }\n            },\n          });\n        },\n        async resumePreview(): Promise<void> {\n          if (video.current) {\n            video.current.play();\n          }\n        },\n        async pausePreview(): Promise<void> {\n          if (video.current) {\n            video.current.pause();\n          }\n        },\n      }),\n      [native.mediaTrackSettings, props.onPictureSaved]\n    );\n\n    // TODO(Bacon): Create a universal prop, on native the microphone is only used when recording videos.\n    // Because we don't support recording video in the browser we don't need the user to give microphone permissions.\n    const isMuted = true;\n\n    const style = React.useMemo<StyleProp<ViewStyle>>(() => {\n      const isFrontFacingCamera = native.type === CameraManager.Type.front;\n      return [\n        StyleSheet.absoluteFill,\n        styles.video,\n        {\n          // Flip the camera\n          transform: isFrontFacingCamera ? [{ scaleX: -1 }] : undefined,\n        },\n      ];\n    }, [native.type]);\n\n    return (\n      <View pointerEvents=\"box-none\" style={[styles.videoWrapper, props.style]}>\n        <Video\n          autoPlay\n          playsInline\n          muted={isMuted}\n          poster={poster}\n          // webkitPlaysinline\n          pointerEvents={props.pointerEvents}\n          ref={video}\n          style={style}\n        />\n        {props.children}\n      </View>\n    );\n  }\n);\n\nexport default ExponentCamera;\n\nconst Video = React.forwardRef(\n  (\n    props: React.ComponentProps<typeof View> & {\n      autoPlay?: boolean;\n      playsInline?: boolean;\n      muted?: boolean;\n      poster?: string;\n    },\n    ref: React.Ref<HTMLVideoElement>\n  ) => createElement('video', { ...props, ref })\n);\n\nconst styles = StyleSheet.create({\n  videoWrapper: {\n    flex: 1,\n    alignItems: 'stretch',\n  },\n  video: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n  },\n});\n"]}