{"version": 3, "names": ["_RTCRtpEncodingParameters", "_interopRequireDefault", "require", "_RTCRtpParameters", "_RTCUtil", "obj", "__esModule", "default", "_defineProperty", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "DegradationPreference", "fromNative", "nativeFormat", "stringFormat", "toLowerCase", "replace", "toNative", "format", "toUpperCase", "RTCRtpSendParameters", "RTCRtpParameters", "constructor", "init", "transactionId", "encodings", "degradationPreference", "enc", "push", "RTCRtpEncodingParameters", "toJSON", "map", "e", "deepClone", "exports"], "sources": ["RTCRtpSendParameters.ts"], "sourcesContent": ["import RTCRtpEncodingParameters, { RTCRtpEncodingParametersInit } from './RTCRtpEncodingParameters';\nimport RTCRtpParameters, { RTCRtpParametersInit } from './RTCRtpParameters';\nimport { deepClone } from './RTCUtil';\n\ntype DegradationPreferenceType = 'maintain-framerate'\n    | 'maintain-resolution'\n    | 'balanced'\n    | 'disabled'\n\n\n/**\n * Class to convert degradation preference format. Native has a format such as\n * MAINTAIN_FRAMERATE whereas the web APIs expect maintain-framerate\n */\nclass DegradationPreference {\n    static fromNative(nativeFormat: string): DegradationPreferenceType {\n        const stringFormat = nativeFormat.toLowerCase().replace('_', '-');\n\n        return stringFormat as DegradationPreferenceType;\n    }\n\n    static toNative(format: DegradationPreferenceType): string {\n        return format.toUpperCase().replace('-', '_');\n    }\n}\n\nexport interface RTCRtpSendParametersInit extends RTCRtpParametersInit {\n    transactionId: string;\n    encodings: RTCRtpEncodingParametersInit[];\n    degradationPreference?: string;\n}\n\nexport default class RTCRtpSendParameters extends RTCRtpParameters {\n    readonly transactionId: string;\n    encodings: (RTCRtpEncodingParameters | RTCRtpEncodingParametersInit)[];\n    degradationPreference: DegradationPreferenceType | null;\n\n    constructor(init: RTCRtpSendParametersInit) {\n        super(init);\n\n        this.transactionId = init.transactionId;\n        this.encodings = [];\n        this.degradationPreference = init.degradationPreference ?\n            DegradationPreference.fromNative(init.degradationPreference) : null;\n\n        for (const enc of init.encodings) {\n            this.encodings.push(new RTCRtpEncodingParameters(enc));\n        }\n    }\n\n    toJSON() {\n        const obj = super.toJSON();\n\n        obj['transactionId'] = this.transactionId;\n        obj['encodings'] = this.encodings.map(e => deepClone(e));\n\n        if (this.degradationPreference !== null) {\n            obj['degradationPreference'] = DegradationPreference.toNative(this.degradationPreference);\n        }\n\n        return obj;\n    }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,yBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAAsC,SAAAD,uBAAAI,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,gBAAAH,GAAA,EAAAI,GAAA,EAAAC,KAAA,QAAAD,GAAA,IAAAJ,GAAA,IAAAM,MAAA,CAAAC,cAAA,CAAAP,GAAA,EAAAI,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAG,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAV,GAAA,CAAAI,GAAA,IAAAC,KAAA,WAAAL,GAAA;AAQtC;AACA;AACA;AACA;AACA,MAAMW,qBAAqB,CAAC;EACxB,OAAOC,UAAUA,CAACC,YAAoB,EAA6B;IAC/D,MAAMC,YAAY,GAAGD,YAAY,CAACE,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAEjE,OAAOF,YAAY;EACvB;EAEA,OAAOG,QAAQA,CAACC,MAAiC,EAAU;IACvD,OAAOA,MAAM,CAACC,WAAW,CAAC,CAAC,CAACH,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EACjD;AACJ;AAQe,MAAMI,oBAAoB,SAASC,yBAAgB,CAAC;EAK/DC,WAAWA,CAACC,IAA8B,EAAE;IACxC,KAAK,CAACA,IAAI,CAAC;IAACpB,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAEZ,IAAI,CAACqB,aAAa,GAAGD,IAAI,CAACC,aAAa;IACvC,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,qBAAqB,GAAGH,IAAI,CAACG,qBAAqB,GACnDf,qBAAqB,CAACC,UAAU,CAACW,IAAI,CAACG,qBAAqB,CAAC,GAAG,IAAI;IAEvE,KAAK,MAAMC,GAAG,IAAIJ,IAAI,CAACE,SAAS,EAAE;MAC9B,IAAI,CAACA,SAAS,CAACG,IAAI,CAAC,IAAIC,iCAAwB,CAACF,GAAG,CAAC,CAAC;IAC1D;EACJ;EAEAG,MAAMA,CAAA,EAAG;IACL,MAAM9B,GAAG,GAAG,KAAK,CAAC8B,MAAM,CAAC,CAAC;IAE1B9B,GAAG,CAAC,eAAe,CAAC,GAAG,IAAI,CAACwB,aAAa;IACzCxB,GAAG,CAAC,WAAW,CAAC,GAAG,IAAI,CAACyB,SAAS,CAACM,GAAG,CAACC,CAAC,IAAI,IAAAC,kBAAS,EAACD,CAAC,CAAC,CAAC;IAExD,IAAI,IAAI,CAACN,qBAAqB,KAAK,IAAI,EAAE;MACrC1B,GAAG,CAAC,uBAAuB,CAAC,GAAGW,qBAAqB,CAACM,QAAQ,CAAC,IAAI,CAACS,qBAAqB,CAAC;IAC7F;IAEA,OAAO1B,GAAG;EACd;AACJ;AAACkC,OAAA,CAAAhC,OAAA,GAAAkB,oBAAA"}