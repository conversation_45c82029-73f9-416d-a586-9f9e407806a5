/**
 * WebRTC Signaling Server
 * Handles peer-to-peer connection establishment
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

interface SignalingMessage {
  type: string;
  channel: string;
  offer?: RTCSessionDescriptionInit;
  answer?: RTCSessionDescriptionInit;
  candidate?: RTCIceCandidateInit;
  clientId?: string;
}

// Store active connections per channel
const channels = new Map<string, Set<WebSocket>>();
const clientChannels = new Map<WebSocket, string>();

function addToChannel(ws: WebSocket, channelName: string) {
  if (!channels.has(channelName)) {
    channels.set(channelName, new Set());
  }
  channels.get(channelName)!.add(ws);
  clientChannels.set(ws, channelName);
}

function removeFromChannel(ws: WebSocket) {
  const channelName = clientChannels.get(ws);
  if (channelName) {
    const channelClients = channels.get(channelName);
    if (channelClients) {
      channelClients.delete(ws);
      if (channelClients.size === 0) {
        channels.delete(channelName);
      }
    }
    clientChannels.delete(ws);
  }
}

function broadcastToChannel(channelName: string, message: any, excludeWs?: WebSocket) {
  const channelClients = channels.get(channelName);
  if (channelClients) {
    const messageStr = JSON.stringify(message);
    channelClients.forEach(ws => {
      if (ws !== excludeWs && ws.readyState === WebSocket.OPEN) {
        try {
          ws.send(messageStr);
        } catch (error) {
          console.error('Failed to send message:', error);
          removeFromChannel(ws);
        }
      }
    });
  }
}

serve((req: Request) => {
  // Handle CORS
  if (req.method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  }

  // Handle WebSocket upgrade
  if (req.headers.get("upgrade") === "websocket") {
    const { socket, response } = Deno.upgradeWebSocket(req);

    socket.onopen = () => {
      console.log("WebSocket connection opened");
    };

    socket.onmessage = (event) => {
      try {
        const message: SignalingMessage = JSON.parse(event.data);
        console.log("Received message:", message.type, "for channel:", message.channel);

        switch (message.type) {
          case 'join':
            addToChannel(socket, message.channel);
            // Notify others in channel about new participant
            broadcastToChannel(message.channel, {
              type: 'participant-joined',
              channel: message.channel,
            }, socket);
            break;

          case 'offer':
            addToChannel(socket, message.channel);
            // Forward offer to other participants
            broadcastToChannel(message.channel, {
              type: 'offer',
              offer: message.offer,
              channel: message.channel,
            }, socket);
            break;

          case 'answer':
            // Forward answer to other participants
            broadcastToChannel(message.channel, {
              type: 'answer',
              answer: message.answer,
              channel: message.channel,
            }, socket);
            break;

          case 'ice-candidate':
            // Forward ICE candidate to other participants
            broadcastToChannel(message.channel, {
              type: 'ice-candidate',
              candidate: message.candidate,
              channel: message.channel,
            }, socket);
            break;

          case 'leave':
            removeFromChannel(socket);
            break;

          default:
            console.log('Unknown message type:', message.type);
        }
      } catch (error) {
        console.error('Error processing message:', error);
      }
    };

    socket.onclose = () => {
      console.log("WebSocket connection closed");
      removeFromChannel(socket);
    };

    socket.onerror = (error) => {
      console.error("WebSocket error:", error);
      removeFromChannel(socket);
    };

    return response;
  }

  // Handle HTTP requests
  if (req.method === "GET") {
    return new Response(JSON.stringify({
      message: "WebRTC Signaling Server",
      activeChannels: Array.from(channels.keys()),
      totalConnections: Array.from(channels.values()).reduce((sum, set) => sum + set.size, 0),
    }), {
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
    });
  }

  return new Response("Method not allowed", { status: 405 });
});
