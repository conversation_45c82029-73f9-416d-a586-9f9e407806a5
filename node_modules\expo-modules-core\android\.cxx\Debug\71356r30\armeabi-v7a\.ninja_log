# ninja log v5
7	900	7759272621103033	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/.trae/more/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o	f21dd22732ea25ed
4368	4463	7759272657116700	../../../../build/intermediates/cxx/Debug/71356r30/obj/armeabi-v7a/libexpo-modules-core.so	37425b0adef39678
174	1636	7759272628827523	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o	a6e323d0c108baa9
37	2373	7759272636083439	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o	b81ade227ea15a7d
106	1208	7759272624424851	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/.trae/more/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o	bf7e2cbbb6497295
92	3279	7759272645213941	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o	87d96065f2b32156
19	1252	7759272624795627	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o	dcdd286c89800d04
115	2553	7759272637915537	CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o	66281cbfa81312b
56	2332	7759272635634837	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o	14281235ff7eb883
85	3083	7759272643232020	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o	1395693884efe5a9
1	1564	7759272628049920	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/.trae/more/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o	26cdd0a5ed81b008
142	3528	7759272647659916	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o	d7e29712a85f7e8b
162	2481	7759272637234957	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o	577108c8259f4b5a
43	2624	7759272638634625	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o	8e21dcdd30f342eb
63	2801	7759272640381189	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o	33e67e5b206b160d
13	2884	7759272641219923	CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o	3d81af5b30501edf
70	3051	7759272642912292	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o	b2f3e6832f25a01b
78	4368	7759272656131822	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o	902f4fb8371b35f1
201	3093	7759272643278437	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o	92fbfb9637ee286b
193	3516	7759272647541327	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o	d8ba25c0b7df7f4a
50	3143	7759272643794008	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o	36f485fcd0ce5ebf
25	3457	7759272646934917	CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o	995544d51dfa3fc5
31	3461	7759272646975145	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIInteropModuleRegistry.cpp.o	447616f20e9eed03
98	3756	7759272649974103	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o	4be0ed215bac51cc
184	3919	7759272651612162	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o	aef502ce36d94965
153	4139	7759272653845180	CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o	5335966bd285902
0	9	0	clean	17a56fa2c58de3da
0	6	0	clean	17a56fa2c58de3da
