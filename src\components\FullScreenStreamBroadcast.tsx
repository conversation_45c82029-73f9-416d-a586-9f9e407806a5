import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Dimensions,
  Animated,
  PanResponder,
  Alert,
  TextInput,
} from 'react-native';
import { AgoraService, RtcSurfaceView, ClientRoleType } from '../services/AgoraServiceFactory';
import BackendService from '../services/BackendService';
import { CONFIG } from '../config/index';

interface FullScreenStreamBroadcastProps {
  channelName?: string;
  onClose: () => void;
}

const FullScreenStreamBroadcast: React.FC<FullScreenStreamBroadcastProps> = ({
  channelName: initialChannelName,
  onClose,
}) => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showControls, setShowControls] = useState(true);
  const [isMuted, setIsMuted] = useState(false);
  const [isFrontCamera, setIsFrontCamera] = useState(true);
  const [streamSessionId, setStreamSessionId] = useState<string | null>(null);
  const [channelName, setChannelName] = useState(initialChannelName || '');
  const [streamTitle, setStreamTitle] = useState('');
  const [showSetup, setShowSetup] = useState(!initialChannelName);
  const [viewerCount, setViewerCount] = useState(0);

  const agoraService = useRef(new AgoraService()).current;
  const backendService = useRef(new BackendService()).current;
  const [uid] = useState(Math.floor(Math.random() * 1000));
  const controlsOpacity = useRef(new Animated.Value(1)).current;
  const hideControlsTimer = useRef<NodeJS.Timeout | null>(null);

  // Get screen dimensions
  const { width, height } = Dimensions.get('window');

  // Pan responder for tap to show/hide controls
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => false,
      onPanResponderGrant: () => {
        if (isStreaming) {
          console.log('Broadcast screen tapped, toggling controls');
          if (showControls) {
            hideControls();
          } else {
            setShowControls(true);
            showControlsWithTimer();
          }
        }
      },
    })
  ).current;

  useEffect(() => {
    initializeAgora();

    return () => {
      cleanup();
    };
  }, []);

  useEffect(() => {
    if (showControls && isStreaming) {
      showControlsWithTimer();
    }
  }, [showControls, isStreaming]);

  // Show controls initially when streaming starts
  useEffect(() => {
    if (isStreaming) {
      setShowControls(true);
      showControlsWithTimer();
    }
  }, [isStreaming]);

  const initializeAgora = async () => {
    try {
      setIsLoading(true);
      await agoraService.initialize(CONFIG.AGORA_APP_ID);
      setIsLoading(false);
    } catch (error) {
      console.error('Failed to initialize Agora:', error);
      setError('Failed to initialize streaming service');
      setIsLoading(false);
    }
  };

  const startStream = async () => {
    try {
      if (!channelName.trim()) {
        Alert.alert('Channel Required', 'Please enter a channel name');
        return;
      }

      setIsLoading(true);
      setError(null);

      // Create stream session
      const { session, error: sessionError } = await backendService.startStreamSession(
        channelName.trim(),
        {
          title: streamTitle.trim() || 'Live Stream',
          description: 'Live streaming session'
        }
      );

      if (sessionError || !session) {
        throw new Error(sessionError || 'Failed to create stream session');
      }

      setStreamSessionId(session.id);

      // Start preview and join channel
      await agoraService.startPreview();
      await agoraService.joinChannel('', channelName.trim(), uid, ClientRoleType.ClientRoleBroadcaster);

      setIsStreaming(true);
      setShowSetup(false);
      setIsLoading(false);

      // Start viewer count polling
      startViewerCountPolling();
    } catch (error) {
      console.error('Failed to start stream:', error);
      setError('Failed to start stream. Please try again.');
      setIsLoading(false);
    }
  };

  const stopStream = async () => {
    try {
      setIsLoading(true);

      await agoraService.leaveChannel();
      await agoraService.stopPreview();

      if (streamSessionId) {
        await backendService.endStreamSession(streamSessionId);
      }

      setIsStreaming(false);
      setStreamSessionId(null);
      setIsLoading(false);

      onClose();
    } catch (error) {
      console.error('Failed to stop stream:', error);
      setError('Failed to stop stream');
      setIsLoading(false);
    }
  };

  const startViewerCountPolling = () => {
    const interval = setInterval(async () => {
      if (streamSessionId) {
        try {
          // In a real app, you'd have an API to get current viewer count
          // For now, we'll simulate it
          setViewerCount(Math.floor(Math.random() * 50) + 1);
        } catch (error) {
          console.error('Failed to get viewer count:', error);
        }
      }
    }, 5000);

    return () => clearInterval(interval);
  };

  const cleanup = async () => {
    if (isStreaming) {
      await stopStream();
    }
    if (hideControlsTimer.current) {
      clearTimeout(hideControlsTimer.current);
    }
  };

  const toggleControls = () => {
    setShowControls(!showControls);
  };

  const showControlsWithTimer = () => {
    console.log('Showing broadcast controls with timer');

    // Clear any existing timer
    if (hideControlsTimer.current) {
      clearTimeout(hideControlsTimer.current);
    }

    // Show controls immediately
    Animated.timing(controlsOpacity, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();

    // Hide controls after 10 seconds (increased for better UX)
    hideControlsTimer.current = setTimeout(() => {
      console.log('Auto-hiding broadcast controls');
      hideControls();
    }, 10000);
  };

  const hideControls = () => {
    console.log('Hiding broadcast controls');

    // Clear timer
    if (hideControlsTimer.current) {
      clearTimeout(hideControlsTimer.current);
    }

    Animated.timing(controlsOpacity, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setShowControls(false);
    });
  };

  const toggleMute = () => {
    agoraService.muteLocalAudioStream(!isMuted);
    setIsMuted(!isMuted);
  };

  const switchCamera = () => {
    agoraService.switchCamera();
    setIsFrontCamera(!isFrontCamera);
  };

  const renderSetupScreen = () => {
    return (
      <View style={styles.setupContainer}>
        <StatusBar hidden />

        {/* Always visible back button on setup screen */}
        <TouchableOpacity style={styles.alwaysVisibleBackButton} onPress={() => {
          console.log('Setup screen back button pressed');
          onClose();
        }}>
          <Text style={styles.alwaysVisibleBackButtonText}>✕</Text>
        </TouchableOpacity>

        <View style={styles.setupContent}>
          <Text style={styles.setupTitle}>Start Your Stream</Text>

          <TextInput
            style={styles.input}
            placeholder="Channel Name"
            placeholderTextColor="#999"
            value={channelName}
            onChangeText={setChannelName}
          />

          <TextInput
            style={styles.input}
            placeholder="Stream Title (optional)"
            placeholderTextColor="#999"
            value={streamTitle}
            onChangeText={setStreamTitle}
          />

          <TouchableOpacity
            style={styles.startButton}
            onPress={startStream}
            disabled={isLoading || !channelName.trim()}
          >
            <Text style={styles.startButtonText}>
              {isLoading ? 'Starting...' : 'Go Live'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.cancelButton} onPress={() => {
            console.log('Cancel button pressed');
            onClose();
          }}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderStreamingContent = () => {
    if (error) {
      return (
        <View style={styles.centerContent}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={startStream}>
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <RtcSurfaceView
        style={styles.fullScreenVideo}
        canvas={{ uid: uid }}
        isLocal={true}
      />
    );
  };

  const renderControls = () => {
    if (!showControls || !isStreaming) return null;

    return (
      <Animated.View style={[styles.controlsOverlay, { opacity: controlsOpacity }]}>
        {/* Top Controls */}
        <View style={styles.topControls}>
          <TouchableOpacity style={styles.closeButton} onPress={stopStream}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>

          <View style={styles.streamInfo}>
            <Text style={styles.liveIndicator}>● LIVE</Text>
            <Text style={styles.channelName}>{channelName}</Text>
            <Text style={styles.viewerCount}>{viewerCount} watching</Text>
          </View>
        </View>

        {/* Bottom Controls */}
        <View style={styles.bottomControls}>
          <TouchableOpacity
            style={[styles.controlButton, isMuted && styles.mutedButton]}
            onPress={toggleMute}
          >
            <Text style={styles.controlButtonText}>
              {isMuted ? '🔇' : '🎤'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.controlButton}
            onPress={switchCamera}
          >
            <Text style={styles.controlButtonText}>📷</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.endStreamButton}
            onPress={() => {
              Alert.alert(
                'End Stream',
                'Are you sure you want to end your stream?',
                [
                  { text: 'Cancel', style: 'cancel' },
                  { text: 'End Stream', onPress: stopStream, style: 'destructive' }
                ]
              );
            }}
          >
            <Text style={styles.endStreamButtonText}>End Stream</Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };

  if (showSetup) {
    return renderSetupScreen();
  }

  return (
    <View style={styles.container} {...panResponder.panHandlers}>
      <StatusBar hidden />

      {renderStreamingContent()}
      {renderControls()}

      {/* Always visible tap indicator when streaming */}
      {isStreaming && !showControls && (
        <View style={styles.tapIndicator}>
          <Text style={styles.tapIndicatorText}>Tap to show controls</Text>
        </View>
      )}

      {/* Always visible back button when streaming */}
      {isStreaming && (
        <TouchableOpacity style={styles.alwaysVisibleBackButton} onPress={() => {
          Alert.alert(
            'End Stream',
            'Are you sure you want to end your stream?',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'End Stream', onPress: stopStream, style: 'destructive' }
            ]
          );
        }}>
          <Text style={styles.alwaysVisibleBackButtonText}>✕</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  setupContainer: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  setupContent: {
    width: '80%',
    alignItems: 'center',
  },
  setupTitle: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
  },
  input: {
    width: '100%',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    color: '#fff',
    fontSize: 16,
  },
  startButton: {
    backgroundColor: '#FF6B35',
    paddingHorizontal: 40,
    paddingVertical: 15,
    borderRadius: 25,
    marginTop: 20,
    width: '100%',
    alignItems: 'center',
  },
  startButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  cancelButton: {
    marginTop: 15,
    paddingVertical: 10,
  },
  cancelButtonText: {
    color: '#999',
    fontSize: 16,
  },
  fullScreenVideo: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  errorText: {
    color: '#ff4444',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryText: {
    color: '#fff',
    fontSize: 16,
  },
  controlsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
  },
  topControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  streamInfo: {
    alignItems: 'center',
  },
  liveIndicator: {
    color: '#ff4444',
    fontSize: 14,
    fontWeight: 'bold',
  },
  channelName: {
    color: '#fff',
    fontSize: 16,
    marginTop: 2,
  },
  viewerCount: {
    color: '#fff',
    fontSize: 12,
    marginTop: 2,
    opacity: 0.8,
  },
  bottomControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 50,
    paddingHorizontal: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  controlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mutedButton: {
    backgroundColor: 'rgba(255,68,68,0.3)',
  },
  controlButtonText: {
    fontSize: 20,
  },
  endStreamButton: {
    backgroundColor: '#ff4444',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
  },
  endStreamButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  tapIndicator: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginHorizontal: 50,
  },
  tapIndicatorText: {
    color: '#fff',
    fontSize: 12,
    opacity: 0.8,
  },
  alwaysVisibleBackButton: {
    position: 'absolute',
    top: 50,
    left: 20,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  alwaysVisibleBackButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default FullScreenStreamBroadcast;
