{"name": "event-target-shim", "version": "6.0.2", "description": "An implementation of WHATWG EventTarget interface.", "main": "index.js", "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./es5": {"import": "./es5.mjs", "require": "./es5.js"}, "./umd": "./umd.js", "./package.json": "./package.json"}, "files": ["index.*", "es5.*", "umd.*"], "engines": {"node": ">=10.13.0"}, "scripts": {"build": "run-s \"build:{clean,rollup,dts,meta}\"", "build:clean": "rimraf \"dist/*\"", "build:rollup": "rollup --config scripts/rollup.config.js", "build:dts": "dts-bundle-generator --project tsconfig/dts.json --out-file dist/index.d.ts src/index.ts && dts-bundle-generator --project tsconfig/dts.json --out-file dist/es5.d.ts src/index.ts", "build:meta": "cpx \"{LICENSE,package.json,README.md}\" dist/", "preversion": "npm test", "version": "npm run build", "postversion": "release", "test": "run-s \"test:{clean,tsc,lint,format,mocha}\"", "test:clean": "rimraf \"coverage/*\"", "test:tsc": "tsc -p tsconfig/build.json --noEmit", "test:lint": "eslint .", "test:format": "prettier --check .", "test:mocha": "ts-node scripts/test", "watch:mocha": "mocha --require ts-node/register/transpile-only --extensions ts --watch-files src,test --watch \"test/*.ts\""}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-runtime": "^7.12.10", "@babel/preset-env": "^7.12.11", "@mysticatea/eslint-plugin": "^13.0.0", "@mysticatea/spy": "^0.1.2", "@mysticatea/tools": "^0.1.1", "@rollup/plugin-babel": "^5.2.2", "@rollup/plugin-typescript": "^8.1.0", "@types/istanbul-lib-coverage": "^2.0.3", "@types/istanbul-lib-report": "^3.0.0", "@types/istanbul-lib-source-maps": "^4.0.1", "@types/istanbul-reports": "^3.0.0", "@types/mocha": "^8.2.0", "@types/rimraf": "^3.0.0", "assert": "^2.0.0", "babel-loader": "^8.2.2", "babel-plugin-istanbul": "^6.0.0", "buffer": "^6.0.3", "chalk": "^4.1.0", "codecov": "^3.8.1", "cpx": "^1.5.0", "dts-bundle-generator": "^5.5.0", "eslint": "^7.15.0", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.0", "istanbul-reports": "^3.0.2", "mocha": "^7.2.0", "npm-run-all": "^4.1.5", "path-browserify": "^1.0.1", "playwright": "^1.7.0", "prettier": "~2.2.1", "process": "^0.11.10", "rimraf": "^3.0.2", "rollup": "^2.35.1", "rollup-plugin-terser": "^7.0.2", "rollup-watch": "^4.3.1", "stream-browserify": "^3.0.0", "ts-loader": "^8.0.12", "ts-node": "^9.1.1", "tslib": "^2.0.3", "typescript": "~4.1.3", "url": "^0.11.0", "util": "^0.12.3", "webpack": "^5.11.0"}, "repository": {"type": "git", "url": "https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "whatwg", "eventtarget", "event", "events", "shim"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "funding": "https://github.com/sponsors/mysticatea", "sideEffects": false, "unpkg": "umd.js"}