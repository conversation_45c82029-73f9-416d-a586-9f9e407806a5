# Check if you have EAS CLI installed
eas --version

# If not installed, install it:
npm install -g @expo/eas-cli

Step 1: Configure EAS build
# Initialize EAS configuration
eas build:configure

# Login to Expo (if not already logged in)
npx eas-cli login

npx eas-cli build --platform android --profile development

Step 2: Update app.json for development build

Step 3: Install and Build the development dependencies
# Install expo-dev-client
npx expo install expo-dev-client

# Ensure expo-camera is properly installed
npx expo install expo-camera


# Install any missing dependencies
npx expo install --fix

npx expo install expo-dev-client 

npx expo install expo-camera

Step 4. Build Development APK (android)
# Build development APK for Android
eas build --platform android --profile development

# Alternative: Build for local development (faster)
eas build --platform android --profile development --local

Step 5. Build Development App (iOS - if you have Mac)
# Build development app for iOS (requires Mac)

# Build development app for iOS (requires Mac)
eas build --platform ios --profile development

# For iOS Simulator
eas build --platform ios --profile development-simulator

Step 6.Quick Local Development (Recommended)
# Generate development build locally (much faster)
npx create-expo-app --template

# Or use prebuild for existing project
npx expo prebuild --clean

# Then build locally with Android Studio or Xcode

Step 7. Install and Run Development Build
# After build completes, install the APK on your device
# Download from EAS build dashboard or use:
eas build:list

# Start development server
npx expo start --dev-client

# Or start with specific options
npx expo start --dev-client --clear

Complete Command Sequence (Run These in Order):
Option A: Cloud Build (Easier)
# 1. Login to EAS
eas login

# 2. Build development APK
eas build --platform android --profile development

# 3. Download and install APK on your device
# (EAS will provide download link)

# 4. Start development server
npx expo start --dev-client

Option B: Local Build (Faster)
# 1. Prebuild native code
npx expo prebuild --clean

# 2. Install Android dependencies
cd android && ./gradlew clean && cd ..

# 3. Build and install on connected device
npx expo run:android --device

# 4. Start development server
npx expo start --dev-client

Recommended Quick Start:
Run these commands one by one:
# 1. Login to EAS
eas login

# 2. Build development APK (this will take 10-15 minutes)
eas build --platform android --profile development

# 3. While build is running, prepare your device:
#    - Enable Developer Options
#    - Enable USB Debugging
#    - Connect via USB or ensure same WiFi network

# 4. Once build completes, download APK and install on device

# 5. Start development server
npx expo start --dev-client

# 6. Open app on device and test live streaming
#    - Ensure camera permissions are granted
#    - Test audio and video streaming features
#    - Verify that the app is using the development build
#      (check for "Dev Client" label in app info)
#    - If using Agora, ensure you have a valid App ID and Certificate







