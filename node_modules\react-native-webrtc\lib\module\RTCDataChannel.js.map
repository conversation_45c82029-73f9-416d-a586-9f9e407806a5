{"version": 3, "names": ["base64", "EventTarget", "defineEventAttribute", "NativeModules", "addListener", "removeListener", "MessageEvent", "RTCDataChannelEvent", "WebRTCModule", "RTCDataChannel", "constructor", "info", "_defineProperty", "_peerConnectionId", "peerConnectionId", "_reactTag", "reactTag", "_bufferedAmount", "_label", "label", "_id", "id", "_ordered", "Boolean", "ordered", "_maxPacketLifeTime", "maxPacketLifeTime", "_maxRetransmits", "maxRetransmits", "_protocol", "protocol", "_negotiated", "negotiated", "_readyState", "readyState", "_registerEvents", "bufferedAmount", "send", "data", "dataChannelSend", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Uint8Array", "buffer", "byteOffset", "byteLength", "TypeError", "base64data", "fromByteArray", "close", "dataChannelClose", "ev", "state", "dispatchEvent", "channel", "dataChannelDispose", "type", "toByteArray", "bufferedAmountLowThreshold", "proto", "prototype"], "sources": ["RTCDataChannel.ts"], "sourcesContent": ["import * as base64 from 'base64-js';\nimport { EventTarget, defineEventAttribute } from 'event-target-shim/index';\nimport { NativeModules } from 'react-native';\n\nimport { addListener, removeListener } from './EventEmitter';\nimport MessageEvent from './MessageEvent';\nimport RTCDataChannelEvent from './RTCDataChannelEvent';\n\nconst { WebRTCModule } = NativeModules;\n\ntype RTCDataChannelState = 'connecting' | 'open' | 'closing' | 'closed';\n\ntype DataChannelEventMap = {\n    bufferedamountlow: RTCDataChannelEvent<'bufferedamountlow'>;\n    close: RTCDataChannelEvent<'close'>;\n    closing: RTCDataChannelEvent<'closing'>;\n    error: RTCDataChannelEvent<'error'>;\n    message: MessageEvent<'message'>;\n    open: RTCDataChannelEvent<'open'>;\n};\n\nexport default class RTCDataChannel extends EventTarget<DataChannelEventMap> {\n    _peerConnectionId: number;\n    _reactTag: string;\n\n    _bufferedAmount: number;\n    _id: number;\n    _label: string;\n    _maxPacketLifeTime?: number;\n    _maxRetransmits?: number;\n    _negotiated: boolean;\n    _ordered: boolean;\n    _protocol: string;\n    _readyState: RTCDataChannelState;\n\n    binaryType = 'arraybuffer'; // we only support 'arraybuffer'\n    bufferedAmountLowThreshold = 0;\n\n    constructor(info) {\n        super();\n\n        this._peerConnectionId = info.peerConnectionId;\n        this._reactTag = info.reactTag;\n\n        this._bufferedAmount = 0;\n        this._label = info.label;\n        this._id = info.id === -1 ? null : info.id; // null until negotiated.\n        this._ordered = Boolean(info.ordered);\n        this._maxPacketLifeTime = info.maxPacketLifeTime;\n        this._maxRetransmits = info.maxRetransmits;\n        this._protocol = info.protocol || '';\n        this._negotiated = Boolean(info.negotiated);\n        this._readyState = info.readyState;\n\n        this._registerEvents();\n    }\n\n    get bufferedAmount(): number {\n        return this._bufferedAmount;\n    }\n\n    get label(): string {\n        return this._label;\n    }\n\n    get id(): number {\n        return this._id;\n    }\n\n    get ordered(): boolean {\n        return this._ordered;\n    }\n\n    get maxPacketLifeTime(): number | undefined {\n        return this._maxPacketLifeTime;\n    }\n\n    get maxRetransmits(): number | undefined {\n        return this._maxRetransmits;\n    }\n\n    get protocol(): string {\n        return this._protocol;\n    }\n\n    get negotiated(): boolean {\n        return this._negotiated;\n    }\n\n    get readyState(): string {\n        return this._readyState;\n    }\n\n    send(data: string): void;\n    send(data: ArrayBuffer): void;\n    send(data: ArrayBufferView): void;\n    send(data: string | ArrayBuffer | ArrayBufferView): void {\n        if (typeof data === 'string') {\n            WebRTCModule.dataChannelSend(this._peerConnectionId, this._reactTag, data, 'text');\n\n            return;\n        }\n\n        // Safely convert the buffer object to an Uint8Array for base64-encoding\n        if (ArrayBuffer.isView(data)) {\n            data = new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n        } else if (data instanceof ArrayBuffer) {\n            data = new Uint8Array(data);\n        } else {\n            throw new TypeError('Data must be either string, ArrayBuffer, or ArrayBufferView');\n        }\n\n        const base64data = base64.fromByteArray(data as Uint8Array);\n\n        WebRTCModule.dataChannelSend(this._peerConnectionId, this._reactTag, base64data, 'binary');\n    }\n\n    close(): void {\n        if (this._readyState === 'closing' || this._readyState === 'closed') {\n            return;\n        }\n\n        WebRTCModule.dataChannelClose(this._peerConnectionId, this._reactTag);\n    }\n\n    _registerEvents(): void {\n        addListener(this, 'dataChannelStateChanged', (ev: any) => {\n            if (ev.reactTag !== this._reactTag) {\n                return;\n            }\n\n            this._readyState = ev.state;\n\n            if (this._id === null && ev.id !== -1) {\n                this._id = ev.id;\n            }\n\n            if (this._readyState === 'open') {\n                this.dispatchEvent(new RTCDataChannelEvent('open', { channel: this }));\n            } else if (this._readyState === 'closing') {\n                this.dispatchEvent(new RTCDataChannelEvent('closing', { channel: this }));\n            } else if (this._readyState === 'closed') {\n                this.dispatchEvent(new RTCDataChannelEvent('close', { channel: this }));\n\n                // This DataChannel is done, clean up event handlers.\n                removeListener(this);\n\n                WebRTCModule.dataChannelDispose(this._peerConnectionId, this._reactTag);\n            }\n        });\n\n        addListener(this, 'dataChannelReceiveMessage', (ev: any) => {\n            if (ev.reactTag !== this._reactTag) {\n                return;\n            }\n\n            let data = ev.data;\n\n            if (ev.type === 'binary') {\n                data = base64.toByteArray(ev.data).buffer;\n            }\n\n            this.dispatchEvent(new MessageEvent('message', { data }));\n        });\n\n        addListener(this, 'dataChannelDidChangeBufferedAmount', (ev: any) => {\n            if (ev.reactTag !== this._reactTag) {\n                return;\n            }\n\n            this._bufferedAmount = ev.bufferedAmount;\n\n            if (this._bufferedAmount < this.bufferedAmountLowThreshold) {\n                this.dispatchEvent(new RTCDataChannelEvent('bufferedamountlow', { channel: this }));\n            }\n        });\n    }\n}\n\n/**\n * Define the `onxxx` event handlers.\n */\nconst proto = RTCDataChannel.prototype;\n\ndefineEventAttribute(proto, 'bufferedamountlow');\ndefineEventAttribute(proto, 'close');\ndefineEventAttribute(proto, 'closing');\ndefineEventAttribute(proto, 'error');\ndefineEventAttribute(proto, 'message');\ndefineEventAttribute(proto, 'open');\n"], "mappings": ";AAAA,OAAO,KAAKA,MAAM,MAAM,WAAW;AACnC,SAASC,WAAW,EAAEC,oBAAoB,QAAQ,yBAAyB;AAC3E,SAASC,aAAa,QAAQ,cAAc;AAE5C,SAASC,WAAW,EAAEC,cAAc,QAAQ,gBAAgB;AAC5D,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,mBAAmB,MAAM,uBAAuB;AAEvD,MAAM;EAAEC;AAAa,CAAC,GAAGL,aAAa;AAatC,eAAe,MAAMM,cAAc,SAASR,WAAW,CAAsB;EAc7C;;EAG5BS,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IAACC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,qBAJC,aAAa;IAAAA,eAAA,qCACG,CAAC;IAK1B,IAAI,CAACC,iBAAiB,GAAGF,IAAI,CAACG,gBAAgB;IAC9C,IAAI,CAACC,SAAS,GAAGJ,IAAI,CAACK,QAAQ;IAE9B,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,MAAM,GAAGP,IAAI,CAACQ,KAAK;IACxB,IAAI,CAACC,GAAG,GAAGT,IAAI,CAACU,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,GAAGV,IAAI,CAACU,EAAE,CAAC,CAAC;IAC5C,IAAI,CAACC,QAAQ,GAAGC,OAAO,CAACZ,IAAI,CAACa,OAAO,CAAC;IACrC,IAAI,CAACC,kBAAkB,GAAGd,IAAI,CAACe,iBAAiB;IAChD,IAAI,CAACC,eAAe,GAAGhB,IAAI,CAACiB,cAAc;IAC1C,IAAI,CAACC,SAAS,GAAGlB,IAAI,CAACmB,QAAQ,IAAI,EAAE;IACpC,IAAI,CAACC,WAAW,GAAGR,OAAO,CAACZ,IAAI,CAACqB,UAAU,CAAC;IAC3C,IAAI,CAACC,WAAW,GAAGtB,IAAI,CAACuB,UAAU;IAElC,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;EAEA,IAAIC,cAAcA,CAAA,EAAW;IACzB,OAAO,IAAI,CAACnB,eAAe;EAC/B;EAEA,IAAIE,KAAKA,CAAA,EAAW;IAChB,OAAO,IAAI,CAACD,MAAM;EACtB;EAEA,IAAIG,EAAEA,CAAA,EAAW;IACb,OAAO,IAAI,CAACD,GAAG;EACnB;EAEA,IAAII,OAAOA,CAAA,EAAY;IACnB,OAAO,IAAI,CAACF,QAAQ;EACxB;EAEA,IAAII,iBAAiBA,CAAA,EAAuB;IACxC,OAAO,IAAI,CAACD,kBAAkB;EAClC;EAEA,IAAIG,cAAcA,CAAA,EAAuB;IACrC,OAAO,IAAI,CAACD,eAAe;EAC/B;EAEA,IAAIG,QAAQA,CAAA,EAAW;IACnB,OAAO,IAAI,CAACD,SAAS;EACzB;EAEA,IAAIG,UAAUA,CAAA,EAAY;IACtB,OAAO,IAAI,CAACD,WAAW;EAC3B;EAEA,IAAIG,UAAUA,CAAA,EAAW;IACrB,OAAO,IAAI,CAACD,WAAW;EAC3B;EAKAI,IAAIA,CAACC,IAA4C,EAAQ;IACrD,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC1B9B,YAAY,CAAC+B,eAAe,CAAC,IAAI,CAAC1B,iBAAiB,EAAE,IAAI,CAACE,SAAS,EAAEuB,IAAI,EAAE,MAAM,CAAC;MAElF;IACJ;;IAEA;IACA,IAAIE,WAAW,CAACC,MAAM,CAACH,IAAI,CAAC,EAAE;MAC1BA,IAAI,GAAG,IAAII,UAAU,CAACJ,IAAI,CAACK,MAAM,EAAEL,IAAI,CAACM,UAAU,EAAEN,IAAI,CAACO,UAAU,CAAC;IACxE,CAAC,MAAM,IAAIP,IAAI,YAAYE,WAAW,EAAE;MACpCF,IAAI,GAAG,IAAII,UAAU,CAACJ,IAAI,CAAC;IAC/B,CAAC,MAAM;MACH,MAAM,IAAIQ,SAAS,CAAC,6DAA6D,CAAC;IACtF;IAEA,MAAMC,UAAU,GAAG/C,MAAM,CAACgD,aAAa,CAACV,IAAkB,CAAC;IAE3D9B,YAAY,CAAC+B,eAAe,CAAC,IAAI,CAAC1B,iBAAiB,EAAE,IAAI,CAACE,SAAS,EAAEgC,UAAU,EAAE,QAAQ,CAAC;EAC9F;EAEAE,KAAKA,CAAA,EAAS;IACV,IAAI,IAAI,CAAChB,WAAW,KAAK,SAAS,IAAI,IAAI,CAACA,WAAW,KAAK,QAAQ,EAAE;MACjE;IACJ;IAEAzB,YAAY,CAAC0C,gBAAgB,CAAC,IAAI,CAACrC,iBAAiB,EAAE,IAAI,CAACE,SAAS,CAAC;EACzE;EAEAoB,eAAeA,CAAA,EAAS;IACpB/B,WAAW,CAAC,IAAI,EAAE,yBAAyB,EAAG+C,EAAO,IAAK;MACtD,IAAIA,EAAE,CAACnC,QAAQ,KAAK,IAAI,CAACD,SAAS,EAAE;QAChC;MACJ;MAEA,IAAI,CAACkB,WAAW,GAAGkB,EAAE,CAACC,KAAK;MAE3B,IAAI,IAAI,CAAChC,GAAG,KAAK,IAAI,IAAI+B,EAAE,CAAC9B,EAAE,KAAK,CAAC,CAAC,EAAE;QACnC,IAAI,CAACD,GAAG,GAAG+B,EAAE,CAAC9B,EAAE;MACpB;MAEA,IAAI,IAAI,CAACY,WAAW,KAAK,MAAM,EAAE;QAC7B,IAAI,CAACoB,aAAa,CAAC,IAAI9C,mBAAmB,CAAC,MAAM,EAAE;UAAE+C,OAAO,EAAE;QAAK,CAAC,CAAC,CAAC;MAC1E,CAAC,MAAM,IAAI,IAAI,CAACrB,WAAW,KAAK,SAAS,EAAE;QACvC,IAAI,CAACoB,aAAa,CAAC,IAAI9C,mBAAmB,CAAC,SAAS,EAAE;UAAE+C,OAAO,EAAE;QAAK,CAAC,CAAC,CAAC;MAC7E,CAAC,MAAM,IAAI,IAAI,CAACrB,WAAW,KAAK,QAAQ,EAAE;QACtC,IAAI,CAACoB,aAAa,CAAC,IAAI9C,mBAAmB,CAAC,OAAO,EAAE;UAAE+C,OAAO,EAAE;QAAK,CAAC,CAAC,CAAC;;QAEvE;QACAjD,cAAc,CAAC,IAAI,CAAC;QAEpBG,YAAY,CAAC+C,kBAAkB,CAAC,IAAI,CAAC1C,iBAAiB,EAAE,IAAI,CAACE,SAAS,CAAC;MAC3E;IACJ,CAAC,CAAC;IAEFX,WAAW,CAAC,IAAI,EAAE,2BAA2B,EAAG+C,EAAO,IAAK;MACxD,IAAIA,EAAE,CAACnC,QAAQ,KAAK,IAAI,CAACD,SAAS,EAAE;QAChC;MACJ;MAEA,IAAIuB,IAAI,GAAGa,EAAE,CAACb,IAAI;MAElB,IAAIa,EAAE,CAACK,IAAI,KAAK,QAAQ,EAAE;QACtBlB,IAAI,GAAGtC,MAAM,CAACyD,WAAW,CAACN,EAAE,CAACb,IAAI,CAAC,CAACK,MAAM;MAC7C;MAEA,IAAI,CAACU,aAAa,CAAC,IAAI/C,YAAY,CAAC,SAAS,EAAE;QAAEgC;MAAK,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC;IAEFlC,WAAW,CAAC,IAAI,EAAE,oCAAoC,EAAG+C,EAAO,IAAK;MACjE,IAAIA,EAAE,CAACnC,QAAQ,KAAK,IAAI,CAACD,SAAS,EAAE;QAChC;MACJ;MAEA,IAAI,CAACE,eAAe,GAAGkC,EAAE,CAACf,cAAc;MAExC,IAAI,IAAI,CAACnB,eAAe,GAAG,IAAI,CAACyC,0BAA0B,EAAE;QACxD,IAAI,CAACL,aAAa,CAAC,IAAI9C,mBAAmB,CAAC,mBAAmB,EAAE;UAAE+C,OAAO,EAAE;QAAK,CAAC,CAAC,CAAC;MACvF;IACJ,CAAC,CAAC;EACN;AACJ;;AAEA;AACA;AACA;AACA,MAAMK,KAAK,GAAGlD,cAAc,CAACmD,SAAS;AAEtC1D,oBAAoB,CAACyD,KAAK,EAAE,mBAAmB,CAAC;AAChDzD,oBAAoB,CAACyD,KAAK,EAAE,OAAO,CAAC;AACpCzD,oBAAoB,CAACyD,KAAK,EAAE,SAAS,CAAC;AACtCzD,oBAAoB,CAACyD,KAAK,EAAE,OAAO,CAAC;AACpCzD,oBAAoB,CAACyD,KAAK,EAAE,SAAS,CAAC;AACtCzD,oBAAoB,CAACyD,KAAK,EAAE,MAAM,CAAC"}