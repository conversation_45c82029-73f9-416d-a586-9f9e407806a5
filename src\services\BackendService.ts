import AuthManager from './AuthManager';
import { createClient } from '@supabase/supabase-js';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '../config/supabase';

export interface StreamSession {
  id: string;
  userId: string;
  channelName: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  viewers: number;
  status: 'active' | 'ended' | 'failed';
  metadata?: {
    title?: string;
    description?: string;
    tags?: string[];
  };
}

export interface ViewerSession {
  id: string;
  userId?: string;
  streamId: string;
  joinTime: string;
  leaveTime?: string;
  duration?: number;
  ipAddress?: string;
}

export interface StreamAnalytics {
  totalStreams: number;
  totalViewers: number;
  totalDuration: number;
  averageViewersPerStream: number;
  popularTimes: Array<{
    hour: number;
    streams: number;
    viewers: number;
  }>;
}

class BackendService {
  private supabase;
  private static instance: BackendService;

  constructor() {
    this.supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
  }

  static getInstance(): BackendService {
    if (!BackendService.instance) {
      BackendService.instance = new BackendService();
    }
    return BackendService.instance;
  }

  // Start a new stream session
  async startStreamSession(channelName: string, metadata?: StreamSession['metadata']): Promise<{
    session: StreamSession | null;
    error: string | null;
  }> {
    try {
      const { user } = await AuthManager.getInstance().getCurrentUser();
      if (!user) {
        return { session: null, error: 'User not authenticated' };
      }

      const { data, error } = await this.supabase
        .from('stream_sessions')
        .insert({
          user_id: user.id,
          channel_name: channelName,
          start_time: new Date().toISOString(),
          status: 'active',
          viewers: 0,
          metadata: metadata || {}
        })
        .select()
        .single();

      if (error) {
        console.error('Failed to start stream session:', error);
        return { session: null, error: error.message };
      }

      const session: StreamSession = {
        id: data.id,
        userId: data.user_id,
        channelName: data.channel_name,
        startTime: data.start_time,
        viewers: data.viewers,
        status: data.status,
        metadata: data.metadata
      };

      return { session, error: null };
    } catch (error) {
      console.error('Unexpected error starting stream:', error);
      return { session: null, error: 'Unexpected error' };
    }
  }

  // End a stream session
  async endStreamSession(sessionId: string): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      const { user } = await AuthManager.getInstance().getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const endTime = new Date().toISOString();

      // Get the session to calculate duration
      const { data: sessionData, error: sessionError } = await this.supabase
        .from('stream_sessions')
        .select('start_time')
        .eq('id', sessionId)
        .limit(1);

      if (sessionError || !sessionData || sessionData.length === 0) {
        return { success: false, error: 'Session not found' };
      }

      const session = sessionData[0];

      const duration = new Date(endTime).getTime() - new Date(session.start_time).getTime();

      const { error } = await this.supabase
        .from('stream_sessions')
        .update({
          end_time: endTime,
          duration: Math.floor(duration / 1000), // Convert to seconds
          status: 'ended'
        })
        .eq('id', sessionId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Failed to end stream session:', error);
        return { success: false, error: error.message };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Unexpected error ending stream:', error);
      return { success: false, error: 'Unexpected error' };
    }
  }

  // Record viewer joining a stream
  async recordViewerJoin(streamId: string, ipAddress?: string): Promise<{
    viewerId: string | null;
    error: string | null;
  }> {
    try {
      const { user } = await AuthManager.getInstance().getCurrentUser();

      const { data, error } = await this.supabase
        .from('viewer_sessions')
        .insert({
          user_id: user?.id || null,
          stream_id: streamId,
          join_time: new Date().toISOString(),
          ip_address: ipAddress
        })
        .select()
        .single();

      if (error) {
        console.error('Failed to record viewer join:', error);
        return { viewerId: null, error: error.message };
      }

      // Increment viewer count
      await this.supabase.rpc('increment_viewer_count', {
        session_id: streamId
      });

      return { viewerId: data.id, error: null };
    } catch (error) {
      console.error('Unexpected error recording viewer join:', error);
      return { viewerId: null, error: 'Unexpected error' };
    }
  }

  // Record viewer leaving a stream
  async recordViewerLeave(viewerId: string): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      const leaveTime = new Date().toISOString();

      // Get the viewer session to calculate duration
      const { data: viewerData } = await this.supabase
        .from('viewer_sessions')
        .select('join_time')
        .eq('id', viewerId)
        .single();

      if (!viewerData) {
        return { success: false, error: 'Viewer session not found' };
      }

      const duration = new Date(leaveTime).getTime() - new Date(viewerData.join_time).getTime();

      const { error } = await this.supabase
        .from('viewer_sessions')
        .update({
          leave_time: leaveTime,
          duration: Math.floor(duration / 1000) // Convert to seconds
        })
        .eq('id', viewerId);

      if (error) {
        console.error('Failed to record viewer leave:', error);
        return { success: false, error: error.message };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Unexpected error recording viewer leave:', error);
      return { success: false, error: 'Unexpected error' };
    }
  }

  // Get user's stream history
  async getUserStreamHistory(limit: number = 10): Promise<{
    streams: StreamSession[];
    error: string | null;
  }> {
    try {
      const { user } = await AuthManager.getInstance().getCurrentUser();
      if (!user) {
        return { streams: [], error: 'User not authenticated' };
      }

      const { data, error } = await this.supabase
        .from('stream_sessions')
        .select('*')
        .eq('user_id', user.id)
        .order('start_time', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Failed to get stream history:', error);
        return { streams: [], error: error.message };
      }

      const streams: StreamSession[] = data.map(item => ({
        id: item.id,
        userId: item.user_id,
        channelName: item.channel_name,
        startTime: item.start_time,
        endTime: item.end_time,
        duration: item.duration,
        viewers: item.viewers,
        status: item.status,
        metadata: item.metadata
      }));

      return { streams, error: null };
    } catch (error) {
      console.error('Unexpected error getting stream history:', error);
      return { streams: [], error: 'Unexpected error' };
    }
  }

  // Get active stream by channel name
  async getActiveStream(channelName: string): Promise<{
    id: string | null;
    channelName: string | null;
    error: string | null;
  }> {
    try {
      const { data, error } = await this.supabase
        .from('stream_sessions')
        .select('id, channel_name')
        .eq('channel_name', channelName)
        .eq('status', 'active')
        .order('start_time', { ascending: false })
        .limit(1);

      if (error) {
        console.error('Failed to get active stream:', error);
        return { id: null, channelName: null, error: error.message };
      }

      // Handle case where no active stream is found
      if (!data || data.length === 0) {
        return { id: null, channelName: null, error: null };
      }

      const stream = data[0];
      return { id: stream.id, channelName: stream.channel_name, error: null };
    } catch (error) {
      console.error('Unexpected error getting active stream:', error);
      return { id: null, channelName: null, error: 'Unexpected error' };
    }
  }

  // Get stream analytics
  async getStreamAnalytics(timeframe: 'day' | 'week' | 'month' = 'week'): Promise<{
    analytics: StreamAnalytics;
    error: string | null;
  }> {
    try {
      const { user } = await AuthManager.getInstance().getCurrentUser();
      if (!user) {
        return {
          analytics: {
            totalStreams: 0,
            totalViewers: 0,
            totalDuration: 0,
            averageViewersPerStream: 0,
            popularTimes: []
          },
          error: 'User not authenticated'
        };
      }

      const startDate = new Date();
      switch (timeframe) {
        case 'day':
          startDate.setDate(startDate.getDate() - 1);
          break;
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1);
          break;
      }

      const { data, error } = await this.supabase
        .from('stream_sessions')
        .select('*')
        .eq('user_id', user.id)
        .gte('start_time', startDate.toISOString())
        .order('start_time', { ascending: false });

      if (error) {
        console.error('Failed to get analytics:', error);
        return {
          analytics: {
            totalStreams: 0,
            totalViewers: 0,
            totalDuration: 0,
            averageViewersPerStream: 0,
            popularTimes: []
          },
          error: error.message
        };
      }

      const totalStreams = data.length;
      const totalViewers = data.reduce((sum, session) => sum + session.viewers, 0);
      const totalDuration = data.reduce((sum, session) => sum + (session.duration || 0), 0);
      const averageViewersPerStream = totalStreams > 0 ? totalViewers / totalStreams : 0;

      // Calculate popular times by hour
      const hourlyData = Array(24).fill(null).map((_, hour) => ({
        hour,
        streams: 0,
        viewers: 0
      }));

      data.forEach(session => {
        const hour = new Date(session.start_time).getHours();
        hourlyData[hour].streams++;
        hourlyData[hour].viewers += session.viewers;
      });

      const analytics: StreamAnalytics = {
        totalStreams,
        totalViewers,
        totalDuration,
        averageViewersPerStream,
        popularTimes: hourlyData
      };

      return { analytics, error: null };
    } catch (error) {
      console.error('Unexpected error getting analytics:', error);
      return {
        analytics: {
          totalStreams: 0,
          totalViewers: 0,
          totalDuration: 0,
          averageViewersPerStream: 0,
          popularTimes: []
        },
        error: 'Unexpected error'
      };
    }
  }

  // Get active streams
  async getActiveStreams(): Promise<{
    streams: StreamSession[];
    error: string | null;
  }> {
    try {
      const { data, error } = await this.supabase
        .from('stream_sessions')
        .select('*')
        .eq('status', 'active')
        .order('start_time', { ascending: false });

      if (error) {
        console.error('Failed to get active streams:', error);
        return { streams: [], error: error.message };
      }

      const streams: StreamSession[] = data.map(item => ({
        id: item.id,
        userId: item.user_id,
        channelName: item.channel_name,
        startTime: item.start_time,
        viewers: item.viewers,
        status: item.status,
        metadata: item.metadata
      }));

      return { streams, error: null };
    } catch (error) {
      console.error('Unexpected error getting active streams:', error);
      return { streams: [], error: 'Unexpected error' };
    }
  }

  // Record stream metadata
  async updateStreamMetadata(sessionId: string, metadata: StreamSession['metadata']): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      const { user } = await AuthManager.getInstance().getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const { error } = await this.supabase
        .from('stream_sessions')
        .update({ metadata })
        .eq('id', sessionId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Failed to update stream metadata:', error);
        return { success: false, error: error.message };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Unexpected error updating stream metadata:', error);
      return { success: false, error: 'Unexpected error' };
    }
  }

  // User Preferences methods
  async getUserPreferences(): Promise<{
    preferences: any;
    error: string | null;
  }> {
    try {
      const { user } = await AuthManager.getInstance().getCurrentUser();
      if (!user) {
        return { preferences: {}, error: 'User not authenticated' };
      }

      const { data, error } = await this.supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', user.id)
        .limit(1);

      if (error) {
        console.error('Failed to get user preferences:', error);
        return { preferences: {}, error: error.message };
      }

      // Return first result or empty object if no preferences found
      return { preferences: data && data.length > 0 ? data[0] : {}, error: null };
    } catch (error) {
      console.error('Unexpected error getting user preferences:', error);
      return { preferences: {}, error: 'Unexpected error' };
    }
  }

  async updateUserPreferences(preferences: any): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      const { user } = await AuthManager.getInstance().getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const { error } = await this.supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          ...preferences,
          updated_at: new Date().toISOString(),
        });

      if (error) {
        console.error('Failed to update user preferences:', error);
        return { success: false, error: error.message };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Unexpected error updating user preferences:', error);
      return { success: false, error: 'Unexpected error' };
    }
  }

  // Notification methods
  async getUserNotifications(limit: number = 50): Promise<{
    notifications: any[];
    error: string | null;
  }> {
    try {
      const { user } = await AuthManager.getInstance().getCurrentUser();
      if (!user) {
        return { notifications: [], error: 'User not authenticated' };
      }

      const { data, error } = await this.supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Failed to get notifications:', error);
        return { notifications: [], error: error.message };
      }

      return { notifications: data || [], error: null };
    } catch (error) {
      console.error('Unexpected error getting notifications:', error);
      return { notifications: [], error: 'Unexpected error' };
    }
  }

  async createNotification(notification: {
    type: string;
    title: string;
    message: string;
    data?: any;
    read?: boolean;
  }): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      const { user } = await AuthManager.getInstance().getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const { error } = await this.supabase
        .from('notifications')
        .insert({
          user_id: user.id,
          ...notification,
          created_at: new Date().toISOString(),
        });

      if (error) {
        console.error('Failed to create notification:', error);
        return { success: false, error: error.message };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Unexpected error creating notification:', error);
      return { success: false, error: 'Unexpected error' };
    }
  }

  async markNotificationAsRead(notificationId: string): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      const { user } = await AuthManager.getInstance().getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const { error } = await this.supabase
        .from('notifications')
        .update({ read: true, updated_at: new Date().toISOString() })
        .eq('id', notificationId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Failed to mark notification as read:', error);
        return { success: false, error: error.message };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Unexpected error marking notification as read:', error);
      return { success: false, error: 'Unexpected error' };
    }
  }

  async markAllNotificationsAsRead(): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      const { user } = await AuthManager.getInstance().getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const { error } = await this.supabase
        .from('notifications')
        .update({ read: true, updated_at: new Date().toISOString() })
        .eq('user_id', user.id)
        .eq('read', false);

      if (error) {
        console.error('Failed to mark all notifications as read:', error);
        return { success: false, error: error.message };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Unexpected error marking all notifications as read:', error);
      return { success: false, error: 'Unexpected error' };
    }
  }

  async clearUserNotifications(): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      const { user } = await AuthManager.getInstance().getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const { error } = await this.supabase
        .from('notifications')
        .delete()
        .eq('user_id', user.id);

      if (error) {
        console.error('Failed to clear notifications:', error);
        return { success: false, error: error.message };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Unexpected error clearing notifications:', error);
      return { success: false, error: 'Unexpected error' };
    }
  }

  async sendEmailNotification(data: {
    title: string;
    message: string;
    data?: any;
  }): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      // Email notifications would be handled by a separate email service
      console.log('Email notification:', data);
      return { success: true, error: null };
    } catch (error) {
      console.error('Unexpected error sending email notification:', error);
      return { success: false, error: 'Unexpected error' };
    }
  }

  // Parental consent methods
  async createParentalConsentRequest(parentEmail: string): Promise<{
    requestId: string | null;
    error: string | null;
  }> {
    try {
      const { user } = await AuthManager.getInstance().getCurrentUser();
      if (!user) {
        return { requestId: null, error: 'User not authenticated' };
      }

      const { data, error } = await this.supabase
        .from('parental_consent_requests')
        .insert({
          user_id: user.id,
          parent_email: parentEmail,
          status: 'pending',
          requested_at: new Date().toISOString(),
        })
        .select('id')
        .single();

      if (error) {
        console.error('Failed to create consent request:', error);
        return { requestId: null, error: error.message };
      }

      return { requestId: data.id, error: null };
    } catch (error) {
      console.error('Unexpected error creating consent request:', error);
      return { requestId: null, error: 'Unexpected error' };
    }
  }

  async getParentalConsentStatus(): Promise<{
    status: string;
    error: string | null;
  }> {
    try {
      const { user } = await AuthManager.getInstance().getCurrentUser();
      if (!user) {
        return { status: 'none', error: 'User not authenticated' };
      }

      const { data, error } = await this.supabase
        .from('parental_consent_requests')
        .select('status')
        .eq('user_id', user.id)
        .order('requested_at', { ascending: false })
        .limit(1);

      if (error) {
        console.error('Failed to get consent status:', error);
        return { status: 'none', error: error.message };
      }

      // Return status from first result or 'none' if no requests found
      return { status: data && data.length > 0 ? data[0].status : 'none', error: null };
    } catch (error) {
      console.error('Unexpected error getting consent status:', error);
      return { status: 'none', error: 'Unexpected error' };
    }
  }
}

export default BackendService;