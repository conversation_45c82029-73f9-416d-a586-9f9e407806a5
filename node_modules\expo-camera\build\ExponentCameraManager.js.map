{"version": 3, "file": "ExponentCameraManager.js", "sourceRoot": "", "sources": ["../src/ExponentCameraManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,MAAM,aAAa,GAAwB,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;AAEjF,eAAe,aAAa,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nconst CameraManager: Record<string, any> = requireNativeModule('ExponentCamera');\n\nexport default CameraManager;\n"]}