{"version": 3, "names": ["EventTarget", "Event", "defineEventAttribute", "NativeModules", "addListener", "removeListener", "<PERSON><PERSON>", "deepClone", "normalizeConstraints", "log", "WebRTCModule", "MediaStreamTrack", "constructor", "info", "_defineProperty", "_constraints", "constraints", "_enabled", "enabled", "_settings", "settings", "_muted", "_peerConnectionId", "peerConnectionId", "_readyState", "readyState", "id", "kind", "remote", "_registerEvents", "Boolean", "mediaStreamTrackSetEnabled", "muted", "stop", "_switchCamera", "Error", "deviceId", "facingMode", "applyConstraints", "_setVideoEffects", "names", "mediaStreamTrackSetVideoEffects", "_setVideoEffect", "name", "_setMutedInternal", "dispatchEvent", "_setVolume", "volume", "mediaStreamTrackSetVolume", "normalized", "video", "mediaStreamTrackApplyConstraints", "clone", "getCapabilities", "getConstraints", "getSettings", "ev", "trackId", "debug", "release", "mediaStreamTrackRelease", "proto", "prototype"], "sources": ["MediaStreamTrack.ts"], "sourcesContent": ["import { EventTarget, Event, defineEventAttribute } from 'event-target-shim/index';\nimport { NativeModules } from 'react-native';\n\nimport { MediaTrackConstraints } from './Constraints';\nimport { addListener, removeListener } from './EventEmitter';\nimport Logger from './Logger';\nimport { deepClone, normalizeConstraints } from './RTCUtil';\n\nconst log = new Logger('pc');\nconst { WebRTCModule } = NativeModules;\n\n\ntype MediaStreamTrackState = 'live' | 'ended';\n\nexport type MediaStreamTrackInfo = {\n    id: string;\n    kind: string;\n    remote: boolean;\n    constraints: object;\n    enabled: boolean;\n    settings: object;\n    peerConnectionId: number;\n    readyState: MediaStreamTrackState;\n}\n\nexport type MediaTrackSettings = {\n    width?: number;\n    height?: number;\n    frameRate?: number;\n    facingMode?: string;\n    deviceId?: string;\n    groupId?: string;\n}\n\ntype MediaStreamTrackEventMap = {\n    ended: Event<'ended'>;\n    mute: Event<'mute'>;\n    unmute: Event<'unmute'>;\n}\n\nexport default class MediaStreamTrack extends EventTarget<MediaStreamTrackEventMap> {\n    _constraints: MediaTrackConstraints;\n    _enabled: boolean;\n    _settings: MediaTrackSettings;\n    _muted: boolean;\n    _peerConnectionId: number;\n    _readyState: MediaStreamTrackState;\n\n    readonly id: string;\n    readonly kind: string;\n    readonly label: string = '';\n    readonly remote: boolean;\n\n    constructor(info: MediaStreamTrackInfo) {\n        super();\n\n        this._constraints = info.constraints || {};\n        this._enabled = info.enabled;\n        this._settings = info.settings || {};\n        this._muted = false;\n        this._peerConnectionId = info.peerConnectionId;\n        this._readyState = info.readyState;\n\n        this.id = info.id;\n        this.kind = info.kind;\n        this.remote = info.remote;\n\n        if (!this.remote) {\n            this._registerEvents();\n        }\n    }\n\n    get enabled(): boolean {\n        return this._enabled;\n    }\n\n    set enabled(enabled: boolean) {\n        if (enabled === this._enabled) {\n            return;\n        }\n\n        this._enabled = Boolean(enabled);\n\n        if (this._readyState === 'ended') {\n            return;\n        }\n\n        WebRTCModule.mediaStreamTrackSetEnabled(this.remote ? this._peerConnectionId : -1, this.id, this._enabled);\n    }\n\n    get muted(): boolean {\n        return this._muted;\n    }\n\n    get readyState(): string {\n        return this._readyState;\n    }\n\n    stop(): void {\n        this.enabled = false;\n        this._readyState = 'ended';\n    }\n\n    /**\n     * Private / custom API for switching the cameras on the fly, without the\n     * need for adding / removing tracks or doing any SDP renegotiation.\n     *\n     * This is how the reference application (AppRTCMobile) implements camera\n     * switching.\n     *\n     * @deprecated Use applyConstraints instead.\n     */\n    _switchCamera(): void {\n        if (this.remote) {\n            throw new Error('Not implemented for remote tracks');\n        }\n\n        if (this.kind !== 'video') {\n            throw new Error('Only implemented for video tracks');\n        }\n\n        const constraints = deepClone(this._settings);\n\n        delete constraints.deviceId;\n        constraints.facingMode = this._settings.facingMode === 'user' ? 'environment' : 'user';\n\n        this.applyConstraints(constraints);\n    }\n\n    _setVideoEffects(names: string[]) {\n        if (this.remote) {\n            throw new Error('Not implemented for remote tracks');\n        }\n\n        if (this.kind !== 'video') {\n            throw new Error('Only implemented for video tracks');\n        }\n\n        WebRTCModule.mediaStreamTrackSetVideoEffects(this.id, names);\n    }\n\n    _setVideoEffect(name: string) {\n        this._setVideoEffects([ name ]);\n    }\n\n    /**\n     * Internal function which is used to set the muted state on remote tracks and\n     * emit the mute / unmute event.\n     *\n     * @param muted Whether the track should be marked as muted / unmuted.\n     */\n    _setMutedInternal(muted: boolean) {\n        if (!this.remote) {\n            throw new Error('Track is not remote!');\n        }\n\n        this._muted = muted;\n        this.dispatchEvent(new Event(muted ? 'mute' : 'unmute'));\n    }\n\n    /**\n     * Custom API for setting the volume on an individual audio track.\n     *\n     * @param volume a gain value in the range of 0-10. defaults to 1.0\n     */\n    _setVolume(volume: number) {\n        if (this.kind !== 'audio') {\n            throw new Error('Only implemented for audio tracks');\n        }\n\n        WebRTCModule.mediaStreamTrackSetVolume(this.remote ? this._peerConnectionId : -1, this.id, volume);\n    }\n\n    /**\n     * Applies a new set of constraints to the track.\n     *\n     * @param constraints An object listing the constraints\n     * to apply to the track's constrainable properties; any existing\n     * constraints are replaced with the new values specified, and any\n     * constrainable properties not included are restored to their default\n     * constraints. If this parameter is omitted, all currently set custom\n     * constraints are cleared.\n     */\n    async applyConstraints(constraints?: MediaTrackConstraints): Promise<void> {\n        if (this.kind !== 'video') {\n            throw new Error('Only implemented for video tracks');\n        }\n\n        const normalized = normalizeConstraints({ video: constraints ?? true });\n\n        this._settings = await WebRTCModule.mediaStreamTrackApplyConstraints(this.id, normalized.video);\n        this._constraints = constraints ?? {};\n    }\n\n    clone(): never {\n        throw new Error('Not implemented.');\n    }\n\n    getCapabilities(): never {\n        throw new Error('Not implemented.');\n    }\n\n    getConstraints() {\n        return deepClone(this._constraints);\n    }\n\n    getSettings(): MediaTrackSettings {\n        return deepClone(this._settings);\n    }\n\n    _registerEvents(): void {\n        addListener(this, 'mediaStreamTrackEnded', (ev: any) => {\n            if (ev.trackId !== this.id || this._readyState === 'ended') {\n                return;\n            }\n\n            log.debug(`${this.id} mediaStreamTrackEnded`);\n            this._readyState = 'ended';\n\n            this.dispatchEvent(new Event('ended'));\n        });\n    }\n\n    release(): void {\n        if (this.remote) {\n            return;\n        }\n\n        removeListener(this);\n        WebRTCModule.mediaStreamTrackRelease(this.id);\n    }\n}\n\n/**\n * Define the `onxxx` event handlers.\n */\nconst proto = MediaStreamTrack.prototype;\n\ndefineEventAttribute(proto, 'ended');\ndefineEventAttribute(proto, 'mute');\ndefineEventAttribute(proto, 'unmute');\n"], "mappings": ";AAAA,SAASA,WAAW,EAAEC,KAAK,EAAEC,oBAAoB,QAAQ,yBAAyB;AAClF,SAASC,aAAa,QAAQ,cAAc;AAG5C,SAASC,WAAW,EAAEC,cAAc,QAAQ,gBAAgB;AAC5D,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,SAAS,EAAEC,oBAAoB,QAAQ,WAAW;AAE3D,MAAMC,GAAG,GAAG,IAAIH,MAAM,CAAC,IAAI,CAAC;AAC5B,MAAM;EAAEI;AAAa,CAAC,GAAGP,aAAa;AA+BtC,eAAe,MAAMQ,gBAAgB,SAASX,WAAW,CAA2B;EAahFY,WAAWA,CAACC,IAA0B,EAAE;IACpC,KAAK,CAAC,CAAC;IAACC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,gBAJa,EAAE;IAAAA,eAAA;IAMvB,IAAI,CAACC,YAAY,GAAGF,IAAI,CAACG,WAAW,IAAI,CAAC,CAAC;IAC1C,IAAI,CAACC,QAAQ,GAAGJ,IAAI,CAACK,OAAO;IAC5B,IAAI,CAACC,SAAS,GAAGN,IAAI,CAACO,QAAQ,IAAI,CAAC,CAAC;IACpC,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,iBAAiB,GAAGT,IAAI,CAACU,gBAAgB;IAC9C,IAAI,CAACC,WAAW,GAAGX,IAAI,CAACY,UAAU;IAElC,IAAI,CAACC,EAAE,GAAGb,IAAI,CAACa,EAAE;IACjB,IAAI,CAACC,IAAI,GAAGd,IAAI,CAACc,IAAI;IACrB,IAAI,CAACC,MAAM,GAAGf,IAAI,CAACe,MAAM;IAEzB,IAAI,CAAC,IAAI,CAACA,MAAM,EAAE;MACd,IAAI,CAACC,eAAe,CAAC,CAAC;IAC1B;EACJ;EAEA,IAAIX,OAAOA,CAAA,EAAY;IACnB,OAAO,IAAI,CAACD,QAAQ;EACxB;EAEA,IAAIC,OAAOA,CAACA,OAAgB,EAAE;IAC1B,IAAIA,OAAO,KAAK,IAAI,CAACD,QAAQ,EAAE;MAC3B;IACJ;IAEA,IAAI,CAACA,QAAQ,GAAGa,OAAO,CAACZ,OAAO,CAAC;IAEhC,IAAI,IAAI,CAACM,WAAW,KAAK,OAAO,EAAE;MAC9B;IACJ;IAEAd,YAAY,CAACqB,0BAA0B,CAAC,IAAI,CAACH,MAAM,GAAG,IAAI,CAACN,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,CAACI,EAAE,EAAE,IAAI,CAACT,QAAQ,CAAC;EAC9G;EAEA,IAAIe,KAAKA,CAAA,EAAY;IACjB,OAAO,IAAI,CAACX,MAAM;EACtB;EAEA,IAAII,UAAUA,CAAA,EAAW;IACrB,OAAO,IAAI,CAACD,WAAW;EAC3B;EAEAS,IAAIA,CAAA,EAAS;IACT,IAAI,CAACf,OAAO,GAAG,KAAK;IACpB,IAAI,CAACM,WAAW,GAAG,OAAO;EAC9B;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIU,aAAaA,CAAA,EAAS;IAClB,IAAI,IAAI,CAACN,MAAM,EAAE;MACb,MAAM,IAAIO,KAAK,CAAC,mCAAmC,CAAC;IACxD;IAEA,IAAI,IAAI,CAACR,IAAI,KAAK,OAAO,EAAE;MACvB,MAAM,IAAIQ,KAAK,CAAC,mCAAmC,CAAC;IACxD;IAEA,MAAMnB,WAAW,GAAGT,SAAS,CAAC,IAAI,CAACY,SAAS,CAAC;IAE7C,OAAOH,WAAW,CAACoB,QAAQ;IAC3BpB,WAAW,CAACqB,UAAU,GAAG,IAAI,CAAClB,SAAS,CAACkB,UAAU,KAAK,MAAM,GAAG,aAAa,GAAG,MAAM;IAEtF,IAAI,CAACC,gBAAgB,CAACtB,WAAW,CAAC;EACtC;EAEAuB,gBAAgBA,CAACC,KAAe,EAAE;IAC9B,IAAI,IAAI,CAACZ,MAAM,EAAE;MACb,MAAM,IAAIO,KAAK,CAAC,mCAAmC,CAAC;IACxD;IAEA,IAAI,IAAI,CAACR,IAAI,KAAK,OAAO,EAAE;MACvB,MAAM,IAAIQ,KAAK,CAAC,mCAAmC,CAAC;IACxD;IAEAzB,YAAY,CAAC+B,+BAA+B,CAAC,IAAI,CAACf,EAAE,EAAEc,KAAK,CAAC;EAChE;EAEAE,eAAeA,CAACC,IAAY,EAAE;IAC1B,IAAI,CAACJ,gBAAgB,CAAC,CAAEI,IAAI,CAAE,CAAC;EACnC;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACIC,iBAAiBA,CAACZ,KAAc,EAAE;IAC9B,IAAI,CAAC,IAAI,CAACJ,MAAM,EAAE;MACd,MAAM,IAAIO,KAAK,CAAC,sBAAsB,CAAC;IAC3C;IAEA,IAAI,CAACd,MAAM,GAAGW,KAAK;IACnB,IAAI,CAACa,aAAa,CAAC,IAAI5C,KAAK,CAAC+B,KAAK,GAAG,MAAM,GAAG,QAAQ,CAAC,CAAC;EAC5D;;EAEA;AACJ;AACA;AACA;AACA;EACIc,UAAUA,CAACC,MAAc,EAAE;IACvB,IAAI,IAAI,CAACpB,IAAI,KAAK,OAAO,EAAE;MACvB,MAAM,IAAIQ,KAAK,CAAC,mCAAmC,CAAC;IACxD;IAEAzB,YAAY,CAACsC,yBAAyB,CAAC,IAAI,CAACpB,MAAM,GAAG,IAAI,CAACN,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,CAACI,EAAE,EAAEqB,MAAM,CAAC;EACtG;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMT,gBAAgBA,CAACtB,WAAmC,EAAiB;IACvE,IAAI,IAAI,CAACW,IAAI,KAAK,OAAO,EAAE;MACvB,MAAM,IAAIQ,KAAK,CAAC,mCAAmC,CAAC;IACxD;IAEA,MAAMc,UAAU,GAAGzC,oBAAoB,CAAC;MAAE0C,KAAK,EAAElC,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAI;IAAK,CAAC,CAAC;IAEvE,IAAI,CAACG,SAAS,GAAG,MAAMT,YAAY,CAACyC,gCAAgC,CAAC,IAAI,CAACzB,EAAE,EAAEuB,UAAU,CAACC,KAAK,CAAC;IAC/F,IAAI,CAACnC,YAAY,GAAGC,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAI,CAAC,CAAC;EACzC;EAEAoC,KAAKA,CAAA,EAAU;IACX,MAAM,IAAIjB,KAAK,CAAC,kBAAkB,CAAC;EACvC;EAEAkB,eAAeA,CAAA,EAAU;IACrB,MAAM,IAAIlB,KAAK,CAAC,kBAAkB,CAAC;EACvC;EAEAmB,cAAcA,CAAA,EAAG;IACb,OAAO/C,SAAS,CAAC,IAAI,CAACQ,YAAY,CAAC;EACvC;EAEAwC,WAAWA,CAAA,EAAuB;IAC9B,OAAOhD,SAAS,CAAC,IAAI,CAACY,SAAS,CAAC;EACpC;EAEAU,eAAeA,CAAA,EAAS;IACpBzB,WAAW,CAAC,IAAI,EAAE,uBAAuB,EAAGoD,EAAO,IAAK;MACpD,IAAIA,EAAE,CAACC,OAAO,KAAK,IAAI,CAAC/B,EAAE,IAAI,IAAI,CAACF,WAAW,KAAK,OAAO,EAAE;QACxD;MACJ;MAEAf,GAAG,CAACiD,KAAK,CAAE,GAAE,IAAI,CAAChC,EAAG,wBAAuB,CAAC;MAC7C,IAAI,CAACF,WAAW,GAAG,OAAO;MAE1B,IAAI,CAACqB,aAAa,CAAC,IAAI5C,KAAK,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC,CAAC;EACN;EAEA0D,OAAOA,CAAA,EAAS;IACZ,IAAI,IAAI,CAAC/B,MAAM,EAAE;MACb;IACJ;IAEAvB,cAAc,CAAC,IAAI,CAAC;IACpBK,YAAY,CAACkD,uBAAuB,CAAC,IAAI,CAAClC,EAAE,CAAC;EACjD;AACJ;;AAEA;AACA;AACA;AACA,MAAMmC,KAAK,GAAGlD,gBAAgB,CAACmD,SAAS;AAExC5D,oBAAoB,CAAC2D,KAAK,EAAE,OAAO,CAAC;AACpC3D,oBAAoB,CAAC2D,KAAK,EAAE,MAAM,CAAC;AACnC3D,oBAAoB,CAAC2D,KAAK,EAAE,QAAQ,CAAC"}