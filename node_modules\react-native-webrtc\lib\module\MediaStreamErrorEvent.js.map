{"version": 3, "names": ["MediaStreamErrorEvent", "constructor", "type", "eventInitDict", "_defineProperty", "toString", "Object", "assign"], "sources": ["MediaStreamErrorEvent.ts"], "sourcesContent": ["\nimport type MediaStreamError from './MediaStreamError';\n\nexport default class MediaStreamErrorEvent {\n    type: string;\n    error?: MediaStreamError;\n    constructor(type, eventInitDict) {\n        this.type = type.toString();\n        Object.assign(this, eventInitDict);\n    }\n}\n"], "mappings": ";AAGA,eAAe,MAAMA,qBAAqB,CAAC;EAGvCC,WAAWA,CAACC,IAAI,EAAEC,aAAa,EAAE;IAAAC,eAAA;IAAAA,eAAA;IAC7B,IAAI,CAACF,IAAI,GAAGA,IAAI,CAACG,QAAQ,CAAC,CAAC;IAC3BC,MAAM,CAACC,MAAM,CAAC,IAAI,EAAEJ,aAAa,CAAC;EACtC;AACJ"}