{"name": "streaming-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "detox test", "test:e2e:build": "detox build", "build:android": "expo build:android", "build:ios": "expo build:ios", "build:staging": "expo build:android --release-channel staging", "build:production": "expo build:android --release-channel production", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "analyze": "expo build:android --release-channel production --analyze", "db:push": "supabase db push", "db:reset": "supabase db reset", "db:status": "supabase status", "supabase:start": "supabase start", "supabase:stop": "supabase stop"}, "dependencies": {"@supabase/supabase-js": "^2.0.0", "agora-access-token": "^2.0.4", "expo": "~49.0.15", "expo-camera": "~13.4.4", "expo-constants": "~14.4.2", "expo-dev-client": "~2.4.13", "expo-secure-store": "~12.3.1", "react": "18.2.0", "react-native": "0.72.10", "react-native-agora": "^4.2.1", "react-native-permissions": "^3.10.1", "react-native-url-polyfill": "^2.0.0", "react-native-webrtc": "^124.0.6"}, "devDependencies": {"@babel/core": "^7.20.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.2", "@types/jest": "^29.0.0", "@types/react": "~18.2.14", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "jest-expo": "~49.0.0", "prettier": "^2.8.8", "react-test-renderer": "18.2.0", "ts-jest": "^29.4.0", "typescript": "^5.1.3"}, "private": true}