import mediaDevices from './MediaDevices';
import MediaStream from './MediaStream';
import MediaStreamTrack, { type MediaTrackSettings } from './MediaStreamTrack';
import permissions from './Permissions';
import RTCAudioSession from './RTCAudioSession';
import RTCErrorEvent from './RTCErrorEvent';
import RTCIceCandidate from './RTCIceCandidate';
import RTCPIPView, { startIOSPIP, stopIOSPIP } from './RTCPIPView';
import RTCPeerConnection from './RTCPeerConnection';
import RTCRtpReceiver from './RTCRtpReceiver';
import RTCRtpSender from './RTCRtpSender';
import RTCRtpTransceiver from './RTCRtpTransceiver';
import RTCSessionDescription from './RTCSessionDescription';
import RTCView, { type RTCVideoViewProps, type RTCIOSPIPOptions } from './RTCView';
import ScreenCapturePickerView from './ScreenCapturePickerView';
export { RTCIceCandidate, RTCPeerConnection, RTCSessionDescription, RTCView, RTCPIPView, ScreenCapturePickerView, RTCRtpTransceiver, RTCRtpReceiver, RTCRtpSender, RTCErrorEvent, RTCAudioSession, MediaStream, MediaStreamTrack, type MediaTrackSettings, type RTCVideoViewProps, type RTCIOSPIPOptions, mediaDevices, permissions, registerGlobals, startIOSPIP, stopIOSPIP, };
declare function registerGlobals(): void;
