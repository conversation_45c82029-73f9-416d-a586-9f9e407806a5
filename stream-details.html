<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stream Details - TICK</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #2E7DFF;
            --primary-dark: #1A66E0;
            --primary-light: #EBF3FF;
            --secondary: #FF6B2E;
            --dark: #1A1A2E;
            --light: #F7F9FC;
            --gray: #8A94A6;
            --success: #2ECC71;
            --warning: #F39C12;
            --danger: #E74C3C;
            --card-shadow: 0 8px 16px rgba(0,0,0,0.05);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light);
            color: var(--dark);
            max-width: 100vw;
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        
        .container {
            width: 100%;
            max-width: 480px;
            margin: 0 auto;
            padding: 0;
            display: flex;
            flex-direction: column;
            flex: 1;
            /* 9:16 ratio container */
            aspect-ratio: 9/16;
            overflow: hidden;
            position: relative;
        }
        
        /* Header */
        .header {
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(0,0,0,0.8);
            z-index: 10;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
        }
        
        .back-button {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            z-index: 15;
        }
        
        .stream-actions {
            display: flex;
            gap: 15px;
        }
        
        .stream-action {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            opacity: 0.8;
            transition: var(--transition);
        }
        
        .stream-action:hover {
            opacity: 1;
        }
        
        /* Video Player */
        .video-container {
            width: 100%;
            position: relative;
            background-color: black;
            aspect-ratio: 9/16;
        }
        
        .video-player {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .live-badge {
            position: absolute;
            top: 70px;
            left: 20px;
            background-color: var(--danger);
            color: white;
            font-size: 12px;
            font-weight: 600;
            padding: 4px 10px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 5px;
            z-index: 2;
        }
        
        .live-badge .dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: white;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .video-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
            color: white;
            z-index: 5;
        }
        
        .stream-info {
            margin-bottom: 15px;
        }
        
        .stream-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .stream-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 12px;
            color: rgba(255,255,255,0.8);
        }
        
        .stream-host {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .host-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid white;
        }
        
        .host-name {
            font-weight: 500;
        }
        
        .viewers-count {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .stream-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .control-buttons {
            display: flex;
            gap: 20px;
        }
        
        .control-button {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            opacity: 0.8;
            transition: var(--transition);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }
        
        .control-button:hover {
            opacity: 1;
        }
        
        .control-label {
            font-size: 10px;
            font-weight: 500;
        }
        
        .book-button {
            background-color: var(--primary);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .book-button:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
        }
        
        /* Main Content */
        .main-content {
            flex: 1;
            overflow-y: auto;
            padding: 0 0 80px;
            margin-top: calc(9/16 * 100%);
        }
        
        /* Tabs */
        .tabs {
            display: flex;
            background-color: white;
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 5;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .tab {
            padding: 15px 0;
            margin-right: 20px;
            font-size: 14px;
            font-weight: 500;
            color: var(--gray);
            cursor: pointer;
            position: relative;
            transition: var(--transition);
        }
        
        .tab.active {
            color: var(--primary);
            font-weight: 600;
        }
        
        .tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: var(--primary);
            border-radius: 3px 3px 0 0;
        }
        
        /* Tab Content */
        .tab-content {
            display: none;
            padding: 20px;
        }
        
        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Chat Section */
        .chat-messages {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .chat-message {
            display: flex;
            gap: 10px;
        }
        
        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            overflow: hidden;
            flex-shrink: 0;
        }
        
        .message-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .message-content {
            flex: 1;
            background-color: white;
            border-radius: 12px;
            padding: 12px;
            box-shadow: var(--card-shadow);
        }
        
        .message-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .message-sender {
            font-weight: 600;
            font-size: 14px;
        }
        
        .message-time {
            font-size: 12px;
            color: var(--gray);
        }
        
        .message-text {
            font-size: 14px;
            line-height: 1.4;
        }
        
        .chat-input {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .input-wrapper {
            flex: 1;
            background-color: white;
            border-radius: 24px;
            padding: 5px 15px;
            display: flex;
            align-items: center;
            border: 1px solid #DFE3EA;
            transition: var(--transition);
        }
        
        .input-wrapper:focus-within {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(46, 125, 255, 0.15);
        }
        
        .chat-input-field {
            flex: 1;
            border: none;
            background: none;
            font-family: 'Poppins', sans-serif;
            font-size: 14px;
            padding: 10px 0;
            outline: none;
        }
        
        .send-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary);
            color: white;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            cursor: pointer;
            transition: var(--transition);
            flex-shrink: 0;
        }
        
        .send-button:hover {
            background-color: var(--primary-dark);
            transform: scale(1.05);
        }
        
        /* About Section */
        .about-section {
            background-color: white;
            border-radius: 12px;
            padding: 15px;
            box-shadow: var(--card-shadow);
            margin-bottom: 20px;
        }
        
        .about-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .about-description {
            font-size: 14px;
            color: var(--gray);
            line-height: 1.5;
            margin-bottom: 15px;
        }
        
        .host-info {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px 0;
            border-top: 1px solid rgba(0,0,0,0.05);
        }
        
        .host-info-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .host-info-details {
            flex: 1;
        }
        
        .host-info-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 2px;
        }
        
        .host-info-meta {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 12px;
            color: var(--gray);
        }
        
        .host-rating {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .host-rating i {
            color: #FFD700;
        }
        
        .follow-button {
            background-color: var(--primary-light);
            color: var(--primary);
            border: none;
            border-radius: 50px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .follow-button:hover {
            background-color: var(--primary);
            color: white;
        }
        
        .follow-button.following {
            background-color: var(--primary);
            color: white;
        }
        
        /* Products Section */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .product-card {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.1);
        }
        
        .product-image {
            width: 100%;
            aspect-ratio: 1/1;
            object-fit: cover;
        }
        
        .product-info {
            padding: 12px;
        }
        
        .product-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 42px;
        }
        
        .product-price {
            font-size: 14px;
            font-weight: 600;
            color: var(--primary);
        }
        
        /* Related Streams */
        .related-streams {
            margin-top: 20px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .stream-cards {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding-bottom: 10px;
            scrollbar-width: none;
        }
        
        .stream-cards::-webkit-scrollbar {
            display: none;
        }
        
        .stream-card {
            min-width: 200px;
            border-radius: 12px;
            overflow: hidden;
            background-color: white;
            box-shadow: var(--card-shadow);
            cursor: pointer;
            transition: var(--transition);
            position: relative;
        }
        
        .stream-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.1);
        }
        
        .stream-thumbnail {
            width: 100%;
            height: 120px;
            object-fit: cover;
            display: block;
        }
        
        .stream-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            z-index: 2;
        }
        
        .badge-live {
            background-color: var(--danger);
            color: white;
            display: flex;
            align-items: center;
            gap: 3px;
        }
        
        .badge-replay {
            background-color: var(--gray);
            color: white;
        }
        
        .stream-viewers {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(0,0,0,0.5);
            color: white;
            font-size: 10px;
            padding: 3px 6px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 3px;
        }
        
        .stream-card-info {
            padding: 12px;
        }
        
        .stream-card-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 42px;
        }
        
        .stream-card-host {
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .card-host-avatar {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .card-host-name {
            font-size: 12px;
            color: var(--gray);
        }
        
        /* Floating Action Button */
        .fab {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-color: var(--primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(46, 125, 255, 0.3);
            cursor: pointer;
            transition: var(--transition);
            z-index: 90;
        }
        
        .fab:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 20px rgba(46, 125, 255, 0.4);
        }
        
        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Video Player Section -->
        <div class="video-container">
            <video class="video-player" id="videoPlayer" autoplay muted loop>
                <source src="https://assets.mixkit.co/videos/preview/mixkit-man-working-on-a-house-roof-9963-large.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video>
            
            <header class="header">
                <button class="back-button" onclick="navigateTo('live')">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <div class="stream-actions">
                    <button class="stream-action" id="shareButton">
                        <i class="fas fa-share-alt"></i>
                    </button>
                    <button class="stream-action" id="moreButton">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </header>
            
            <div class="live-badge">
                <div class="dot"></div>
                LIVE
            </div>
            
            <div class="video-overlay">
                <div class="stream-info">
                    <h1 class="stream-title">Kitchen Backsplash Installation Tips</h1>
                    <div class="stream-meta">
                        <div class="stream-host">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Host" class="host-avatar">
                            <span class="host-name">Mike's Tile Co.</span>
                        </div>
                        <div class="viewers-count">
                            <i class="fas fa-eye"></i>
                            <span>128 watching</span>
                        </div>
                    </div>
                </div>
                
                <div class="stream-controls">
                    <div class="control-buttons">
                        <button class="control-button" id="likeButton">
                            <i class="far fa-heart"></i>
                            <span class="control-label">Like</span>
                        </button>
                        <button class="control-button" id="commentButton">
                            <i class="far fa-comment"></i>
                            <span class="control-label">Comment</span>
                        </button>
                        <button class="control-button" id="saveButton">
                            <i class="far fa-bookmark"></i>
                            <span class="control-label">Save</span>
                        </button>
                    </div>
                    
                    <button class="book-button" id="bookButton">
                        <i class="fas fa-calendar-check"></i>
                        Book Now
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            <div class="tabs">
                <div class="tab active" data-tab="chat">Chat</div>
                <div class="tab" data-tab="about">About</div>
                <div class="tab" data-tab="products">Products</div>
            </div>
            
            <!-- Chat Tab -->
            <div class="tab-content active" id="chat-content">
                <div class="chat-messages">
                    <div class="chat-message">
                        <div class="message-avatar">
                            <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="User">
                        </div>
                        <div class="message-content">
                            <div class="message-header">
                                <span class="message-sender">Emily Johnson</span>
                                <span class="message-time">2:34 PM</span>
                            </div>
                            <p class="message-text">What type of tile spacers do you recommend for subway tiles?</p>
                        </div>
                    </div>
                    
                    <div class="chat-message">
                        <div class="message-avatar">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Host">
                        </div>
                        <div class="message-content">
                            <div class="message-header">
                                <span class="message-sender">Mike's Tile Co.</span>
                                <span class="message-time">2:36 PM</span>
                            </div>
                            <p class="message-text">Great question, Emily! For subway tiles, I recommend 1/16" or 1/8" spacers depending on the look you want. Smaller spacers give a more seamless look.</p>
                        </div>
                    </div>
                    
                    <div class="chat-message">
                        <div class="message-avatar">
                            <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="User">
                        </div>
                        <div class="message-content">
                            <div class="message-header">
                                <span class="message-sender">David Wilson</span>
                                <span class="message-time">2:38 PM</span>
                            </div>
                            <p class="message-text">What's the best way to cut around outlets?</p>
                        </div>
                    </div>
                    
                    <div class="chat-message">
                        <div class="message-avatar">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Host">
                        </div>
                        <div class="message-content">
                            <div class="message-header">
                                <span class="message-sender">Mike's Tile Co.</span>
                                <span class="message-time">2:40 PM</span>
                            </div>
                            <p class="message-text">David, I'll show that in a minute! But you'll want to use a wet saw or a tile nipper for precise cuts. Always measure twice and cut once.</p>
                        </div>
                    </div>
                </div>
                
                <div class="chat-input">
                    <div class="input-wrapper">
                        <input type="text" class="chat-input-field" placeholder="Type a comment...">
                    </div>
                    <button class="send-button">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
            
            <!-- About Tab -->
            <div class="tab-content" id="about-content">
                <div class="about-section">
                    <h2 class="about-title">About This Stream</h2>
                    <p class="about-description">
                        Join me as I demonstrate how to install a kitchen backsplash using ceramic subway tiles. I'll cover everything from preparation to grouting and sealing. Perfect for DIY enthusiasts or homeowners planning a kitchen renovation.
                    </p>
                    <p class="about-description">
                        Topics covered:
                        <br>• Surface preparation
                        <br>• Tile layout and planning
                        <br>• Cutting techniques
                        <br>• Adhesive application
                        <br>• Grouting and sealing
                    </p>
                    
                    <div class="host-info">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Host" class="host-info-avatar">
                        <div class="host-info-details">
                            <h3 class="host-info-name">Mike's Tile Co.</h3>
                            <div class="host-info-meta">
                                <div class="host-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.9</span>
                                </div>
                                <span>•</span>
                                <span>Austin, TX</span>
                            </div>
                        </div>
                        <button class="follow-button" id="followButton">
                            <i class="fas fa-plus"></i>
                            Follow
                        </button>
                    </div>
                </div>
                
                <div class="related-streams">
                    <h2 class="section-title">More From This Pro</h2>
                    <div class="stream-cards">
                        <div class="stream-card" onclick="navigateTo('stream-details', {id: 12})">
                            <img src="https://images.unsplash.com/photo-1615971677499-5467cbab01c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80" alt="Stream" class="stream-thumbnail">
                            <div class="stream-badge badge-replay">REPLAY</div>
                            <div class="stream-viewers">
                                <i class="fas fa-play"></i>
                                342
                            </div>
                            <div class="stream-card-info">
                                <h3 class="stream-card-title">Shower Tile Installation Basics</h3>
                                <div class="stream-card-host">
                                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Host" class="card-host-avatar">
                                    <div class="card-host-name">Mike's Tile Co.</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="stream-card" onclick="navigateTo('stream-details', {id: 13})">
                            <img src="https://images.unsplash.com/photo-1609587312208-cea54be969e7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80" alt="Stream" class="stream-thumbnail">
                            <div class="stream-badge badge-replay">REPLAY</div>
                            <div class="stream-viewers">
                                <i class="fas fa-play"></i>
                                289
                            </div>
                            <div class="stream-card-info">
                                <h3 class="stream-card-title">Grouting Tips & Techniques</h3>
                                <div class="stream-card-host">
                                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Host" class="card-host-avatar">
                                    <div class="card-host-name">Mike's Tile Co.</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Products Tab -->
            <div class="tab-content" id="products-content">
                <h2 class="section-title">Products Used In This Stream</h2>
                <div class="products-grid">
                    <div class="product-card" onclick="navigateTo('product-details', {id: 1})">
                        <img src="https://images.unsplash.com/photo-*************-98549f351e36?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80" alt="Product" class="product-image">
                        <div class="product-info">
                            <h3 class="product-name">White Subway Ceramic Tile (3x6 inch)</h3>
                            <div class="product-price">$4.99/sq.ft</div>
                        </div>
                    </div>
                    
                    <div class="product-card" onclick="navigateTo('product-details', {id: 2})">
                        <img src="https://images.unsplash.com/photo-1575652642698-d13f6f3e56a9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80" alt="Product" class="product-image">
                        <div class="product-info">
                            <h3 class="product-name">Premium Tile Adhesive (1 Gallon)</h3>
                            <div class="product-price">$24.99</div>
                        </div>
                    </div>
                    
                    <div class="product-card" onclick="navigateTo('product-details', {id: 3})">
                        <img src="https://images.unsplash.com/photo-1572207485376-c04d1b6a0e2a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80" alt="Product" class="product-image">
                        <div class="product-info">
                            <h3 class="product-name">Tile Spacers (1/8 inch, 200 pack)</h3>
                            <div class="product-price">$8.99</div>
                        </div>
                    </div>
                    
                    <div class="product-card" onclick="navigateTo('product-details', {id: 4})">
                        <img src="https://images.unsplash.com/photo-1581235720704-06d3acfcb36f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80" alt="Product" class="product-image">
                        <div class="product-info">
                            <h3 class="product-name">Sanded Grout (10 lb, White)</h3>
                            <div class="product-price">$19.99</div>
                        </div>
                    </div>
                </div>
                
                <div class="related-streams">
                    <h2 class="section-title">Related Streams</h2>
                    <div class="stream-cards">
                        <div class="stream-card" onclick="navigateTo('stream-details', {id: 14})">
                            <img src="https://images.unsplash.com/photo-1556911220-bff31c812dba?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80" alt="Stream" class="stream-thumbnail">
                            <div class="stream-badge badge-replay">REPLAY</div>
                            <div class="stream-viewers">
                                <i class="fas fa-play"></i>
                                876
                            </div>
                            <div class="stream-card-info">
                                <h3 class="stream-card-title">Budget-Friendly Kitchen Updates</h3>
                                <div class="stream-card-host">
                                    <img src="https://randomuser.me/api/portraits/women/42.jpg" alt="Host" class="card-host-avatar">
                                    <div class="card-host-name">Modern Kitchens</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="stream-card" onclick="navigateTo('stream-details', {id: 15})">
                            <img src="https://images.unsplash.com/photo-1556909114-44e3e9399a2c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80" alt="Stream" class="stream-thumbnail">
                            <div class="stream-badge badge-replay">REPLAY</div>
                            <div class="stream-viewers">
                                <i class="fas fa-play"></i>
                                542
                            </div>
                            <div class="stream-card-info">
                                <h3 class="stream-card-title">Kitchen Countertop Installation</h3>
                                <div class="stream-card-host">
                                    <img src="https://randomuser.me/api/portraits/men/62.jpg" alt="Host" class="card-host-avatar">
                                    <div class="card-host-name">Countertop Pros</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="fab" onclick="navigateTo('ai-assistant')">
            <i class="fas fa-robot"></i>
        </div>
    </div>
    
    <script>
        // DOM Elements
        const tabs = document.querySelectorAll('.tab');
        const tabContents = document.querySelectorAll('.tab-content');
        const videoPlayer = document.getElementById('videoPlayer');
        const likeButton = document.getElementById('likeButton');
        const saveButton = document.getElementById('saveButton');
        const followButton = document.getElementById('followButton');
        const commentInput = document.querySelector('.chat-input-field');
        const sendButton = document.querySelector('.send-button');
        const bookButton = document.getElementById('bookButton');
        const shareButton = document.getElementById('shareButton');
        
        // Get stream ID from URL parameters
        const params = getQueryParams();
        const streamId = params.get('id');
        
        // Initialize
        function init() {
            // Load stream data based on ID
            loadStreamData(streamId);
            
            // Tab switching
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const tabId = tab.getAttribute('data-tab');
                    switchTab(tabId);
                });
            });
            
            // Video player controls
            videoPlayer.addEventListener('click', togglePlayPause);
            
            // Interaction buttons
            likeButton.addEventListener('click', toggleLike);
            saveButton.addEventListener('click', toggleSave);
            followButton.addEventListener('click', toggleFollow);
            
            // Comment functionality
            sendButton.addEventListener('click', sendComment);
            commentInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendComment();
                }
            });
            
            // Book button
            bookButton.addEventListener('click', () => {
                navigateTo('book-service', {id: streamId, provider: 'mikes-tile'});
            });
            
            // Share button
            shareButton.addEventListener('click', shareStream);
        }
        
        // Load stream data
        function loadStreamData(id) {
            // In a real app, this would fetch data from an API
            console.log(`Loading stream data for ID: ${id}`);
            
            // For demo purposes, we're using hardcoded data
            // Adjust UI based on stream type (live vs replay)
            if (id > 3) {
                // If it's a replay, update the badge and controls
                const liveBadge = document.querySelector('.live-badge');
                if (liveBadge) {
                    liveBadge.innerHTML = 'REPLAY';
                    liveBadge.style.backgroundColor = 'var(--gray)';
                }
                
                // Add replay controls
                const viewersCount = document.querySelector('.viewers-count');
                if (viewersCount) {
                    viewersCount.innerHTML = '<i class="fas fa-play"></i><span>876 views</span>';
                }
            }
        }
        
        // Switch tab
        function switchTab(tabId) {
            // Update tab buttons
            tabs.forEach(tab => {
                if (tab.getAttribute('data-tab') === tabId) {
                    tab.classList.add('active');
                } else {
                    tab.classList.remove('active');
                }
            });
            
            // Update tab content
            tabContents.forEach(content => {
                if (content.id === `${tabId}-content`) {
                    content.classList.add('active');
                } else {
                    content.classList.remove('active');
                }
            });
        }
        
        // Toggle video play/pause
        function togglePlayPause() {
            if (videoPlayer.paused) {
                videoPlayer.play();
            } else {
                videoPlayer.pause();
            }
        }
        
        // Toggle like button
        function toggleLike() {
            const icon = likeButton.querySelector('i');
            if (icon.classList.contains('far')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                icon.style.color = '#E74C3C';
                showToast('Added to your likes');
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                icon.style.color = '';
                showToast('Removed from your likes');
            }
        }
        
        // Toggle save button
        function toggleSave() {
            const icon = saveButton.querySelector('i');
            if (icon.classList.contains('far')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                icon.style.color = 'var(--primary)';
                showToast('Saved to your collection');
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                icon.style.color = '';
                showToast('Removed from your collection');
            }
        }
        
        // Toggle follow button
        function toggleFollow() {
            if (followButton.classList.contains('following')) {
                followButton.classList.remove('following');
                followButton.innerHTML = '<i class="fas fa-plus"></i> Follow';
                showToast('Unfollowed Mike\'s Tile Co.');
            } else {
                followButton.classList.add('following');
                followButton.innerHTML = '<i class="fas fa-check"></i> Following';
                showToast('Now following Mike\'s Tile Co.');
            }
        }
        
        // Send comment
        function sendComment() {
            const comment = commentInput.value.trim();
            if (comment === '') return;
            
            // Create new comment element
            const chatMessages = document.querySelector('.chat-messages');
            const newComment = document.createElement('div');
            newComment.className = 'chat-message';
            
            const time = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            
            newComment.innerHTML = `
                <div class="message-avatar">
                    <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User">
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <span class="message-sender">You</span>
                        <span class="message-time">${time}</span>
                    </div>
                    <p class="message-text">${comment}</p>
                </div>
            `;
            
            chatMessages.appendChild(newComment);
            
            // Clear input
            commentInput.value = '';
            
            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            // Simulate host response after delay
            setTimeout(() => {
                const hostResponse = document.createElement('div');
                hostResponse.className = 'chat-message';
                
                hostResponse.innerHTML = `
                    <div class="message-avatar">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Host">
                    </div>
                    <div class="message-content">
                        <div class="message-header">
                            <span class="message-sender">Mike's Tile Co.</span>
                            <span class="message-time">${time}</span>
                        </div>
                        <p class="message-text">Thanks for your question! I'll address that in just a moment.</p>
                    </div>
                `;
                
                chatMessages.appendChild(hostResponse);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }, 3000);
        }
        
        // Share stream
        function shareStream() {
            showToast('Share options opened');
            // In a real app, this would open a share dialog
        }
        
        // Show toast message
        function showToast(message) {
            // Create toast element
            const toast = document.createElement('div');
            toast.style.position = 'fixed';
            toast.style.bottom = '100px';
            toast.style.left = '50%';
            toast.style.transform = 'translateX(-50%)';
            toast.style.backgroundColor = 'rgba(0,0,0,0.7)';
            toast.style.color = 'white';
            toast.style.padding = '10px 20px';
            toast.style.borderRadius = '50px';
            toast.style.fontSize = '14px';
            toast.style.zIndex = '1000';
            toast.textContent = message;
            
            // Add to body
            document.body.appendChild(toast);
            
            // Remove after 2 seconds
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transition = 'opacity 0.5s';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 500);
            }, 2000);
        }
        
        // Initialize on page load
        init();
    </script>
</body>
</html>