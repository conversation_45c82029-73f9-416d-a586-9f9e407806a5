{"version": 3, "names": ["RTCRtpEncodingParameters", "constructor", "init", "_init$rid", "_init$maxBitrate", "_init$maxFramerate", "_init$scaleResolution", "_defineProperty", "active", "_rid", "rid", "_maxBitrate", "maxBitrate", "_maxFramerate", "maxFramerate", "_scaleResolutionDownBy", "scaleResolutionDownBy", "framerate", "bitrate", "resolutionScale", "toJSON", "obj", "Boolean"], "sources": ["RTCRtpEncodingParameters.ts"], "sourcesContent": ["export interface RTCRtpEncodingParametersInit {\n    active: boolean,\n    rid?: string;\n    maxFramerate?: number;\n    maxBitrate?: number;\n    scaleResolutionDownBy?: number;\n}\n\nexport default class RTCRtpEncodingParameters {\n    active: boolean;\n    _rid: string | null;\n    _maxFramerate: number | null;\n    _maxBitrate: number | null;\n    _scaleResolutionDownBy: number | null;\n\n    constructor(init: RTCRtpEncodingParametersInit) {\n        this.active = init.active;\n        this._rid = init.rid ?? null;\n        this._maxBitrate = init.maxBitrate ?? null;\n        this._maxFramerate = init.maxFramerate ?? null;\n        this._scaleResolutionDownBy = init.scaleResolutionDownBy ?? null;\n    }\n\n    get rid() {\n        return this._rid;\n    }\n\n    get maxFramerate() {\n        return this._maxFramerate;\n    }\n\n    set maxFramerate(framerate) {\n        // eslint-disable-next-line eqeqeq\n        if (framerate != null && framerate > 0) {\n            this._maxFramerate = framerate;\n        } else {\n            this._maxFramerate = null;\n        }\n    }\n\n    get maxBitrate() {\n        return this._maxBitrate;\n    }\n\n    set maxBitrate(bitrate) {\n        // eslint-disable-next-line eqeqeq\n        if (bitrate != null && bitrate >= 0) {\n            this._maxBitrate = bitrate;\n        } else {\n            this._maxBitrate = null;\n        }\n    }\n\n    get scaleResolutionDownBy() {\n        return this._scaleResolutionDownBy;\n    }\n\n    set scaleResolutionDownBy(resolutionScale) {\n        // eslint-disable-next-line eqeqeq\n        if (resolutionScale != null && resolutionScale >= 1) {\n            this._scaleResolutionDownBy = resolutionScale;\n        } else {\n            this._scaleResolutionDownBy = null;\n        }\n    }\n\n    toJSON(): RTCRtpEncodingParametersInit {\n        const obj = {\n            active: Boolean(this.active),\n        };\n\n        if (this._rid !== null) {\n            obj['rid'] = this._rid;\n        }\n\n        if (this._maxBitrate !== null) {\n            obj['maxBitrate'] = this._maxBitrate;\n        }\n\n        if (this._maxFramerate !== null) {\n            obj['maxFramerate'] = this._maxFramerate;\n        }\n\n        if (this._scaleResolutionDownBy !== null) {\n            obj['scaleResolutionDownBy'] = this._scaleResolutionDownBy;\n        }\n\n        return obj;\n    }\n}\n"], "mappings": ";AAQA,eAAe,MAAMA,wBAAwB,CAAC;EAO1CC,WAAWA,CAACC,IAAkC,EAAE;IAAA,IAAAC,SAAA,EAAAC,gBAAA,EAAAC,kBAAA,EAAAC,qBAAA;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAC5C,IAAI,CAACC,MAAM,GAAGN,IAAI,CAACM,MAAM;IACzB,IAAI,CAACC,IAAI,IAAAN,SAAA,GAAGD,IAAI,CAACQ,GAAG,cAAAP,SAAA,cAAAA,SAAA,GAAI,IAAI;IAC5B,IAAI,CAACQ,WAAW,IAAAP,gBAAA,GAAGF,IAAI,CAACU,UAAU,cAAAR,gBAAA,cAAAA,gBAAA,GAAI,IAAI;IAC1C,IAAI,CAACS,aAAa,IAAAR,kBAAA,GAAGH,IAAI,CAACY,YAAY,cAAAT,kBAAA,cAAAA,kBAAA,GAAI,IAAI;IAC9C,IAAI,CAACU,sBAAsB,IAAAT,qBAAA,GAAGJ,IAAI,CAACc,qBAAqB,cAAAV,qBAAA,cAAAA,qBAAA,GAAI,IAAI;EACpE;EAEA,IAAII,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACD,IAAI;EACpB;EAEA,IAAIK,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACD,aAAa;EAC7B;EAEA,IAAIC,YAAYA,CAACG,SAAS,EAAE;IACxB;IACA,IAAIA,SAAS,IAAI,IAAI,IAAIA,SAAS,GAAG,CAAC,EAAE;MACpC,IAAI,CAACJ,aAAa,GAAGI,SAAS;IAClC,CAAC,MAAM;MACH,IAAI,CAACJ,aAAa,GAAG,IAAI;IAC7B;EACJ;EAEA,IAAID,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACD,WAAW;EAC3B;EAEA,IAAIC,UAAUA,CAACM,OAAO,EAAE;IACpB;IACA,IAAIA,OAAO,IAAI,IAAI,IAAIA,OAAO,IAAI,CAAC,EAAE;MACjC,IAAI,CAACP,WAAW,GAAGO,OAAO;IAC9B,CAAC,MAAM;MACH,IAAI,CAACP,WAAW,GAAG,IAAI;IAC3B;EACJ;EAEA,IAAIK,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACD,sBAAsB;EACtC;EAEA,IAAIC,qBAAqBA,CAACG,eAAe,EAAE;IACvC;IACA,IAAIA,eAAe,IAAI,IAAI,IAAIA,eAAe,IAAI,CAAC,EAAE;MACjD,IAAI,CAACJ,sBAAsB,GAAGI,eAAe;IACjD,CAAC,MAAM;MACH,IAAI,CAACJ,sBAAsB,GAAG,IAAI;IACtC;EACJ;EAEAK,MAAMA,CAAA,EAAiC;IACnC,MAAMC,GAAG,GAAG;MACRb,MAAM,EAAEc,OAAO,CAAC,IAAI,CAACd,MAAM;IAC/B,CAAC;IAED,IAAI,IAAI,CAACC,IAAI,KAAK,IAAI,EAAE;MACpBY,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAACZ,IAAI;IAC1B;IAEA,IAAI,IAAI,CAACE,WAAW,KAAK,IAAI,EAAE;MAC3BU,GAAG,CAAC,YAAY,CAAC,GAAG,IAAI,CAACV,WAAW;IACxC;IAEA,IAAI,IAAI,CAACE,aAAa,KAAK,IAAI,EAAE;MAC7BQ,GAAG,CAAC,cAAc,CAAC,GAAG,IAAI,CAACR,aAAa;IAC5C;IAEA,IAAI,IAAI,CAACE,sBAAsB,KAAK,IAAI,EAAE;MACtCM,GAAG,CAAC,uBAAuB,CAAC,GAAG,IAAI,CAACN,sBAAsB;IAC9D;IAEA,OAAOM,GAAG;EACd;AACJ"}