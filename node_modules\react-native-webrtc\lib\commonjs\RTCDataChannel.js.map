{"version": 3, "names": ["base64", "_interopRequireWildcard", "require", "_index", "_reactNative", "_EventEmitter", "_MessageEvent", "_interopRequireDefault", "_RTCDataChannelEvent", "obj", "__esModule", "default", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "_defineProperty", "value", "enumerable", "configurable", "writable", "WebRTCModule", "NativeModules", "RTCDataChannel", "EventTarget", "constructor", "info", "_peerConnectionId", "peerConnectionId", "_reactTag", "reactTag", "_bufferedAmount", "_label", "label", "_id", "id", "_ordered", "Boolean", "ordered", "_maxPacketLifeTime", "maxPacketLifeTime", "_maxRetransmits", "maxRetransmits", "_protocol", "protocol", "_negotiated", "negotiated", "_readyState", "readyState", "_registerEvents", "bufferedAmount", "send", "data", "dataChannelSend", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Uint8Array", "buffer", "byteOffset", "byteLength", "TypeError", "base64data", "fromByteArray", "close", "dataChannelClose", "addListener", "ev", "state", "dispatchEvent", "RTCDataChannelEvent", "channel", "removeListener", "dataChannelDispose", "type", "toByteArray", "MessageEvent", "bufferedAmountLowThreshold", "exports", "proto", "defineEventAttribute"], "sources": ["RTCDataChannel.ts"], "sourcesContent": ["import * as base64 from 'base64-js';\nimport { EventTarget, defineEventAttribute } from 'event-target-shim/index';\nimport { NativeModules } from 'react-native';\n\nimport { addListener, removeListener } from './EventEmitter';\nimport MessageEvent from './MessageEvent';\nimport RTCDataChannelEvent from './RTCDataChannelEvent';\n\nconst { WebRTCModule } = NativeModules;\n\ntype RTCDataChannelState = 'connecting' | 'open' | 'closing' | 'closed';\n\ntype DataChannelEventMap = {\n    bufferedamountlow: RTCDataChannelEvent<'bufferedamountlow'>;\n    close: RTCDataChannelEvent<'close'>;\n    closing: RTCDataChannelEvent<'closing'>;\n    error: RTCDataChannelEvent<'error'>;\n    message: MessageEvent<'message'>;\n    open: RTCDataChannelEvent<'open'>;\n};\n\nexport default class RTCDataChannel extends EventTarget<DataChannelEventMap> {\n    _peerConnectionId: number;\n    _reactTag: string;\n\n    _bufferedAmount: number;\n    _id: number;\n    _label: string;\n    _maxPacketLifeTime?: number;\n    _maxRetransmits?: number;\n    _negotiated: boolean;\n    _ordered: boolean;\n    _protocol: string;\n    _readyState: RTCDataChannelState;\n\n    binaryType = 'arraybuffer'; // we only support 'arraybuffer'\n    bufferedAmountLowThreshold = 0;\n\n    constructor(info) {\n        super();\n\n        this._peerConnectionId = info.peerConnectionId;\n        this._reactTag = info.reactTag;\n\n        this._bufferedAmount = 0;\n        this._label = info.label;\n        this._id = info.id === -1 ? null : info.id; // null until negotiated.\n        this._ordered = Boolean(info.ordered);\n        this._maxPacketLifeTime = info.maxPacketLifeTime;\n        this._maxRetransmits = info.maxRetransmits;\n        this._protocol = info.protocol || '';\n        this._negotiated = Boolean(info.negotiated);\n        this._readyState = info.readyState;\n\n        this._registerEvents();\n    }\n\n    get bufferedAmount(): number {\n        return this._bufferedAmount;\n    }\n\n    get label(): string {\n        return this._label;\n    }\n\n    get id(): number {\n        return this._id;\n    }\n\n    get ordered(): boolean {\n        return this._ordered;\n    }\n\n    get maxPacketLifeTime(): number | undefined {\n        return this._maxPacketLifeTime;\n    }\n\n    get maxRetransmits(): number | undefined {\n        return this._maxRetransmits;\n    }\n\n    get protocol(): string {\n        return this._protocol;\n    }\n\n    get negotiated(): boolean {\n        return this._negotiated;\n    }\n\n    get readyState(): string {\n        return this._readyState;\n    }\n\n    send(data: string): void;\n    send(data: ArrayBuffer): void;\n    send(data: ArrayBufferView): void;\n    send(data: string | ArrayBuffer | ArrayBufferView): void {\n        if (typeof data === 'string') {\n            WebRTCModule.dataChannelSend(this._peerConnectionId, this._reactTag, data, 'text');\n\n            return;\n        }\n\n        // Safely convert the buffer object to an Uint8Array for base64-encoding\n        if (ArrayBuffer.isView(data)) {\n            data = new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n        } else if (data instanceof ArrayBuffer) {\n            data = new Uint8Array(data);\n        } else {\n            throw new TypeError('Data must be either string, ArrayBuffer, or ArrayBufferView');\n        }\n\n        const base64data = base64.fromByteArray(data as Uint8Array);\n\n        WebRTCModule.dataChannelSend(this._peerConnectionId, this._reactTag, base64data, 'binary');\n    }\n\n    close(): void {\n        if (this._readyState === 'closing' || this._readyState === 'closed') {\n            return;\n        }\n\n        WebRTCModule.dataChannelClose(this._peerConnectionId, this._reactTag);\n    }\n\n    _registerEvents(): void {\n        addListener(this, 'dataChannelStateChanged', (ev: any) => {\n            if (ev.reactTag !== this._reactTag) {\n                return;\n            }\n\n            this._readyState = ev.state;\n\n            if (this._id === null && ev.id !== -1) {\n                this._id = ev.id;\n            }\n\n            if (this._readyState === 'open') {\n                this.dispatchEvent(new RTCDataChannelEvent('open', { channel: this }));\n            } else if (this._readyState === 'closing') {\n                this.dispatchEvent(new RTCDataChannelEvent('closing', { channel: this }));\n            } else if (this._readyState === 'closed') {\n                this.dispatchEvent(new RTCDataChannelEvent('close', { channel: this }));\n\n                // This DataChannel is done, clean up event handlers.\n                removeListener(this);\n\n                WebRTCModule.dataChannelDispose(this._peerConnectionId, this._reactTag);\n            }\n        });\n\n        addListener(this, 'dataChannelReceiveMessage', (ev: any) => {\n            if (ev.reactTag !== this._reactTag) {\n                return;\n            }\n\n            let data = ev.data;\n\n            if (ev.type === 'binary') {\n                data = base64.toByteArray(ev.data).buffer;\n            }\n\n            this.dispatchEvent(new MessageEvent('message', { data }));\n        });\n\n        addListener(this, 'dataChannelDidChangeBufferedAmount', (ev: any) => {\n            if (ev.reactTag !== this._reactTag) {\n                return;\n            }\n\n            this._bufferedAmount = ev.bufferedAmount;\n\n            if (this._bufferedAmount < this.bufferedAmountLowThreshold) {\n                this.dispatchEvent(new RTCDataChannelEvent('bufferedamountlow', { channel: this }));\n            }\n        });\n    }\n}\n\n/**\n * Define the `onxxx` event handlers.\n */\nconst proto = RTCDataChannel.prototype;\n\ndefineEventAttribute(proto, 'bufferedamountlow');\ndefineEventAttribute(proto, 'close');\ndefineEventAttribute(proto, 'closing');\ndefineEventAttribute(proto, 'error');\ndefineEventAttribute(proto, 'message');\ndefineEventAttribute(proto, 'open');\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AAEA,IAAAG,aAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,oBAAA,GAAAD,sBAAA,CAAAL,OAAA;AAAwD,SAAAK,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAZ,wBAAAQ,GAAA,EAAAI,WAAA,SAAAA,WAAA,IAAAJ,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAQ,KAAA,GAAAL,wBAAA,CAAAC,WAAA,OAAAI,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAT,GAAA,YAAAQ,KAAA,CAAAE,GAAA,CAAAV,GAAA,SAAAW,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAhB,GAAA,QAAAgB,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAnB,GAAA,EAAAgB,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAf,GAAA,EAAAgB,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAhB,GAAA,CAAAgB,GAAA,SAAAL,MAAA,CAAAT,OAAA,GAAAF,GAAA,MAAAQ,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAArB,GAAA,EAAAW,MAAA,YAAAA,MAAA;AAAA,SAAAW,gBAAAtB,GAAA,EAAAgB,GAAA,EAAAO,KAAA,QAAAP,GAAA,IAAAhB,GAAA,IAAAa,MAAA,CAAAC,cAAA,CAAAd,GAAA,EAAAgB,GAAA,IAAAO,KAAA,EAAAA,KAAA,EAAAC,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAA1B,GAAA,CAAAgB,GAAA,IAAAO,KAAA,WAAAvB,GAAA;AAExD,MAAM;EAAE2B;AAAa,CAAC,GAAGC,0BAAa;AAavB,MAAMC,cAAc,SAASC,kBAAW,CAAsB;EAc7C;;EAG5BC,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IAACV,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,qBAJC,aAAa;IAAAA,eAAA,qCACG,CAAC;IAK1B,IAAI,CAACW,iBAAiB,GAAGD,IAAI,CAACE,gBAAgB;IAC9C,IAAI,CAACC,SAAS,GAAGH,IAAI,CAACI,QAAQ;IAE9B,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,MAAM,GAAGN,IAAI,CAACO,KAAK;IACxB,IAAI,CAACC,GAAG,GAAGR,IAAI,CAACS,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,GAAGT,IAAI,CAACS,EAAE,CAAC,CAAC;IAC5C,IAAI,CAACC,QAAQ,GAAGC,OAAO,CAACX,IAAI,CAACY,OAAO,CAAC;IACrC,IAAI,CAACC,kBAAkB,GAAGb,IAAI,CAACc,iBAAiB;IAChD,IAAI,CAACC,eAAe,GAAGf,IAAI,CAACgB,cAAc;IAC1C,IAAI,CAACC,SAAS,GAAGjB,IAAI,CAACkB,QAAQ,IAAI,EAAE;IACpC,IAAI,CAACC,WAAW,GAAGR,OAAO,CAACX,IAAI,CAACoB,UAAU,CAAC;IAC3C,IAAI,CAACC,WAAW,GAAGrB,IAAI,CAACsB,UAAU;IAElC,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;EAEA,IAAIC,cAAcA,CAAA,EAAW;IACzB,OAAO,IAAI,CAACnB,eAAe;EAC/B;EAEA,IAAIE,KAAKA,CAAA,EAAW;IAChB,OAAO,IAAI,CAACD,MAAM;EACtB;EAEA,IAAIG,EAAEA,CAAA,EAAW;IACb,OAAO,IAAI,CAACD,GAAG;EACnB;EAEA,IAAII,OAAOA,CAAA,EAAY;IACnB,OAAO,IAAI,CAACF,QAAQ;EACxB;EAEA,IAAII,iBAAiBA,CAAA,EAAuB;IACxC,OAAO,IAAI,CAACD,kBAAkB;EAClC;EAEA,IAAIG,cAAcA,CAAA,EAAuB;IACrC,OAAO,IAAI,CAACD,eAAe;EAC/B;EAEA,IAAIG,QAAQA,CAAA,EAAW;IACnB,OAAO,IAAI,CAACD,SAAS;EACzB;EAEA,IAAIG,UAAUA,CAAA,EAAY;IACtB,OAAO,IAAI,CAACD,WAAW;EAC3B;EAEA,IAAIG,UAAUA,CAAA,EAAW;IACrB,OAAO,IAAI,CAACD,WAAW;EAC3B;EAKAI,IAAIA,CAACC,IAA4C,EAAQ;IACrD,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC1B/B,YAAY,CAACgC,eAAe,CAAC,IAAI,CAAC1B,iBAAiB,EAAE,IAAI,CAACE,SAAS,EAAEuB,IAAI,EAAE,MAAM,CAAC;MAElF;IACJ;;IAEA;IACA,IAAIE,WAAW,CAACC,MAAM,CAACH,IAAI,CAAC,EAAE;MAC1BA,IAAI,GAAG,IAAII,UAAU,CAACJ,IAAI,CAACK,MAAM,EAAEL,IAAI,CAACM,UAAU,EAAEN,IAAI,CAACO,UAAU,CAAC;IACxE,CAAC,MAAM,IAAIP,IAAI,YAAYE,WAAW,EAAE;MACpCF,IAAI,GAAG,IAAII,UAAU,CAACJ,IAAI,CAAC;IAC/B,CAAC,MAAM;MACH,MAAM,IAAIQ,SAAS,CAAC,6DAA6D,CAAC;IACtF;IAEA,MAAMC,UAAU,GAAG5E,MAAM,CAAC6E,aAAa,CAACV,IAAkB,CAAC;IAE3D/B,YAAY,CAACgC,eAAe,CAAC,IAAI,CAAC1B,iBAAiB,EAAE,IAAI,CAACE,SAAS,EAAEgC,UAAU,EAAE,QAAQ,CAAC;EAC9F;EAEAE,KAAKA,CAAA,EAAS;IACV,IAAI,IAAI,CAAChB,WAAW,KAAK,SAAS,IAAI,IAAI,CAACA,WAAW,KAAK,QAAQ,EAAE;MACjE;IACJ;IAEA1B,YAAY,CAAC2C,gBAAgB,CAAC,IAAI,CAACrC,iBAAiB,EAAE,IAAI,CAACE,SAAS,CAAC;EACzE;EAEAoB,eAAeA,CAAA,EAAS;IACpB,IAAAgB,yBAAW,EAAC,IAAI,EAAE,yBAAyB,EAAGC,EAAO,IAAK;MACtD,IAAIA,EAAE,CAACpC,QAAQ,KAAK,IAAI,CAACD,SAAS,EAAE;QAChC;MACJ;MAEA,IAAI,CAACkB,WAAW,GAAGmB,EAAE,CAACC,KAAK;MAE3B,IAAI,IAAI,CAACjC,GAAG,KAAK,IAAI,IAAIgC,EAAE,CAAC/B,EAAE,KAAK,CAAC,CAAC,EAAE;QACnC,IAAI,CAACD,GAAG,GAAGgC,EAAE,CAAC/B,EAAE;MACpB;MAEA,IAAI,IAAI,CAACY,WAAW,KAAK,MAAM,EAAE;QAC7B,IAAI,CAACqB,aAAa,CAAC,IAAIC,4BAAmB,CAAC,MAAM,EAAE;UAAEC,OAAO,EAAE;QAAK,CAAC,CAAC,CAAC;MAC1E,CAAC,MAAM,IAAI,IAAI,CAACvB,WAAW,KAAK,SAAS,EAAE;QACvC,IAAI,CAACqB,aAAa,CAAC,IAAIC,4BAAmB,CAAC,SAAS,EAAE;UAAEC,OAAO,EAAE;QAAK,CAAC,CAAC,CAAC;MAC7E,CAAC,MAAM,IAAI,IAAI,CAACvB,WAAW,KAAK,QAAQ,EAAE;QACtC,IAAI,CAACqB,aAAa,CAAC,IAAIC,4BAAmB,CAAC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAK,CAAC,CAAC,CAAC;;QAEvE;QACA,IAAAC,4BAAc,EAAC,IAAI,CAAC;QAEpBlD,YAAY,CAACmD,kBAAkB,CAAC,IAAI,CAAC7C,iBAAiB,EAAE,IAAI,CAACE,SAAS,CAAC;MAC3E;IACJ,CAAC,CAAC;IAEF,IAAAoC,yBAAW,EAAC,IAAI,EAAE,2BAA2B,EAAGC,EAAO,IAAK;MACxD,IAAIA,EAAE,CAACpC,QAAQ,KAAK,IAAI,CAACD,SAAS,EAAE;QAChC;MACJ;MAEA,IAAIuB,IAAI,GAAGc,EAAE,CAACd,IAAI;MAElB,IAAIc,EAAE,CAACO,IAAI,KAAK,QAAQ,EAAE;QACtBrB,IAAI,GAAGnE,MAAM,CAACyF,WAAW,CAACR,EAAE,CAACd,IAAI,CAAC,CAACK,MAAM;MAC7C;MAEA,IAAI,CAACW,aAAa,CAAC,IAAIO,qBAAY,CAAC,SAAS,EAAE;QAAEvB;MAAK,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC;IAEF,IAAAa,yBAAW,EAAC,IAAI,EAAE,oCAAoC,EAAGC,EAAO,IAAK;MACjE,IAAIA,EAAE,CAACpC,QAAQ,KAAK,IAAI,CAACD,SAAS,EAAE;QAChC;MACJ;MAEA,IAAI,CAACE,eAAe,GAAGmC,EAAE,CAAChB,cAAc;MAExC,IAAI,IAAI,CAACnB,eAAe,GAAG,IAAI,CAAC6C,0BAA0B,EAAE;QACxD,IAAI,CAACR,aAAa,CAAC,IAAIC,4BAAmB,CAAC,mBAAmB,EAAE;UAAEC,OAAO,EAAE;QAAK,CAAC,CAAC,CAAC;MACvF;IACJ,CAAC,CAAC;EACN;AACJ;;AAEA;AACA;AACA;AAFAO,OAAA,CAAAjF,OAAA,GAAA2B,cAAA;AAGA,MAAMuD,KAAK,GAAGvD,cAAc,CAACZ,SAAS;AAEtC,IAAAoE,2BAAoB,EAACD,KAAK,EAAE,mBAAmB,CAAC;AAChD,IAAAC,2BAAoB,EAACD,KAAK,EAAE,OAAO,CAAC;AACpC,IAAAC,2BAAoB,EAACD,KAAK,EAAE,SAAS,CAAC;AACtC,IAAAC,2BAAoB,EAACD,KAAK,EAAE,OAAO,CAAC;AACpC,IAAAC,2BAAoB,EAACD,KAAK,EAAE,SAAS,CAAC;AACtC,IAAAC,2BAAoB,EAACD,KAAK,EAAE,MAAM,CAAC"}