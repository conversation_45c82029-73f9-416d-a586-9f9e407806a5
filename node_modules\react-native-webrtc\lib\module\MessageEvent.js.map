{"version": 3, "names": ["Event", "MessageEvent", "constructor", "type", "eventInitDict", "_defineProperty", "data"], "sources": ["MessageEvent.ts"], "sourcesContent": ["import { Event } from 'event-target-shim/index';\n\nexport type MessageEventData = string | ArrayBuffer | Blob;\n\ntype MESSAGE_EVENTS = 'message' | 'messageerror';\n\ninterface IMessageEventInitDict extends Event.EventInit {\n    data: MessageEventData;\n}\n\n/**\n * @eventClass\n * This event is fired whenever the RTCDataChannel send message.\n * @param {MESSAGE_EVENTS} type - The type of event.\n * @param {IMessageEventInitDict} eventInitDict - The event init properties.\n * @see\n * {@link https://developer.mozilla.org/en-US/docs/Web/API/RTCDataChannel/message_event#event_type MDN} for details.\n */\nexport default class MessageEvent<TEventType extends MESSAGE_EVENTS> extends Event<TEventType> {\n    /** @eventProperty */\n    data: MessageEventData;\n    constructor(type: TEventType, eventInitDict: IMessageEventInitDict) {\n        super(type, eventInitDict);\n        this.data = eventInitDict.data;\n    }\n}\n"], "mappings": ";AAAA,SAASA,KAAK,QAAQ,yBAAyB;AAU/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMC,YAAY,SAA4CD,KAAK,CAAa;EAC3F;;EAEAE,WAAWA,CAACC,IAAgB,EAAEC,aAAoC,EAAE;IAChE,KAAK,CAACD,IAAI,EAAEC,aAAa,CAAC;IAACC,eAAA;IAC3B,IAAI,CAACC,IAAI,GAAGF,aAAa,CAACE,IAAI;EAClC;AACJ"}