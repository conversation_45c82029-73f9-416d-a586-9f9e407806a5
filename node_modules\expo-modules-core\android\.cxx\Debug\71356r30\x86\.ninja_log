# ninja log v5
4	559	7759272696174783	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/.trae/more/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o	369de4f75b6e1ba3
104	855	7759272699121673	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o	401acb13c572f3d7
29	1489	7759272705406585	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o	3463b22c4baeb74f
60	607	7759272696637759	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/.trae/more/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o	10e01718beb376bc
2940	3037	7759272720966895	../../../../build/intermediates/cxx/Debug/71356r30/obj/x86/libexpo-modules-core.so	71e2ad63cb07e46c
75	2135	7759272711923438	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o	f3036d29813ffb4e
12	664	7759272697221123	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o	a59f8b52aec076e8
92	1617	7759272706670954	CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o	2f933db9efe68267
50	1459	7759272705128155	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o	fa65df2431c60094
66	1914	7759272709709868	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o	d1d42731de52bc70
1	900	7759272699563271	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/.trae/more/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o	d736d25c2d7cd07b
81	2236	7759272712971085	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o	8c0082faa47f4f69
98	1641	7759272706937634	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o	ff0762dd23c526b6
20	1767	7759272708163870	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o	fe818a28e899c8bb
34	1782	7759272708376401	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o	bd94ca4000427bfd
9	1919	7759272709818876	CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o	eb662475d872fd4
124	1982	7759272710405137	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o	79b160e19dc36973
111	2237	7759272712971085	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o	cae0991afdb14aa3
44	2014	7759272710739315	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o	2e8ceb7a654a7723
55	2940	7759272719966626	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o	8506608a10579018
39	2018	7759272710799482	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o	6ad6710074f811d1
16	2184	7759272712407835	CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o	e4c41f68696287c0
25	2193	7759272712543638	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIInteropModuleRegistry.cpp.o	c08010923250c28e
71	2442	7759272715038174	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o	3beca3bfdfd2f8a
117	2528	7759272715896076	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o	a382987cda54d42b
86	2604	7759272716622110	CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o	7c57f881e21ce5
