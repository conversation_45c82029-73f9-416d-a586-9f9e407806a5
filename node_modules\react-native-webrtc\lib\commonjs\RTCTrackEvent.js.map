{"version": 3, "names": ["_index", "require", "_defineProperty", "obj", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "RTCTrackEvent", "Event", "constructor", "type", "eventInitDict", "streams", "transceiver", "receiver", "track", "exports", "default"], "sources": ["RTCTrackEvent.ts"], "sourcesContent": ["import { Event } from 'event-target-shim/index';\n\nimport MediaStream from './MediaStream';\nimport type MediaStreamTrack from './MediaStreamTrack';\nimport RTCRtpReceiver from './RTCRtpReceiver';\nimport RTCRtpTransceiver from './RTCRtpTransceiver';\n\ntype TRACK_EVENTS = 'track'\n\ninterface IRTCTrackEventInitDict extends Event.EventInit {\n    streams: MediaStream[]\n    transceiver: RTCRtpTransceiver\n}\n\n/**\n * @eventClass\n * This event is fired whenever the Track is changed in PeerConnection.\n * @param {TRACK_EVENTS} type - The type of event.\n * @param {IRTCTrackEventInitDict} eventInitDict - The event init properties.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/RTCPeerConnection/track_event MDN} for details.\n */\nexport default class RTCTrackEvent<TEventType extends TRACK_EVENTS> extends Event<TEventType> {\n    /** @eventProperty */\n    readonly streams: MediaStream[] = [];\n\n    /** @eventProperty */\n    readonly transceiver: RTCRtpTransceiver;\n\n    /** @eventProperty */\n    readonly receiver: RTCRtpReceiver | null;\n\n    /** @eventProperty */\n    readonly track: MediaStreamTrack | null;\n\n    constructor(type: TEventType, eventInitDict: IRTCTrackEventInitDict) {\n        super(type, eventInitDict);\n        this.streams = eventInitDict.streams;\n        this.transceiver = eventInitDict.transceiver;\n        this.receiver = eventInitDict.transceiver.receiver;\n        this.track = eventInitDict.transceiver.receiver ? eventInitDict.transceiver.receiver.track : null;\n    }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAAgD,SAAAC,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,QAAAD,GAAA,IAAAD,GAAA,IAAAG,MAAA,CAAAC,cAAA,CAAAJ,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAG,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAP,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAchD;AACA;AACA;AACA;AACA;AACA;AACA;AACe,MAAMQ,aAAa,SAA0CC,YAAK,CAAa;EAC1F;;EAGA;;EAGA;;EAGA;;EAGAC,WAAWA,CAACC,IAAgB,EAAEC,aAAqC,EAAE;IACjE,KAAK,CAACD,IAAI,EAAEC,aAAa,CAAC;IAACb,eAAA,kBAZG,EAAE;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAahC,IAAI,CAACc,OAAO,GAAGD,aAAa,CAACC,OAAO;IACpC,IAAI,CAACC,WAAW,GAAGF,aAAa,CAACE,WAAW;IAC5C,IAAI,CAACC,QAAQ,GAAGH,aAAa,CAACE,WAAW,CAACC,QAAQ;IAClD,IAAI,CAACC,KAAK,GAAGJ,aAAa,CAACE,WAAW,CAACC,QAAQ,GAAGH,aAAa,CAACE,WAAW,CAACC,QAAQ,CAACC,KAAK,GAAG,IAAI;EACrG;AACJ;AAACC,OAAA,CAAAC,OAAA,GAAAV,aAAA"}