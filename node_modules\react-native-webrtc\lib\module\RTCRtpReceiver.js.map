{"version": 3, "names": ["NativeModules", "RTCRtpReceiveParameters", "WebRTCModule", "RTCRtpReceiver", "constructor", "info", "_defineProperty", "_id", "id", "_peerConnectionId", "peerConnectionId", "_rtpParameters", "rtpParameters", "track", "_track", "getCapabilities", "kind", "receiverGetCapabilities", "getStats", "receiverGetStats", "then", "data", "Map", "JSON", "parse", "getParameters"], "sources": ["RTCRtpReceiver.ts"], "sourcesContent": ["import { NativeModules } from 'react-native';\n\nimport MediaStreamTrack from './MediaStreamTrack';\nimport RTCRtpCapabilities from './RTCRtpCapabilities';\nimport { RTCRtpParametersInit } from './RTCRtpParameters';\nimport RTCRtpReceiveParameters from './RTCRtpReceiveParameters';\n\nconst { WebRTCModule } = NativeModules;\n\nexport default class RTCRtpReceiver {\n    _id: string;\n    _peerConnectionId: number;\n    _track: MediaStreamTrack | null = null;\n    _rtpParameters: RTCRtpReceiveParameters;\n\n    constructor(info: {\n        peerConnectionId: number,\n        id: string,\n        track?: MediaStreamTrack,\n        rtpParameters: RTCRtpParametersInit\n    }) {\n        this._id = info.id;\n        this._peerConnectionId = info.peerConnectionId;\n        this._rtpParameters = new RTCRtpReceiveParameters(info.rtpParameters);\n\n        if (info.track) {\n            this._track = info.track;\n        }\n    }\n\n    static getCapabilities(kind: 'audio' | 'video'): RTCRtpCapabilities {\n        return WebRTCModule.receiverGetCapabilities(kind);\n    }\n\n    getStats() {\n        return WebRTCModule.receiverGetStats(this._peerConnectionId, this._id).then(data =>\n            /* On both Android and iOS it is faster to construct a single\n            JSON string representing the Map of StatsReports and have it\n            pass through the React Native bridge rather than the Map of\n            StatsReports. While the implementations do try to be faster in\n            general, the stress is on being faster to pass through the React\n            Native bridge which is a bottleneck that tends to be visible in\n            the UI when there is congestion involving UI-related passing.\n            */\n            new Map(JSON.parse(data))\n        );\n    }\n\n    getParameters(): RTCRtpReceiveParameters {\n        return this._rtpParameters;\n    }\n\n    get id() {\n        return this._id;\n    }\n\n    get track() {\n        return this._track;\n    }\n}\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,cAAc;AAK5C,OAAOC,uBAAuB,MAAM,2BAA2B;AAE/D,MAAM;EAAEC;AAAa,CAAC,GAAGF,aAAa;AAEtC,eAAe,MAAMG,cAAc,CAAC;EAMhCC,WAAWA,CAACC,IAKX,EAAE;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA,iBAR+B,IAAI;IAAAA,eAAA;IASlC,IAAI,CAACC,GAAG,GAAGF,IAAI,CAACG,EAAE;IAClB,IAAI,CAACC,iBAAiB,GAAGJ,IAAI,CAACK,gBAAgB;IAC9C,IAAI,CAACC,cAAc,GAAG,IAAIV,uBAAuB,CAACI,IAAI,CAACO,aAAa,CAAC;IAErE,IAAIP,IAAI,CAACQ,KAAK,EAAE;MACZ,IAAI,CAACC,MAAM,GAAGT,IAAI,CAACQ,KAAK;IAC5B;EACJ;EAEA,OAAOE,eAAeA,CAACC,IAAuB,EAAsB;IAChE,OAAOd,YAAY,CAACe,uBAAuB,CAACD,IAAI,CAAC;EACrD;EAEAE,QAAQA,CAAA,EAAG;IACP,OAAOhB,YAAY,CAACiB,gBAAgB,CAAC,IAAI,CAACV,iBAAiB,EAAE,IAAI,CAACF,GAAG,CAAC,CAACa,IAAI,CAACC,IAAI;IAC5E;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;IACY,IAAIC,GAAG,CAACC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC,CAC5B,CAAC;EACL;EAEAI,aAAaA,CAAA,EAA4B;IACrC,OAAO,IAAI,CAACd,cAAc;EAC9B;EAEA,IAAIH,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACD,GAAG;EACnB;EAEA,IAAIM,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;AACJ"}