{"version": 3, "names": ["_react", "require", "_reactNative", "_interopRequireWildcard", "_RTC<PERSON>iew", "_interopRequireDefault", "obj", "__esModule", "default", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "_extends", "assign", "target", "i", "arguments", "length", "source", "apply", "RTCPIPView", "forwardRef", "props", "ref", "_rtcViewProps$iosPIP", "_rtcViewProps$iosPIP2", "rtcViewProps", "fallback<PERSON><PERSON><PERSON>", "iosPIP", "React", "createElement", "startIOSPIP", "UIManager", "dispatchViewManagerCommand", "ReactNative", "findNodeHandle", "current", "getViewManagerConfig", "Commands", "stopIOSPIP", "_default", "exports"], "sources": ["RTCPIPView.tsx"], "sourcesContent": ["import { Component, forwardRef } from 'react';\nimport ReactNative, { UIManager } from 'react-native';\n\nimport RTCView, { RTCIOSPIPOptions, RTCVideoViewProps } from './RTCView';\n\nexport interface RTCPIPViewProps extends RTCVideoViewProps {\n  iosPIP?: RTCIOSPIPOptions & {\n    fallbackView?: Component;\n  };\n}\n\ntype RTCViewInstance = InstanceType<typeof RTCView>;\n\n/**\n * A convenience wrapper around RTCView to handle the fallback view as a prop.\n */\nconst RTCPIPView = forwardRef<RTCViewInstance, RTCPIPViewProps>((props, ref) => {\n    const rtcViewProps = { ...props };\n    const fallbackView = rtcViewProps.iosPIP?.fallbackView;\n\n    delete rtcViewProps.iosPIP?.fallbackView;\n\n    return (\n        <RTCView ref={ref}\n            {...rtcViewProps}>\n            {fallbackView}\n        </RTCView>\n    );\n});\n\nexport function startIOSPIP(ref) {\n    UIManager.dispatchViewManagerCommand(\n        ReactNative.findNodeHandle(ref.current),\n        UIManager.getViewManagerConfig('RTCVideoView').Commands.startIOSPIP,\n        []\n    );\n}\n\nexport function stopIOSPIP(ref) {\n    UIManager.dispatchViewManagerCommand(\n        ReactNative.findNodeHandle(ref.current),\n        UIManager.getViewManagerConfig('RTCVideoView').Commands.stopIOSPIP,\n        []\n    );\n}\n\nexport default RTCPIPView;"], "mappings": ";;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAC,uBAAA,CAAAF,OAAA;AAEA,IAAAG,QAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAAyE,SAAAI,uBAAAC,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAP,wBAAAG,GAAA,EAAAI,WAAA,SAAAA,WAAA,IAAAJ,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAQ,KAAA,GAAAL,wBAAA,CAAAC,WAAA,OAAAI,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAT,GAAA,YAAAQ,KAAA,CAAAE,GAAA,CAAAV,GAAA,SAAAW,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAhB,GAAA,QAAAgB,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAnB,GAAA,EAAAgB,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAf,GAAA,EAAAgB,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAhB,GAAA,CAAAgB,GAAA,SAAAL,MAAA,CAAAT,OAAA,GAAAF,GAAA,MAAAQ,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAArB,GAAA,EAAAW,MAAA,YAAAA,MAAA;AAAA,SAAAW,SAAA,IAAAA,QAAA,GAAAT,MAAA,CAAAU,MAAA,cAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAT,GAAA,IAAAY,MAAA,QAAAf,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAS,MAAA,EAAAZ,GAAA,KAAAQ,MAAA,CAAAR,GAAA,IAAAY,MAAA,CAAAZ,GAAA,gBAAAQ,MAAA,YAAAF,QAAA,CAAAO,KAAA,OAAAH,SAAA;AAUzE;AACA;AACA;AACA,MAAMI,UAAU,gBAAG,IAAAC,iBAAU,EAAmC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAAA,IAAAC,oBAAA,EAAAC,qBAAA;EAC5E,MAAMC,YAAY,GAAG;IAAE,GAAGJ;EAAM,CAAC;EACjC,MAAMK,YAAY,IAAAH,oBAAA,GAAGE,YAAY,CAACE,MAAM,cAAAJ,oBAAA,uBAAnBA,oBAAA,CAAqBG,YAAY;EAEtD,CAAAF,qBAAA,GAAOC,YAAY,CAACE,MAAM,cAAAH,qBAAA,qBAA1B,OAAOA,qBAAA,CAAqBE,YAAY;EAExC,oBACIE,KAAA,CAAAC,aAAA,CAAC1C,QAAA,CAAAI,OAAO,EAAAoB,QAAA;IAACW,GAAG,EAAEA;EAAI,GACVG,YAAY,GACfC,YACI,CAAC;AAElB,CAAC,CAAC;AAEK,SAASI,WAAWA,CAACR,GAAG,EAAE;EAC7BS,sBAAS,CAACC,0BAA0B,CAChCC,oBAAW,CAACC,cAAc,CAACZ,GAAG,CAACa,OAAO,CAAC,EACvCJ,sBAAS,CAACK,oBAAoB,CAAC,cAAc,CAAC,CAACC,QAAQ,CAACP,WAAW,EACnE,EACJ,CAAC;AACL;AAEO,SAASQ,UAAUA,CAAChB,GAAG,EAAE;EAC5BS,sBAAS,CAACC,0BAA0B,CAChCC,oBAAW,CAACC,cAAc,CAACZ,GAAG,CAACa,OAAO,CAAC,EACvCJ,sBAAS,CAACK,oBAAoB,CAAC,cAAc,CAAC,CAACC,QAAQ,CAACC,UAAU,EAClE,EACJ,CAAC;AACL;AAAC,IAAAC,QAAA,GAEcpB,UAAU;AAAAqB,OAAA,CAAAjD,OAAA,GAAAgD,QAAA"}