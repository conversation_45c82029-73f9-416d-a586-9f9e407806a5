{"version": 3, "names": [], "sources": ["Constraints.ts"], "sourcesContent": ["\nexport type MediaTrackConstraints = {\n    width?: ConstrainNumber;\n    height?: ConstrainNumber;\n    frameRate?: ConstrainNumber;\n    facingMode?: ConstrainString;\n    deviceId?: ConstrainString;\n    groupId?: ConstrainString;\n}\n\ntype ConstrainNumber = number | {\n    exact?: number,\n    ideal?: number,\n    max?: number,\n    min?: number,\n}\n\ntype ConstrainString = string | {\n    exact?: string,\n    ideal?: string,\n}"], "mappings": ""}