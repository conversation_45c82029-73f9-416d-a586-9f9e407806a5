/**
 * Real Video Display Component for WebRTC Streams
 * Supports both local and remote video streams
 */

import React from 'react';
import { View, StyleSheet, Platform } from 'react-native';

interface Props {
  streamURL?: string | null;
  stream?: any; // MediaStream
  style?: any;
  mirror?: boolean;
  objectFit?: 'contain' | 'cover';
}

export default function RTCVideoView({ 
  streamURL, 
  stream, 
  style, 
  mirror = false, 
  objectFit = 'cover' 
}: Props) {
  
  if (Platform.OS === 'web') {
    // Web implementation with HTML video element
    const videoRef = React.useRef<HTMLVideoElement>(null);
    
    React.useEffect(() => {
      if (videoRef.current && stream) {
        videoRef.current.srcObject = stream;
      }
    }, [stream]);
    
    return (
      <div style={{ ...style, position: 'relative' }}>
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted={mirror} // Mute local video to prevent feedback
          style={{
            width: '100%',
            height: '100%',
            objectFit: objectFit,
            backgroundColor: '#000',
            transform: mirror ? 'scaleX(-1)' : 'none',
          }}
        />
      </div>
    );
  } else {
    // React Native implementation
    try {
      const { RTCView } = require('react-native-webrtc');
      
      return (
        <RTCView
          streamURL={streamURL}
          style={[
            styles.video,
            style,
            mirror && styles.mirror,
          ]}
          objectFit={objectFit}
        />
      );
    } catch (error) {
      console.log('RTCView not available, showing placeholder');
      // Fallback placeholder
      return (
        <View style={[styles.placeholder, style]}>
          {/* Placeholder for when RTCView is not available */}
        </View>
      );
    }
  }
}

const styles = StyleSheet.create({
  video: {
    backgroundColor: '#000',
  },
  mirror: {
    transform: [{ scaleX: -1 }],
  },
  placeholder: {
    backgroundColor: '#1a1a1a',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
