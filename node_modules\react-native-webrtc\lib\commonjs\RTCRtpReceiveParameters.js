"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _RTCRtpParameters = _interopRequireDefault(require("./RTCRtpParameters"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class RTCRtpReceiveParameters extends _RTCRtpParameters.default {
  constructor(init) {
    super(init);
  }
}
exports.default = RTCRtpReceiveParameters;
//# sourceMappingURL=RTCRtpReceiveParameters.js.map