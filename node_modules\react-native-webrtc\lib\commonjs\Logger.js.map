{"version": 3, "names": ["_debug", "_interopRequireDefault", "require", "obj", "__esModule", "default", "_defineProperty", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "<PERSON><PERSON>", "enable", "ns", "debug", "constructor", "prefix", "_prefix", "ROOT_PREFIX", "_info", "_warn", "_error", "log", "console", "bind", "msg", "info", "warn", "error", "err", "_err$stack", "trace", "stack", "exports"], "sources": ["Logger.ts"], "sourcesContent": ["import debug from 'debug';\n\n\nexport default class Logger {\n    static ROOT_PREFIX = 'rn-webrtc';\n\n    private _debug: debug.Debugger;\n    private _info: debug.Debugger;\n    private _warn: debug.Debugger;\n    private _error: debug.Debugger;\n\n    static enable(ns: string): void {\n        debug.enable(ns);\n    }\n\n    constructor(prefix: string) {\n        const _prefix = `${Logger.ROOT_PREFIX}:${prefix}`;\n\n        this._debug = debug(`${_prefix}:DEBUG`);\n        this._info = debug(`${_prefix}:INFO`);\n        this._warn = debug(`${_prefix}:WARN`);\n        this._error = debug(`${_prefix}:ERROR`);\n\n        const log = console.log.bind(console);\n\n        this._debug.log = log;\n        this._info.log = log;\n        this._warn.log = log;\n        this._error.log = log;\n    }\n\n    debug(msg: string): void {\n        this._debug(msg);\n    }\n\n    info(msg: string): void {\n        this._info(msg);\n    }\n\n    warn(msg: string): void {\n        this._warn(msg);\n    }\n\n    error(msg: string, err?: Error): void {\n        const trace = err?.stack ?? 'N/A';\n\n        this._error(`${msg} Trace: ${trace}`);\n    }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA0B,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,gBAAAH,GAAA,EAAAI,GAAA,EAAAC,KAAA,QAAAD,GAAA,IAAAJ,GAAA,IAAAM,MAAA,CAAAC,cAAA,CAAAP,GAAA,EAAAI,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAG,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAV,GAAA,CAAAI,GAAA,IAAAC,KAAA,WAAAL,GAAA;AAGX,MAAMW,MAAM,CAAC;EAQxB,OAAOC,MAAMA,CAACC,EAAU,EAAQ;IAC5BC,cAAK,CAACF,MAAM,CAACC,EAAE,CAAC;EACpB;EAEAE,WAAWA,CAACC,MAAc,EAAE;IAAAb,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACxB,MAAMc,OAAO,GAAI,GAAEN,MAAM,CAACO,WAAY,IAAGF,MAAO,EAAC;IAEjD,IAAI,CAACnB,MAAM,GAAG,IAAAiB,cAAK,EAAE,GAAEG,OAAQ,QAAO,CAAC;IACvC,IAAI,CAACE,KAAK,GAAG,IAAAL,cAAK,EAAE,GAAEG,OAAQ,OAAM,CAAC;IACrC,IAAI,CAACG,KAAK,GAAG,IAAAN,cAAK,EAAE,GAAEG,OAAQ,OAAM,CAAC;IACrC,IAAI,CAACI,MAAM,GAAG,IAAAP,cAAK,EAAE,GAAEG,OAAQ,QAAO,CAAC;IAEvC,MAAMK,GAAG,GAAGC,OAAO,CAACD,GAAG,CAACE,IAAI,CAACD,OAAO,CAAC;IAErC,IAAI,CAAC1B,MAAM,CAACyB,GAAG,GAAGA,GAAG;IACrB,IAAI,CAACH,KAAK,CAACG,GAAG,GAAGA,GAAG;IACpB,IAAI,CAACF,KAAK,CAACE,GAAG,GAAGA,GAAG;IACpB,IAAI,CAACD,MAAM,CAACC,GAAG,GAAGA,GAAG;EACzB;EAEAR,KAAKA,CAACW,GAAW,EAAQ;IACrB,IAAI,CAAC5B,MAAM,CAAC4B,GAAG,CAAC;EACpB;EAEAC,IAAIA,CAACD,GAAW,EAAQ;IACpB,IAAI,CAACN,KAAK,CAACM,GAAG,CAAC;EACnB;EAEAE,IAAIA,CAACF,GAAW,EAAQ;IACpB,IAAI,CAACL,KAAK,CAACK,GAAG,CAAC;EACnB;EAEAG,KAAKA,CAACH,GAAW,EAAEI,GAAW,EAAQ;IAAA,IAAAC,UAAA;IAClC,MAAMC,KAAK,IAAAD,UAAA,GAAGD,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEG,KAAK,cAAAF,UAAA,cAAAA,UAAA,GAAI,KAAK;IAEjC,IAAI,CAACT,MAAM,CAAE,GAAEI,GAAI,WAAUM,KAAM,EAAC,CAAC;EACzC;AACJ;AAACE,OAAA,CAAA/B,OAAA,GAAAS,MAAA;AAAAR,eAAA,CA7CoBQ,MAAM,iBACF,WAAW"}