{"version": 3, "names": ["DEFAULT_AUDIO_CONSTRAINTS", "DEFAULT_VIDEO_CONSTRAINTS", "facingMode", "frameRate", "height", "width", "FACING_MODES", "ASPECT_RATIO", "STANDARD_OFFER_OPTIONS", "ice<PERSON><PERSON>", "offertoreceiveaudio", "offertoreceivevideo", "voiceactivitydetection", "SDP_TYPES", "getDefaultMediaConstraints", "mediaType", "TypeError", "extractString", "constraints", "prop", "value", "type", "v", "extractNumber", "Number", "parseInt", "normalizeMediaConstraints", "c", "deviceId", "includes", "Math", "round", "chr4", "random", "toString", "slice", "uniqueID", "deepClone", "obj", "JSON", "parse", "stringify", "isSdpTypeValid", "normalizeOfferOptions", "options", "newOptions", "key", "Object", "entries", "new<PERSON>ey", "toLowerCase", "String", "Boolean", "normalizeConstraints", "mediaTypeConstraints", "typeofMediaTypeConstraints"], "sources": ["RTCUtil.ts"], "sourcesContent": ["\nconst DEFAULT_AUDIO_CONSTRAINTS = {};\n\nconst DEFAULT_VIDEO_CONSTRAINTS = {\n    facingMode: 'user',\n    frameRate: 30,\n    height: 720,\n    width: 1280\n};\n\nconst FACING_MODES = [ 'user', 'environment' ];\n\nconst ASPECT_RATIO = 16 / 9;\n\nexport type RTCOfferOptions  = {\n    iceRestart?:boolean;\n    offerToReceiveAudio?: boolean;\n    offerToReceiveVideo?: boolean;\n    voiceActivityDetection?:boolean\n};\n\nconst STANDARD_OFFER_OPTIONS = {\n    icerestart: 'IceRestart',\n    offertoreceiveaudio: 'OfferToReceiveAudio',\n    offertoreceivevideo: 'OfferToReceiveVideo',\n    voiceactivitydetection: 'VoiceActivityDetection'\n};\n\nconst SDP_TYPES = [\n    'offer',\n    'pranswer',\n    'answer',\n    'rollback'\n];\n\nfunction getDefaultMediaConstraints(mediaType) {\n    switch (mediaType) {\n        case 'audio':\n            return DEFAULT_AUDIO_CONSTRAINTS;\n        case 'video':\n            return DEFAULT_VIDEO_CONSTRAINTS;\n        default:\n            throw new TypeError(`Invalid media type: ${mediaType}`);\n    }\n}\n\nfunction extractString(constraints, prop) {\n    const value = constraints[prop];\n    const type = typeof value;\n\n    if (type === 'object') {\n        for (const v of [ 'exact', 'ideal' ]) {\n            if (value[v]) {\n                return value[v];\n            }\n        }\n    } else if (type === 'string') {\n        return value;\n    }\n}\n\nfunction extractNumber(constraints, prop) {\n    const value = constraints[prop];\n    const type = typeof value;\n\n    if (type === 'number') {\n        return Number.parseInt(value);\n    } else if (type === 'object') {\n        for (const v of [ 'exact', 'ideal', 'max', 'min' ]) {\n            if (value[v]) {\n                return Number.parseInt(value[v]);\n            }\n        }\n    }\n}\n\nfunction normalizeMediaConstraints(constraints, mediaType) {\n    switch (mediaType) {\n        case 'audio':\n            return constraints;\n\n        case 'video': {\n            const c = {\n                deviceId: extractString(constraints, 'deviceId'),\n                facingMode: extractString(constraints, 'facingMode'),\n                frameRate: extractNumber(constraints, 'frameRate'),\n                height: extractNumber(constraints, 'height'),\n                width: extractNumber(constraints, 'width')\n            };\n\n            if (!c.deviceId) {\n                delete c.deviceId;\n            }\n\n            if (!FACING_MODES.includes(c.facingMode)) {\n                c.facingMode = DEFAULT_VIDEO_CONSTRAINTS.facingMode;\n            }\n\n            if (!c.frameRate) {\n                c.frameRate = DEFAULT_VIDEO_CONSTRAINTS.frameRate;\n            }\n\n            if (!c.height && !c.width) {\n                c.height = DEFAULT_VIDEO_CONSTRAINTS.height;\n                c.width = DEFAULT_VIDEO_CONSTRAINTS.width;\n            } else if (!c.height && c.width) {\n                c.height = Math.round(c.width / ASPECT_RATIO);\n            } else if (!c.width && c.height) {\n                c.width = Math.round(c.height * ASPECT_RATIO);\n            }\n\n            return c;\n        }\n\n        default:\n            throw new TypeError(`Invalid media type: ${mediaType}`);\n    }\n}\n\n/**\n * Utility for creating short random strings from float point values.\n * We take 4 characters from the end after converting to a string.\n * Conversion to string gives us some letters as we don't want just numbers.\n * Should be suitable to pass for enough randomness.\n *\n * @return {String} 4 random characters\n */\nfunction chr4() {\n    return Math.random().toString(16).slice(-4);\n}\n\n/**\n * Put together a random string in UUIDv4 format {xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx}\n *\n * @return {String} uuidv4\n */\nexport function uniqueID(): string {\n    return `${chr4()}${chr4()}-${chr4()}-${chr4()}-${chr4()}-${chr4()}${chr4()}${chr4()}`;\n}\n\n/**\n * Utility for deep cloning an object. Object.assign() only does a shallow copy.\n *\n * @param {Object} obj - object to be cloned\n * @return {Object} cloned obj\n */\nexport function deepClone<T>(obj: T): T {\n    return JSON.parse(JSON.stringify(obj));\n}\n\n/**\n * Checks whether an SDP type is valid or not.\n *\n * @param type SDP type to check.\n * @returns Whether the SDP type is valid or not.\n */\nexport function isSdpTypeValid(type: string): boolean {\n    return SDP_TYPES.includes(type);\n}\n\n/**\n * Normalize options passed to createOffer().\n *\n * @param options - user supplied options\n * @return Normalized options\n */\nexport function normalizeOfferOptions(options?: RTCOfferOptions) {\n    const newOptions: Record<string,string> = {};\n\n    if (typeof options !== 'object') {\n        return newOptions;\n    }\n\n    // Convert standard options into WebRTC internal constant names.\n    // See: https://github.com/jitsi/webrtc/blob/0cd6ce4de669bed94ba47b88cb71b9be0341bb81/sdk/media_constraints.cc#L113\n    for (const [ key, value ] of Object.entries(options)) {\n        const newKey = STANDARD_OFFER_OPTIONS[key.toLowerCase()];\n\n        if (newKey) {\n            newOptions[newKey] = String(Boolean(value));\n        }\n    }\n\n    return newOptions;\n}\n\n/**\n * Normalize the given constraints in something we can work with.\n */\nexport function normalizeConstraints(constraints) {\n    const c = deepClone(constraints);\n\n    for (const mediaType of [ 'audio', 'video' ]) {\n        const mediaTypeConstraints = c[mediaType];\n        const typeofMediaTypeConstraints = typeof mediaTypeConstraints;\n\n        if (typeofMediaTypeConstraints !== 'undefined') {\n            if (typeofMediaTypeConstraints === 'boolean') {\n                if (mediaTypeConstraints) {\n                    c[mediaType] = getDefaultMediaConstraints(mediaType);\n                }\n            } else if (typeofMediaTypeConstraints === 'object') {\n                c[mediaType] = normalizeMediaConstraints(mediaTypeConstraints, mediaType);\n            } else {\n                throw new TypeError(`constraints.${mediaType} is neither a boolean nor a dictionary`);\n            }\n        }\n    }\n\n    return c;\n}\n"], "mappings": "AACA,MAAMA,yBAAyB,GAAG,CAAC,CAAC;AAEpC,MAAMC,yBAAyB,GAAG;EAC9BC,UAAU,EAAE,MAAM;EAClBC,SAAS,EAAE,EAAE;EACbC,MAAM,EAAE,GAAG;EACXC,KAAK,EAAE;AACX,CAAC;AAED,MAAMC,YAAY,GAAG,CAAE,MAAM,EAAE,aAAa,CAAE;AAE9C,MAAMC,YAAY,GAAG,EAAE,GAAG,CAAC;AAS3B,MAAMC,sBAAsB,GAAG;EAC3BC,UAAU,EAAE,YAAY;EACxBC,mBAAmB,EAAE,qBAAqB;EAC1CC,mBAAmB,EAAE,qBAAqB;EAC1CC,sBAAsB,EAAE;AAC5B,CAAC;AAED,MAAMC,SAAS,GAAG,CACd,OAAO,EACP,UAAU,EACV,QAAQ,EACR,UAAU,CACb;AAED,SAASC,0BAA0BA,CAACC,SAAS,EAAE;EAC3C,QAAQA,SAAS;IACb,KAAK,OAAO;MACR,OAAOf,yBAAyB;IACpC,KAAK,OAAO;MACR,OAAOC,yBAAyB;IACpC;MACI,MAAM,IAAIe,SAAS,CAAE,uBAAsBD,SAAU,EAAC,CAAC;EAC/D;AACJ;AAEA,SAASE,aAAaA,CAACC,WAAW,EAAEC,IAAI,EAAE;EACtC,MAAMC,KAAK,GAAGF,WAAW,CAACC,IAAI,CAAC;EAC/B,MAAME,IAAI,GAAG,OAAOD,KAAK;EAEzB,IAAIC,IAAI,KAAK,QAAQ,EAAE;IACnB,KAAK,MAAMC,CAAC,IAAI,CAAE,OAAO,EAAE,OAAO,CAAE,EAAE;MAClC,IAAIF,KAAK,CAACE,CAAC,CAAC,EAAE;QACV,OAAOF,KAAK,CAACE,CAAC,CAAC;MACnB;IACJ;EACJ,CAAC,MAAM,IAAID,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAOD,KAAK;EAChB;AACJ;AAEA,SAASG,aAAaA,CAACL,WAAW,EAAEC,IAAI,EAAE;EACtC,MAAMC,KAAK,GAAGF,WAAW,CAACC,IAAI,CAAC;EAC/B,MAAME,IAAI,GAAG,OAAOD,KAAK;EAEzB,IAAIC,IAAI,KAAK,QAAQ,EAAE;IACnB,OAAOG,MAAM,CAACC,QAAQ,CAACL,KAAK,CAAC;EACjC,CAAC,MAAM,IAAIC,IAAI,KAAK,QAAQ,EAAE;IAC1B,KAAK,MAAMC,CAAC,IAAI,CAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAE,EAAE;MAChD,IAAIF,KAAK,CAACE,CAAC,CAAC,EAAE;QACV,OAAOE,MAAM,CAACC,QAAQ,CAACL,KAAK,CAACE,CAAC,CAAC,CAAC;MACpC;IACJ;EACJ;AACJ;AAEA,SAASI,yBAAyBA,CAACR,WAAW,EAAEH,SAAS,EAAE;EACvD,QAAQA,SAAS;IACb,KAAK,OAAO;MACR,OAAOG,WAAW;IAEtB,KAAK,OAAO;MAAE;QACV,MAAMS,CAAC,GAAG;UACNC,QAAQ,EAAEX,aAAa,CAACC,WAAW,EAAE,UAAU,CAAC;UAChDhB,UAAU,EAAEe,aAAa,CAACC,WAAW,EAAE,YAAY,CAAC;UACpDf,SAAS,EAAEoB,aAAa,CAACL,WAAW,EAAE,WAAW,CAAC;UAClDd,MAAM,EAAEmB,aAAa,CAACL,WAAW,EAAE,QAAQ,CAAC;UAC5Cb,KAAK,EAAEkB,aAAa,CAACL,WAAW,EAAE,OAAO;QAC7C,CAAC;QAED,IAAI,CAACS,CAAC,CAACC,QAAQ,EAAE;UACb,OAAOD,CAAC,CAACC,QAAQ;QACrB;QAEA,IAAI,CAACtB,YAAY,CAACuB,QAAQ,CAACF,CAAC,CAACzB,UAAU,CAAC,EAAE;UACtCyB,CAAC,CAACzB,UAAU,GAAGD,yBAAyB,CAACC,UAAU;QACvD;QAEA,IAAI,CAACyB,CAAC,CAACxB,SAAS,EAAE;UACdwB,CAAC,CAACxB,SAAS,GAAGF,yBAAyB,CAACE,SAAS;QACrD;QAEA,IAAI,CAACwB,CAAC,CAACvB,MAAM,IAAI,CAACuB,CAAC,CAACtB,KAAK,EAAE;UACvBsB,CAAC,CAACvB,MAAM,GAAGH,yBAAyB,CAACG,MAAM;UAC3CuB,CAAC,CAACtB,KAAK,GAAGJ,yBAAyB,CAACI,KAAK;QAC7C,CAAC,MAAM,IAAI,CAACsB,CAAC,CAACvB,MAAM,IAAIuB,CAAC,CAACtB,KAAK,EAAE;UAC7BsB,CAAC,CAACvB,MAAM,GAAG0B,IAAI,CAACC,KAAK,CAACJ,CAAC,CAACtB,KAAK,GAAGE,YAAY,CAAC;QACjD,CAAC,MAAM,IAAI,CAACoB,CAAC,CAACtB,KAAK,IAAIsB,CAAC,CAACvB,MAAM,EAAE;UAC7BuB,CAAC,CAACtB,KAAK,GAAGyB,IAAI,CAACC,KAAK,CAACJ,CAAC,CAACvB,MAAM,GAAGG,YAAY,CAAC;QACjD;QAEA,OAAOoB,CAAC;MACZ;IAEA;MACI,MAAM,IAAIX,SAAS,CAAE,uBAAsBD,SAAU,EAAC,CAAC;EAC/D;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiB,IAAIA,CAAA,EAAG;EACZ,OAAOF,IAAI,CAACG,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC/C;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAAA,EAAW;EAC/B,OAAQ,GAAEJ,IAAI,CAAC,CAAE,GAAEA,IAAI,CAAC,CAAE,IAAGA,IAAI,CAAC,CAAE,IAAGA,IAAI,CAAC,CAAE,IAAGA,IAAI,CAAC,CAAE,IAAGA,IAAI,CAAC,CAAE,GAAEA,IAAI,CAAC,CAAE,GAAEA,IAAI,CAAC,CAAE,EAAC;AACzF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,SAASA,CAAIC,GAAM,EAAK;EACpC,OAAOC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAAC,CAAC;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,cAAcA,CAACrB,IAAY,EAAW;EAClD,OAAOR,SAAS,CAACgB,QAAQ,CAACR,IAAI,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASsB,qBAAqBA,CAACC,OAAyB,EAAE;EAC7D,MAAMC,UAAiC,GAAG,CAAC,CAAC;EAE5C,IAAI,OAAOD,OAAO,KAAK,QAAQ,EAAE;IAC7B,OAAOC,UAAU;EACrB;;EAEA;EACA;EACA,KAAK,MAAM,CAAEC,GAAG,EAAE1B,KAAK,CAAE,IAAI2B,MAAM,CAACC,OAAO,CAACJ,OAAO,CAAC,EAAE;IAClD,MAAMK,MAAM,GAAGzC,sBAAsB,CAACsC,GAAG,CAACI,WAAW,CAAC,CAAC,CAAC;IAExD,IAAID,MAAM,EAAE;MACRJ,UAAU,CAACI,MAAM,CAAC,GAAGE,MAAM,CAACC,OAAO,CAAChC,KAAK,CAAC,CAAC;IAC/C;EACJ;EAEA,OAAOyB,UAAU;AACrB;;AAEA;AACA;AACA;AACA,OAAO,SAASQ,oBAAoBA,CAACnC,WAAW,EAAE;EAC9C,MAAMS,CAAC,GAAGU,SAAS,CAACnB,WAAW,CAAC;EAEhC,KAAK,MAAMH,SAAS,IAAI,CAAE,OAAO,EAAE,OAAO,CAAE,EAAE;IAC1C,MAAMuC,oBAAoB,GAAG3B,CAAC,CAACZ,SAAS,CAAC;IACzC,MAAMwC,0BAA0B,GAAG,OAAOD,oBAAoB;IAE9D,IAAIC,0BAA0B,KAAK,WAAW,EAAE;MAC5C,IAAIA,0BAA0B,KAAK,SAAS,EAAE;QAC1C,IAAID,oBAAoB,EAAE;UACtB3B,CAAC,CAACZ,SAAS,CAAC,GAAGD,0BAA0B,CAACC,SAAS,CAAC;QACxD;MACJ,CAAC,MAAM,IAAIwC,0BAA0B,KAAK,QAAQ,EAAE;QAChD5B,CAAC,CAACZ,SAAS,CAAC,GAAGW,yBAAyB,CAAC4B,oBAAoB,EAAEvC,SAAS,CAAC;MAC7E,CAAC,MAAM;QACH,MAAM,IAAIC,SAAS,CAAE,eAAcD,SAAU,wCAAuC,CAAC;MACzF;IACJ;EACJ;EAEA,OAAOY,CAAC;AACZ"}