{"logs": [{"outputFile": "com.streamingapp-mergeDebugResources-61:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\01b98716bd18db7c75d98a3148ec3aaa\\transformed\\core-1.8.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "42", "startColumns": "4", "startOffsets": "4361", "endColumns": "100", "endOffsets": "4457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\808043cb4cf2ad49d6936bcf600e8cf8\\transformed\\biometric-1.1.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,256,374,512,655,776,909,1053,1153,1291,1436", "endColumns": "109,90,117,137,142,120,132,143,99,137,144,121", "endOffsets": "160,251,369,507,650,771,904,1048,1148,1286,1431,1553"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2777,2887,2978,3096,3234,3377,3498,3631,3775,3875,4013,4158", "endColumns": "109,90,117,137,142,120,132,143,99,137,144,121", "endOffsets": "2882,2973,3091,3229,3372,3493,3626,3770,3870,4008,4153,4275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7c5e03387f63f3788dbd5294d085d57\\transformed\\appcompat-1.4.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,41", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,4280", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,4356"}}]}]}