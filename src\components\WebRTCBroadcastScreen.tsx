/**
 * WebRTC-based Broadcasting Screen
 * Real streaming without native dependencies
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  StatusBar,
  Platform,
} from 'react-native';
import { Camera } from 'expo-camera';
import { webRTCStreamingService } from '../services/WebRTCStreamingService';
import RTCVideoView from './RTCVideoView';

interface Props {
  channelName: string;
  onStop: () => void;
}

export default function WebRTCBroadcastScreen({ channelName, onStop }: Props) {
  const [isStreaming, setIsStreaming] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [viewerCount, setViewerCount] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [cameraType, setCameraType] = useState('front' as any);
  const [connectionState, setConnectionState] = useState('new');
  const [error, setError] = useState<string | null>(null);
  const [localStream, setLocalStream] = useState<any>(null);
  const [localStreamURL, setLocalStreamURL] = useState<string | null>(null);

  const localVideoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    initializeStreaming();
    return () => {
      webRTCStreamingService.stopStreaming();
    };
  }, []);

  const initializeStreaming = async () => {
    try {
      setIsLoading(true);

      await webRTCStreamingService.initialize({
        channelName,
        isHost: true,
        onRemoteStream: (stream) => {
          console.log('Viewer connected');
          setViewerCount(prev => prev + 1);
        },
        onConnectionStateChange: (state) => {
          setConnectionState(state);
          if (state === 'connected') {
            setViewerCount(prev => prev + 1);
          } else if (state === 'disconnected') {
            setViewerCount(prev => Math.max(0, prev - 1));
          }
        },
        onError: (error) => {
          setError(error.message);
          Alert.alert('Streaming Error', error.message);
        },
      });

      setIsLoading(false);
    } catch (error) {
      console.error('Failed to initialize streaming:', error);
      setError('Failed to initialize streaming');
      setIsLoading(false);
    }
  };

  const startStreaming = async () => {
    try {
      setIsLoading(true);
      await webRTCStreamingService.startStreaming();

      // Get local stream and display it
      const stream = webRTCStreamingService.getLocalStream();
      if (stream) {
        setLocalStream(stream);

        // For React Native, get stream URL
        if (Platform.OS !== 'web') {
          try {
            const streamURL = stream.toURL ? stream.toURL() : null;
            setLocalStreamURL(streamURL);
          } catch (error) {
            console.log('Could not get stream URL:', error);
          }
        }

        // For web, set video element
        if (localVideoRef.current) {
          localVideoRef.current.srcObject = stream;
        }
      }

      setIsStreaming(true);
      setIsLoading(false);
    } catch (error) {
      console.error('Failed to start streaming:', error);
      Alert.alert('Error', 'Failed to start streaming');
      setIsLoading(false);
    }
  };

  const stopStreaming = async () => {
    try {
      setIsLoading(true);
      await webRTCStreamingService.stopStreaming();
      setIsStreaming(false);
      setViewerCount(0);
      setIsLoading(false);
      onStop();
    } catch (error) {
      console.error('Failed to stop streaming:', error);
      setIsLoading(false);
    }
  };

  const toggleMute = () => {
    webRTCStreamingService.toggleMute();
    setIsMuted(!isMuted);
  };

  const switchCamera = () => {
    webRTCStreamingService.switchCamera();
    setCameraType(
      cameraType === 'front' ? 'back' : 'front'
    );
  };

  const renderStreamDisplay = () => {
    if (Platform.OS === 'web') {
      // Web implementation with HTML video element
      return (
        <div style={{ flex: 1, position: 'relative' }}>
          <video
            ref={localVideoRef}
            autoPlay
            muted
            playsInline
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              backgroundColor: '#000',
            }}
          />
          {renderOverlay()}
        </div>
      );
    } else {
      // Mobile implementation with real video or fallback to Expo Camera
      if (localStream || localStreamURL) {
        return (
          <View style={styles.camera}>
            <RTCVideoView
              stream={localStream}
              streamURL={localStreamURL}
              style={styles.camera}
              mirror={true}
              objectFit="cover"
            />
            {renderOverlay()}
          </View>
        );
      } else {
        // Fallback to Expo Camera
        return (
          <Camera style={styles.camera} type={cameraType}>
            {renderOverlay()}
          </Camera>
        );
      }
    }
  };

  const renderOverlay = () => (
    <View style={styles.overlay}>
      <View style={styles.topControls}>
        <TouchableOpacity style={styles.backButton} onPress={stopStreaming}>
          <Text style={styles.backButtonText}>← Stop</Text>
        </TouchableOpacity>

        <View style={styles.liveIndicator}>
          <View style={[styles.liveDot, { backgroundColor: isStreaming ? '#ff4444' : '#666' }]} />
          <Text style={styles.liveText}>
            {isStreaming ? 'LIVE' : connectionState.toUpperCase()}
          </Text>
          <Text style={styles.viewerText}>{viewerCount} viewers</Text>
        </View>
      </View>

      <View style={styles.centerInfo}>
        <Text style={styles.streamingText}>
          {isStreaming ? '🔴 Broadcasting Live via WebRTC' : '📡 Preparing Stream...'}
        </Text>
        <Text style={styles.channelText}>Channel: {channelName}</Text>
        {error && <Text style={styles.errorText}>Error: {error}</Text>}
      </View>

      <View style={styles.bottomControls}>
        <TouchableOpacity
          style={[styles.controlButton, { opacity: isMuted ? 0.5 : 1 }]}
          onPress={toggleMute}
        >
          <Text style={styles.controlButtonText}>{isMuted ? '🔇' : '🎤'}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.controlButton, styles.stopButton]}
          onPress={isStreaming ? stopStreaming : startStreaming}
          disabled={isLoading}
        >
          <Text style={styles.stopButtonText}>
            {isLoading ? '⏳' : isStreaming ? '⏹️ Stop' : '▶️ Start'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.controlButton} onPress={switchCamera}>
          <Text style={styles.controlButtonText}>🔄</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar hidden />
      {renderStreamDisplay()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    justifyContent: 'space-between',
    padding: 20,
  },
  topControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  backButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  liveIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  liveDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  liveText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginRight: 8,
  },
  viewerText: {
    color: '#fff',
    fontSize: 12,
  },
  centerInfo: {
    alignItems: 'center',
  },
  streamingText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  channelText: {
    color: '#fff',
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.8,
  },
  errorText: {
    color: '#ff4444',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 8,
  },
  bottomControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  controlButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  controlButtonText: {
    fontSize: 24,
  },
  stopButton: {
    backgroundColor: '#ff4444',
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  stopButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
