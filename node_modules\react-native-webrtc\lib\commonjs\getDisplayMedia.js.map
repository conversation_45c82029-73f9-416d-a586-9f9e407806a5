{"version": 3, "names": ["_reactNative", "require", "_MediaStream", "_interopRequireDefault", "_MediaStreamError", "obj", "__esModule", "default", "WebRTCModule", "NativeModules", "getDisplayMedia", "Promise", "resolve", "reject", "then", "data", "streamId", "track", "info", "streamReactTag", "tracks", "stream", "MediaStream", "error", "MediaStreamError"], "sources": ["getDisplayMedia.ts"], "sourcesContent": ["\nimport { NativeModules } from 'react-native';\n\nimport MediaStream from './MediaStream';\nimport MediaStreamError from './MediaStreamError';\n\nconst { WebRTCModule } = NativeModules;\n\nexport default function getDisplayMedia(): Promise<MediaStream> {\n    return new Promise((resolve, reject) => {\n        WebRTCModule.getDisplayMedia().then(\n            data => {\n                const { streamId, track } = data;\n\n                const info = {\n                    streamId: streamId,\n                    streamReactTag: streamId,\n                    tracks: [ track ]\n                };\n\n                const stream = new MediaStream(info);\n\n                resolve(stream);\n            },\n            error => {\n                reject(new MediaStreamError(error));\n            }\n        );\n    });\n}\n"], "mappings": ";;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AAEA,IAAAC,YAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,iBAAA,GAAAD,sBAAA,CAAAF,OAAA;AAAkD,SAAAE,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAElD,MAAM;EAAEG;AAAa,CAAC,GAAGC,0BAAa;AAEvB,SAASC,eAAeA,CAAA,EAAyB;EAC5D,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACpCL,YAAY,CAACE,eAAe,CAAC,CAAC,CAACI,IAAI,CAC/BC,IAAI,IAAI;MACJ,MAAM;QAAEC,QAAQ;QAAEC;MAAM,CAAC,GAAGF,IAAI;MAEhC,MAAMG,IAAI,GAAG;QACTF,QAAQ,EAAEA,QAAQ;QAClBG,cAAc,EAAEH,QAAQ;QACxBI,MAAM,EAAE,CAAEH,KAAK;MACnB,CAAC;MAED,MAAMI,MAAM,GAAG,IAAIC,oBAAW,CAACJ,IAAI,CAAC;MAEpCN,OAAO,CAACS,MAAM,CAAC;IACnB,CAAC,EACDE,KAAK,IAAI;MACLV,MAAM,CAAC,IAAIW,yBAAgB,CAACD,KAAK,CAAC,CAAC;IACvC,CACJ,CAAC;EACL,CAAC,CAAC;AACN"}