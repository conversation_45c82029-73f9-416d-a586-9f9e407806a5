import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Dimensions,
  Animated,
  PanResponder,
  Alert,
} from 'react-native';
import { AgoraService, RtcSurfaceView, ClientRoleType } from '../services/AgoraServiceFactory';
import BackendService from '../services/BackendService';
import { CONFIG } from '../config/index';

interface FullScreenStreamViewerProps {
  channelName: string;
  onClose: () => void;
  onStartBroadcast?: () => void;
}

const FullScreenStreamViewer: React.FC<FullScreenStreamViewerProps> = ({
  channelName,
  onClose,
  onStartBroadcast,
}) => {
  const [isWatching, setIsWatching] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [remoteUid, setRemoteUid] = useState<number | null>(null);
  const [streamInfo, setStreamInfo] = useState<any>(null);
  const [showControls, setShowControls] = useState(true);
  const [isMuted, setIsMuted] = useState(false);
  const [viewerSessionId, setViewerSessionId] = useState<string | null>(null);

  const agoraService = useRef(new AgoraService()).current;
  const backendService = useRef(new BackendService()).current;
  const [uid] = useState(Math.floor(Math.random() * 1000));
  const controlsOpacity = useRef(new Animated.Value(1)).current;
  const hideControlsTimer = useRef<NodeJS.Timeout | null>(null);

  // Get screen dimensions
  const { width, height } = Dimensions.get('window');

  // Pan responder for tap to show/hide controls
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => false,
      onPanResponderGrant: () => {
        console.log('Screen tapped, toggling controls');
        if (showControls) {
          hideControls();
        } else {
          setShowControls(true);
          showControlsWithTimer();
        }
      },
    })
  ).current;

  useEffect(() => {
    initializeStream();

    return () => {
      cleanup();
    };
  }, []);

  useEffect(() => {
    if (showControls) {
      showControlsWithTimer();
    }
  }, [showControls]);

  // Show controls initially and when component mounts
  useEffect(() => {
    setShowControls(true);
    showControlsWithTimer();
  }, []);

  const initializeStream = async () => {
    try {
      setIsLoading(true);
      setError(null);

      await agoraService.initialize(CONFIG.AGORA_APP_ID);

      // Set up event listeners
      const engine = agoraService.getEngine();
      if (engine) {
        engine.addListener('onUserJoined', (_: any, remoteUid: number) => {
          console.log('Remote user joined:', remoteUid);
          setRemoteUid(remoteUid);
        });

        engine.addListener('onUserOffline', (_: any, remoteUid: number) => {
          console.log('Remote user left:', remoteUid);
          setRemoteUid(null);
        });
      }

      // Check for active stream
      const activeStream = await backendService.getActiveStream(channelName);
      if (activeStream.id) {
        setStreamInfo(activeStream);
        await joinStream(activeStream.id);
      } else {
        setError('No active stream found for this channel');
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Failed to initialize stream:', error);
      setError('Failed to initialize streaming service');
      setIsLoading(false);
    }
  };

  const joinStream = async (streamId: string) => {
    try {
      // Record viewer join
      const session = await backendService.recordViewerJoin(streamId);
      setViewerSessionId(session.viewerId);

      // Join Agora channel
      await agoraService.joinChannel('', channelName, uid, ClientRoleType.ClientRoleAudience);
      setIsWatching(true);
    } catch (error) {
      console.error('Failed to join stream:', error);
      setError('Failed to join stream');
    }
  };

  const leaveStream = async () => {
    try {
      await agoraService.leaveChannel();

      if (viewerSessionId) {
        await backendService.recordViewerLeave(viewerSessionId);
      }

      setIsWatching(false);
      setRemoteUid(null);
      onClose();
    } catch (error) {
      console.error('Failed to leave stream:', error);
    }
  };

  const cleanup = async () => {
    if (isWatching) {
      await leaveStream();
    }
    if (hideControlsTimer.current) {
      clearTimeout(hideControlsTimer.current);
    }
  };

  const toggleControls = () => {
    setShowControls(!showControls);
  };

  const showControlsWithTimer = () => {
    console.log('Showing controls with timer');

    // Clear any existing timer
    if (hideControlsTimer.current) {
      clearTimeout(hideControlsTimer.current);
    }

    // Show controls immediately
    Animated.timing(controlsOpacity, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();

    // Hide controls after 5 seconds (increased from 3)
    hideControlsTimer.current = setTimeout(() => {
      console.log('Auto-hiding controls');
      hideControls();
    }, 5000);
  };

  const hideControls = () => {
    console.log('Hiding controls');

    // Clear timer
    if (hideControlsTimer.current) {
      clearTimeout(hideControlsTimer.current);
    }

    Animated.timing(controlsOpacity, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setShowControls(false);
    });
  };

  const toggleMute = () => {
    agoraService.muteLocalAudioStream(!isMuted);
    setIsMuted(!isMuted);
  };

  const handleStartBroadcast = () => {
    Alert.alert(
      'Start Broadcasting',
      'Do you want to start your own stream?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Start Stream',
          onPress: () => {
            if (onStartBroadcast) {
              onStartBroadcast();
            }
          }
        }
      ]
    );
  };

  const renderVideoContent = () => {
    if (isLoading) {
      return (
        <View style={styles.centerContent}>
          <Text style={styles.loadingText}>Loading stream...</Text>
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.centerContent}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={initializeStream}>
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }

    if (!remoteUid) {
      return (
        <View style={styles.centerContent}>
          <Text style={styles.waitingText}>Waiting for broadcaster...</Text>
        </View>
      );
    }

    return (
      <RtcSurfaceView
        style={styles.fullScreenVideo}
        canvas={{ uid: remoteUid }}
      />
    );
  };

  const renderControls = () => {
    if (!showControls) return null;

    return (
      <Animated.View style={[styles.controlsOverlay, { opacity: controlsOpacity }]}>
        {/* Top Controls */}
        <View style={styles.topControls}>
          <TouchableOpacity style={styles.closeButton} onPress={leaveStream}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>

          <View style={styles.streamInfo}>
            <Text style={styles.liveIndicator}>● LIVE</Text>
            <Text style={styles.channelName}>{channelName}</Text>
          </View>
        </View>

        {/* Bottom Controls */}
        <View style={styles.bottomControls}>
          <TouchableOpacity
            style={styles.controlButton}
            onPress={toggleMute}
          >
            <Text style={styles.controlButtonText}>
              {isMuted ? '🔇' : '🔊'}
            </Text>
          </TouchableOpacity>

          {onStartBroadcast && (
            <TouchableOpacity
              style={styles.broadcastButton}
              onPress={handleStartBroadcast}
            >
              <Text style={styles.broadcastButtonText}>📹 Go Live</Text>
            </TouchableOpacity>
          )}
        </View>
      </Animated.View>
    );
  };

  return (
    <View style={styles.container} {...panResponder.panHandlers}>
      <StatusBar hidden />

      {renderVideoContent()}
      {renderControls()}

      {/* Always visible tap indicator */}
      {!showControls && (
        <View style={styles.tapIndicator}>
          <Text style={styles.tapIndicatorText}>Tap to show controls</Text>
        </View>
      )}

      {/* Always visible back button */}
      <TouchableOpacity style={styles.alwaysVisibleBackButton} onPress={leaveStream}>
        <Text style={styles.alwaysVisibleBackButtonText}>✕</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  fullScreenVideo: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  loadingText: {
    color: '#fff',
    fontSize: 18,
    textAlign: 'center',
  },
  errorText: {
    color: '#ff4444',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  waitingText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  retryText: {
    color: '#fff',
    fontSize: 16,
  },
  controlsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
  },
  topControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  streamInfo: {
    alignItems: 'center',
  },
  liveIndicator: {
    color: '#ff4444',
    fontSize: 14,
    fontWeight: 'bold',
  },
  channelName: {
    color: '#fff',
    fontSize: 16,
    marginTop: 2,
  },
  bottomControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 50,
    paddingHorizontal: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  controlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlButtonText: {
    fontSize: 20,
  },
  broadcastButton: {
    backgroundColor: '#FF6B35',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
  },
  broadcastButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  tapIndicator: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginHorizontal: 50,
  },
  tapIndicatorText: {
    color: '#fff',
    fontSize: 12,
    opacity: 0.8,
  },
  alwaysVisibleBackButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  alwaysVisibleBackButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default FullScreenStreamViewer;
