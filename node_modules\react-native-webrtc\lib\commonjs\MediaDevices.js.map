{"version": 3, "names": ["_index", "require", "_reactNative", "_getDisplayMedia", "_interopRequireDefault", "_getUserMedia", "obj", "__esModule", "default", "WebRTCModule", "NativeModules", "MediaDevices", "EventTarget", "enumerateDevices", "Promise", "resolve", "getDisplayMedia", "getUserMedia", "constraints", "proto", "prototype", "defineEventAttribute", "_default", "exports"], "sources": ["MediaDevices.ts"], "sourcesContent": ["import { EventTarget, Event, defineEventAttribute } from 'event-target-shim/index';\nimport { NativeModules } from 'react-native';\n\nimport getDisplayMedia from './getDisplayMedia';\nimport getUserMedia, { Constraints } from './getUserMedia';\n\nconst { WebRTCModule } = NativeModules;\n\ntype MediaDevicesEventMap = {\n    devicechange: Event<'devicechange'>\n}\n\nclass MediaDevices extends EventTarget<MediaDevicesEventMap> {\n    /**\n     * W3C \"Media Capture and Streams\" compatible {@code enumerateDevices}\n     * implementation.\n     */\n    enumerateDevices() {\n        return new Promise(resolve => WebRTCModule.enumerateDevices(resolve));\n    }\n\n    /**\n     * W3C \"Screen Capture\" compatible {@code getDisplayMedia} implementation.\n     * See: https://w3c.github.io/mediacapture-screen-share/\n     *\n     * @returns {Promise}\n     */\n    getDisplayMedia() {\n        return getDisplayMedia();\n    }\n\n    /**\n     * W3C \"Media Capture and Streams\" compatible {@code getUserMedia}\n     * implementation.\n     * See: https://www.w3.org/TR/mediacapture-streams/#dom-mediadevices-enumeratedevices\n     *\n     * @param {*} constraints\n     * @returns {Promise}\n     */\n    getUserMedia(constraints: Constraints) {\n        return getUserMedia(constraints);\n    }\n}\n\n/**\n * Define the `onxxx` event handlers.\n */\nconst proto = MediaDevices.prototype;\n\ndefineEventAttribute(proto, 'devicechange');\n\n\nexport default new MediaDevices();\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,gBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,aAAA,GAAAD,sBAAA,CAAAH,OAAA;AAA2D,SAAAG,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE3D,MAAM;EAAEG;AAAa,CAAC,GAAGC,0BAAa;AAMtC,MAAMC,YAAY,SAASC,kBAAW,CAAuB;EACzD;AACJ;AACA;AACA;EACIC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAIN,YAAY,CAACI,gBAAgB,CAACE,OAAO,CAAC,CAAC;EACzE;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAAA,EAAG;IACd,OAAO,IAAAA,wBAAe,EAAC,CAAC;EAC5B;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,YAAYA,CAACC,WAAwB,EAAE;IACnC,OAAO,IAAAD,qBAAY,EAACC,WAAW,CAAC;EACpC;AACJ;;AAEA;AACA;AACA;AACA,MAAMC,KAAK,GAAGR,YAAY,CAACS,SAAS;AAEpC,IAAAC,2BAAoB,EAACF,KAAK,EAAE,cAAc,CAAC;AAAC,IAAAG,QAAA,GAG7B,IAAIX,YAAY,CAAC,CAAC;AAAAY,OAAA,CAAAf,OAAA,GAAAc,QAAA"}