{"version": 3, "file": "ExponentCamera.js", "sourceRoot": "", "sources": ["../src/ExponentCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,wBAAwB,EAAE,MAAM,mBAAmB,CAAC;AAK7D,MAAM,cAAc,GAClB,wBAAwB,CAAC,gBAAgB,CAAC,CAAC;AAE7C,eAAe,cAAc,CAAC", "sourcesContent": ["import { requireNativeViewManager } from 'expo-modules-core';\nimport * as React from 'react';\n\nimport { CameraNativeProps } from './Camera.types';\n\nconst ExponentCamera: React.ComponentType<CameraNativeProps> =\n  requireNativeViewManager('ExponentCamera');\n\nexport default ExponentCamera;\n"]}