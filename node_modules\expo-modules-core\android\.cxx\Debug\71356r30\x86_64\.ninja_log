# ninja log v5
7	506	7759272741162279	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/.trae/more/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o	f61d4e8db4736495
89	781	7759272743908946	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o	8da61f4e18adb59f
17	1327	7759272749345713	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o	1cbe7fa01bd2579e
61	588	7759272741994557	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/.trae/more/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o	9c8d92903c9aac36
45	1834	7759272754471150	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o	7996954eb8f03789
12	613	7759272742242453	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o	afd35243d1099223
79	1442	7759272750503372	CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o	4ba5e5f1e5cf2b68
55	1716	7759272753238660	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o	6a234273c3fe0754
31	1259	7759272748692340	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o	f9814cc888d4279
4	765	7759272743767427	CMakeFiles/expo-modules-core.dir/C_/Users/<USER>/.trae/more/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o	c9e99bcb9da4bd5d
67	1964	7759272755760010	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o	a17dabc937393394
15	1418	7759272750219623	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o	5059cf5db280b3a8
83	1474	7759272750853648	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o	d9ab594c025cbd51
23	1570	7759272751801418	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o	cb14889b22ccbefb
1	1676	7759272752884961	CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o	4099388072ebb041
107	1707	7759272753172944	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o	838c083686a400a8
102	1984	7759272755993564	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o	c7c39bbc805b10d6
35	1748	7759272753622070	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o	4ebb8c60e559c84b
40	2702	7759272763086567	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o	e8d454b71fea9ca3
28	1788	7759272754023848	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o	8e4a522951d16654
20	1887	7759272754978176	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIInteropModuleRegistry.cpp.o	f0b632176bfc8f5c
9	1915	7759272755275280	CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o	7cee9671979af920
50	2148	7759272757619240	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o	9f8a14bb08c60a3
96	2288	7759272759019270	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o	e7a96f9a3d67408e
73	2406	7759272760178417	CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o	f2da825a90617aab
2702	2798	7759272764089966	../../../../build/intermediates/cxx/Debug/71356r30/obj/x86_64/libexpo-modules-core.so	28bae4e1a6afe12a
