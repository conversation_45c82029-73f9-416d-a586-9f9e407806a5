{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,IAAI,MAAM,EAAE,MAAM,UAAU,CAAC;AAC7C,OAAO,EACL,SAAS,EACT,mBAAmB,EACnB,uBAAuB,EACvB,yBAAyB,EACzB,6BAA6B,EAC7B,6BAA6B,EAC7B,iCAAiC,GAClC,MAAM,UAAU,CAAC;AAElB,cAAc,gBAAgB,CAAC", "sourcesContent": ["export { default as Camera } from './Camera';\nexport {\n  Constants,\n  getPermissionsAsync,\n  requestPermissionsAsync,\n  getCameraPermissionsAsync,\n  requestCameraPermissionsAsync,\n  getMicrophonePermissionsAsync,\n  requestMicrophonePermissionsAsync,\n} from './Camera';\n\nexport * from './Camera.types';\n"]}