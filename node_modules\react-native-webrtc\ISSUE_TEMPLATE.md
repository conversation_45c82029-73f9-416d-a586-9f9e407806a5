<!--
If you have any questions then please use our Community Discourse: https://react-native-webrtc.discourse.group/  
Otherwise you are in the right place for reporting bugs.

Please include as much information as possible and make the issue title as descriptive as you can.
But most of all please take your time and explain to the best of your abilities.

Code samples can be very helpful but even more so if displayed correctly.
Here is an example of how you can use syntax highlighting.

```javascript

console.log( 'Hello World' );

```

More info can be found over here.
https://docs.github.com/en/get-started/writing-on-github/working-with-advanced-formatting/creating-and-highlighting-code-blocks#syntax-highlighting
-->

#### Expected Behavior:
<!--
Explain what you expect to happen.
-->

#### Observed Behavior:
<!--
Explain what is actually happening.
-->

#### Steps to reproduce the issue:
<!--
Explain how the issue can be reproduced.
If we manage to reproduce the issue then it is more likely to be addressed in an update.
-->

#### Platform Information  
* **React Native Version**: 
* **WebRTC Module Version**: 
* **Platform OS + Version**: <!-- Android / iOS / macOS / Web / Expo -->
