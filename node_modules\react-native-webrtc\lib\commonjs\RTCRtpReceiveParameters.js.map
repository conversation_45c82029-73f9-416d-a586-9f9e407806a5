{"version": 3, "names": ["_RTCRtpParameters", "_interopRequireDefault", "require", "obj", "__esModule", "default", "RTCRtpReceiveParameters", "RTCRtpParameters", "constructor", "init", "exports"], "sources": ["RTCRtpReceiveParameters.ts"], "sourcesContent": ["import RTCRtpParameters, { RTCRtpParametersInit } from './RTCRtpParameters';\n\nexport default class RTCRtpReceiveParameters extends RTCRtpParameters {\n    constructor(init: RTCRtpParametersInit) {\n        super(init);\n    }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,iBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA4E,SAAAD,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE7D,MAAMG,uBAAuB,SAASC,yBAAgB,CAAC;EAClEC,WAAWA,CAACC,IAA0B,EAAE;IACpC,KAAK,CAACA,IAAI,CAAC;EACf;AACJ;AAACC,OAAA,CAAAL,OAAA,GAAAC,uBAAA"}