{"version": 3, "names": ["_index", "require", "_defineProperty", "obj", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "MessageEvent", "Event", "constructor", "type", "eventInitDict", "data", "exports", "default"], "sources": ["MessageEvent.ts"], "sourcesContent": ["import { Event } from 'event-target-shim/index';\n\nexport type MessageEventData = string | ArrayBuffer | Blob;\n\ntype MESSAGE_EVENTS = 'message' | 'messageerror';\n\ninterface IMessageEventInitDict extends Event.EventInit {\n    data: MessageEventData;\n}\n\n/**\n * @eventClass\n * This event is fired whenever the RTCDataChannel send message.\n * @param {MESSAGE_EVENTS} type - The type of event.\n * @param {IMessageEventInitDict} eventInitDict - The event init properties.\n * @see\n * {@link https://developer.mozilla.org/en-US/docs/Web/API/RTCDataChannel/message_event#event_type MDN} for details.\n */\nexport default class MessageEvent<TEventType extends MESSAGE_EVENTS> extends Event<TEventType> {\n    /** @eventProperty */\n    data: MessageEventData;\n    constructor(type: TEventType, eventInitDict: IMessageEventInitDict) {\n        super(type, eventInitDict);\n        this.data = eventInitDict.data;\n    }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAAgD,SAAAC,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,QAAAD,GAAA,IAAAD,GAAA,IAAAG,MAAA,CAAAC,cAAA,CAAAJ,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAG,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAP,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAUhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,MAAMQ,YAAY,SAA4CC,YAAK,CAAa;EAC3F;;EAEAC,WAAWA,CAACC,IAAgB,EAAEC,aAAoC,EAAE;IAChE,KAAK,CAACD,IAAI,EAAEC,aAAa,CAAC;IAACb,eAAA;IAC3B,IAAI,CAACc,IAAI,GAAGD,aAAa,CAACC,IAAI;EAClC;AACJ;AAACC,OAAA,CAAAC,OAAA,GAAAP,YAAA"}