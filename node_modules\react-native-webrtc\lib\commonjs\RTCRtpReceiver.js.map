{"version": 3, "names": ["_reactNative", "require", "_RTCRtpReceiveParameters", "_interopRequireDefault", "obj", "__esModule", "default", "_defineProperty", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "WebRTCModule", "NativeModules", "RTCRtpReceiver", "constructor", "info", "_id", "id", "_peerConnectionId", "peerConnectionId", "_rtpParameters", "RTCRtpReceiveParameters", "rtpParameters", "track", "_track", "getCapabilities", "kind", "receiverGetCapabilities", "getStats", "receiverGetStats", "then", "data", "Map", "JSON", "parse", "getParameters", "exports"], "sources": ["RTCRtpReceiver.ts"], "sourcesContent": ["import { NativeModules } from 'react-native';\n\nimport MediaStreamTrack from './MediaStreamTrack';\nimport RTCRtpCapabilities from './RTCRtpCapabilities';\nimport { RTCRtpParametersInit } from './RTCRtpParameters';\nimport RTCRtpReceiveParameters from './RTCRtpReceiveParameters';\n\nconst { WebRTCModule } = NativeModules;\n\nexport default class RTCRtpReceiver {\n    _id: string;\n    _peerConnectionId: number;\n    _track: MediaStreamTrack | null = null;\n    _rtpParameters: RTCRtpReceiveParameters;\n\n    constructor(info: {\n        peerConnectionId: number,\n        id: string,\n        track?: MediaStreamTrack,\n        rtpParameters: RTCRtpParametersInit\n    }) {\n        this._id = info.id;\n        this._peerConnectionId = info.peerConnectionId;\n        this._rtpParameters = new RTCRtpReceiveParameters(info.rtpParameters);\n\n        if (info.track) {\n            this._track = info.track;\n        }\n    }\n\n    static getCapabilities(kind: 'audio' | 'video'): RTCRtpCapabilities {\n        return WebRTCModule.receiverGetCapabilities(kind);\n    }\n\n    getStats() {\n        return WebRTCModule.receiverGetStats(this._peerConnectionId, this._id).then(data =>\n            /* On both Android and iOS it is faster to construct a single\n            JSON string representing the Map of StatsReports and have it\n            pass through the React Native bridge rather than the Map of\n            StatsReports. While the implementations do try to be faster in\n            general, the stress is on being faster to pass through the React\n            Native bridge which is a bottleneck that tends to be visible in\n            the UI when there is congestion involving UI-related passing.\n            */\n            new Map(JSON.parse(data))\n        );\n    }\n\n    getParameters(): RTCRtpReceiveParameters {\n        return this._rtpParameters;\n    }\n\n    get id() {\n        return this._id;\n    }\n\n    get track() {\n        return this._track;\n    }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,wBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAgE,SAAAE,uBAAAC,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,gBAAAH,GAAA,EAAAI,GAAA,EAAAC,KAAA,QAAAD,GAAA,IAAAJ,GAAA,IAAAM,MAAA,CAAAC,cAAA,CAAAP,GAAA,EAAAI,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAG,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAV,GAAA,CAAAI,GAAA,IAAAC,KAAA,WAAAL,GAAA;AAEhE,MAAM;EAAEW;AAAa,CAAC,GAAGC,0BAAa;AAEvB,MAAMC,cAAc,CAAC;EAMhCC,WAAWA,CAACC,IAKX,EAAE;IAAAZ,eAAA;IAAAA,eAAA;IAAAA,eAAA,iBAR+B,IAAI;IAAAA,eAAA;IASlC,IAAI,CAACa,GAAG,GAAGD,IAAI,CAACE,EAAE;IAClB,IAAI,CAACC,iBAAiB,GAAGH,IAAI,CAACI,gBAAgB;IAC9C,IAAI,CAACC,cAAc,GAAG,IAAIC,gCAAuB,CAACN,IAAI,CAACO,aAAa,CAAC;IAErE,IAAIP,IAAI,CAACQ,KAAK,EAAE;MACZ,IAAI,CAACC,MAAM,GAAGT,IAAI,CAACQ,KAAK;IAC5B;EACJ;EAEA,OAAOE,eAAeA,CAACC,IAAuB,EAAsB;IAChE,OAAOf,YAAY,CAACgB,uBAAuB,CAACD,IAAI,CAAC;EACrD;EAEAE,QAAQA,CAAA,EAAG;IACP,OAAOjB,YAAY,CAACkB,gBAAgB,CAAC,IAAI,CAACX,iBAAiB,EAAE,IAAI,CAACF,GAAG,CAAC,CAACc,IAAI,CAACC,IAAI;IAC5E;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;IACY,IAAIC,GAAG,CAACC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC,CAC5B,CAAC;EACL;EAEAI,aAAaA,CAAA,EAA4B;IACrC,OAAO,IAAI,CAACf,cAAc;EAC9B;EAEA,IAAIH,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACD,GAAG;EACnB;EAEA,IAAIO,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;AACJ;AAACY,OAAA,CAAAlC,OAAA,GAAAW,cAAA"}