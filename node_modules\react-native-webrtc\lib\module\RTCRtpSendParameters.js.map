{"version": 3, "names": ["RTCRtpEncodingParameters", "RTCRtpParameters", "deepClone", "DegradationPreference", "fromNative", "nativeFormat", "stringFormat", "toLowerCase", "replace", "toNative", "format", "toUpperCase", "RTCRtpSendParameters", "constructor", "init", "_defineProperty", "transactionId", "encodings", "degradationPreference", "enc", "push", "toJSON", "obj", "map", "e"], "sources": ["RTCRtpSendParameters.ts"], "sourcesContent": ["import RTCRtpEncodingParameters, { RTCRtpEncodingParametersInit } from './RTCRtpEncodingParameters';\nimport RTCRtpParameters, { RTCRtpParametersInit } from './RTCRtpParameters';\nimport { deepClone } from './RTCUtil';\n\ntype DegradationPreferenceType = 'maintain-framerate'\n    | 'maintain-resolution'\n    | 'balanced'\n    | 'disabled'\n\n\n/**\n * Class to convert degradation preference format. Native has a format such as\n * MAINTAIN_FRAMERATE whereas the web APIs expect maintain-framerate\n */\nclass DegradationPreference {\n    static fromNative(nativeFormat: string): DegradationPreferenceType {\n        const stringFormat = nativeFormat.toLowerCase().replace('_', '-');\n\n        return stringFormat as DegradationPreferenceType;\n    }\n\n    static toNative(format: DegradationPreferenceType): string {\n        return format.toUpperCase().replace('-', '_');\n    }\n}\n\nexport interface RTCRtpSendParametersInit extends RTCRtpParametersInit {\n    transactionId: string;\n    encodings: RTCRtpEncodingParametersInit[];\n    degradationPreference?: string;\n}\n\nexport default class RTCRtpSendParameters extends RTCRtpParameters {\n    readonly transactionId: string;\n    encodings: (RTCRtpEncodingParameters | RTCRtpEncodingParametersInit)[];\n    degradationPreference: DegradationPreferenceType | null;\n\n    constructor(init: RTCRtpSendParametersInit) {\n        super(init);\n\n        this.transactionId = init.transactionId;\n        this.encodings = [];\n        this.degradationPreference = init.degradationPreference ?\n            DegradationPreference.fromNative(init.degradationPreference) : null;\n\n        for (const enc of init.encodings) {\n            this.encodings.push(new RTCRtpEncodingParameters(enc));\n        }\n    }\n\n    toJSON() {\n        const obj = super.toJSON();\n\n        obj['transactionId'] = this.transactionId;\n        obj['encodings'] = this.encodings.map(e => deepClone(e));\n\n        if (this.degradationPreference !== null) {\n            obj['degradationPreference'] = DegradationPreference.toNative(this.degradationPreference);\n        }\n\n        return obj;\n    }\n}\n"], "mappings": ";AAAA,OAAOA,wBAAwB,MAAwC,4BAA4B;AACnG,OAAOC,gBAAgB,MAAgC,oBAAoB;AAC3E,SAASC,SAAS,QAAQ,WAAW;AAQrC;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EACxB,OAAOC,UAAUA,CAACC,YAAoB,EAA6B;IAC/D,MAAMC,YAAY,GAAGD,YAAY,CAACE,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAEjE,OAAOF,YAAY;EACvB;EAEA,OAAOG,QAAQA,CAACC,MAAiC,EAAU;IACvD,OAAOA,MAAM,CAACC,WAAW,CAAC,CAAC,CAACH,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EACjD;AACJ;AAQA,eAAe,MAAMI,oBAAoB,SAASX,gBAAgB,CAAC;EAK/DY,WAAWA,CAACC,IAA8B,EAAE;IACxC,KAAK,CAACA,IAAI,CAAC;IAACC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAEZ,IAAI,CAACC,aAAa,GAAGF,IAAI,CAACE,aAAa;IACvC,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,qBAAqB,GAAGJ,IAAI,CAACI,qBAAqB,GACnDf,qBAAqB,CAACC,UAAU,CAACU,IAAI,CAACI,qBAAqB,CAAC,GAAG,IAAI;IAEvE,KAAK,MAAMC,GAAG,IAAIL,IAAI,CAACG,SAAS,EAAE;MAC9B,IAAI,CAACA,SAAS,CAACG,IAAI,CAAC,IAAIpB,wBAAwB,CAACmB,GAAG,CAAC,CAAC;IAC1D;EACJ;EAEAE,MAAMA,CAAA,EAAG;IACL,MAAMC,GAAG,GAAG,KAAK,CAACD,MAAM,CAAC,CAAC;IAE1BC,GAAG,CAAC,eAAe,CAAC,GAAG,IAAI,CAACN,aAAa;IACzCM,GAAG,CAAC,WAAW,CAAC,GAAG,IAAI,CAACL,SAAS,CAACM,GAAG,CAACC,CAAC,IAAItB,SAAS,CAACsB,CAAC,CAAC,CAAC;IAExD,IAAI,IAAI,CAACN,qBAAqB,KAAK,IAAI,EAAE;MACrCI,GAAG,CAAC,uBAAuB,CAAC,GAAGnB,qBAAqB,CAACM,QAAQ,CAAC,IAAI,CAACS,qBAAqB,CAAC;IAC7F;IAEA,OAAOI,GAAG;EACd;AACJ"}