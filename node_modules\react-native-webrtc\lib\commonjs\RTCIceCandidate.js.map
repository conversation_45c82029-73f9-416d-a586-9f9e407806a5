{"version": 3, "names": ["RTCIceCandidate", "constructor", "_ref", "candidate", "sdpMLineIndex", "sdpMid", "_defineProperty", "TypeError", "toJSON", "exports", "default"], "sources": ["RTCIceCandidate.ts"], "sourcesContent": ["interface RTCIceCandidateInfo {\n    candidate?: string;\n    sdpMLineIndex?: number | null;\n    sdpMid?: string | null;\n}\n\nexport default class RTCIceCandidate {\n    candidate: string;\n    sdpMLineIndex?: number | null;\n    sdpMid?: string | null;\n\n    constructor({ candidate = '', sdpMLineIndex = null, sdpMid = null }: RTCIceCandidateInfo) {\n        if (sdpMLineIndex === null && sdpMid === null) {\n            throw new TypeError('`sdpMLineIndex` and `sdpMid` must not be both null');\n        }\n\n        this.candidate = candidate;\n        this.sdpMLineIndex = sdpMLineIndex;\n        this.sdpMid = sdpMid;\n    }\n\n    toJSON() {\n        return {\n            candidate: this.candidate,\n            sdpMLineIndex: this.sdpMLineIndex,\n            sdpMid: this.sdpMid\n        };\n    }\n}\n"], "mappings": ";;;;;;;AAMe,MAAMA,eAAe,CAAC;EAKjCC,WAAWA,CAAAC,IAAA,EAA+E;IAAA,IAA9E;MAAEC,SAAS,GAAG,EAAE;MAAEC,aAAa,GAAG,IAAI;MAAEC,MAAM,GAAG;IAA0B,CAAC,GAAAH,IAAA;IAAAI,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACpF,IAAIF,aAAa,KAAK,IAAI,IAAIC,MAAM,KAAK,IAAI,EAAE;MAC3C,MAAM,IAAIE,SAAS,CAAC,oDAAoD,CAAC;IAC7E;IAEA,IAAI,CAACJ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EAEAG,MAAMA,CAAA,EAAG;IACL,OAAO;MACHL,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC;EACL;AACJ;AAACI,OAAA,CAAAC,OAAA,GAAAV,eAAA"}