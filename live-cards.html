<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Streams - TICK</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #2E7DFF;
            --primary-dark: #1A66E0;
            --primary-light: #EBF3FF;
            --secondary: #FF6B2E;
            --dark: #1A1A2E;
            --light: #F7F9FC;
            --gray: #8A94A6;
            --success: #2ECC71;
            --warning: #F39C12;
            --danger: #E74C3C;
            --card-shadow: 0 8px 16px rgba(0,0,0,0.05);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--light);
            color: var(--dark);
            max-width: 100vw;
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        
        .container {
            width: 100%;
            max-width: 480px;
            margin: 0 auto;
            padding: 0;
            display: flex;
            flex-direction: column;
            flex: 1;
            /* 9:16 ratio container */
            aspect-ratio: 9/16;
            overflow: hidden;
            position: relative;
        }
        
        /* Header */
        .header {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: white;
            z-index: 10;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .page-title {
            font-size: 20px;
            font-weight: 600;
        }
        
        .header-action {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--primary-light);
            color: var(--primary);
            font-size: 18px;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .header-action:hover {
            background-color: var(--primary);
            color: white;
        }
        
        /* Tabs */
        .tabs {
            display: flex;
            background-color: white;
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 5;
        }
        
        .tab {
            padding: 15px 0;
            margin-right: 20px;
            font-size: 14px;
            font-weight: 500;
            color: var(--gray);
            cursor: pointer;
            position: relative;
            transition: var(--transition);
        }
        
        .tab.active {
            color: var(--primary);
            font-weight: 600;
        }
        
        .tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: var(--primary);
            border-radius: 3px 3px 0 0;
        }
        
        /* Main Content */
        .main-content {
            flex: 1;
            overflow-y: auto;
            padding: 0 0 80px;
        }
        
        /* Tab Content */
        .tab-content {
            display: none;
            padding: 20px;
        }
        
        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }
        
        /* Featured Stream */
        .featured-stream {
            position: relative;
            border-radius: 16px;
            overflow: hidden;
            margin-bottom: 30px;
            box-shadow: var(--card-shadow);
        }
        
        .featured-thumbnail {
            width: 100%;
            aspect-ratio: 16/9;
            object-fit: cover;
        }
        
        .featured-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
            color: white;
        }
        
        .featured-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .featured-host {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
        }
        
        .host-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid white;
        }
        
        .host-name {
            font-size: 14px;
            font-weight: 500;
        }
        
        .featured-meta {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }
        
        .live-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            background-color: var(--danger);
            color: white;
            font-size: 12px;
            font-weight: 600;
            padding: 4px 10px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 5px;
            z-index: 2;
        }
        
        .live-badge .dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: white;
            animation: pulse 1.5s infinite;
        }
        
        .play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            transition: var(--transition);
            border: 2px solid white;
        }
        
        .play-button:hover {
            background-color: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%) scale(1.1);
        }
        
        /* Stream Categories */
        .categories {
            display: flex;
            gap: 10px;
            overflow-x: auto;
            padding-bottom: 10px;
            margin-bottom: 20px;
            scrollbar-width: none;
        }
        
        .categories::-webkit-scrollbar {
            display: none;
        }
        
        .category-chip {
            padding: 8px 16px;
            border-radius: 50px;
            background-color: white;
            border: 1px solid #DFE3EA;
            font-size: 14px;
            white-space: nowrap;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .category-chip.active {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        
        .category-chip:hover:not(.active) {
            background-color: var(--primary-light);
            border-color: var(--primary);
        }
        
        /* Stream Grid */
        .stream-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .stream-card {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .stream-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.1);
        }
        
        .stream-thumbnail-container {
            position: relative;
        }
        
        .stream-thumbnail {
            width: 100%;
            aspect-ratio: 16/9;
            object-fit: cover;
        }
        
        .stream-badge {
            position: absolute;
            top: 8px;
            left: 8px;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            z-index: 2;
        }
        
        .badge-live {
            background-color: var(--danger);
            color: white;
            display: flex;
            align-items: center;
            gap: 3px;
        }
        
        .badge-upcoming {
            background-color: var(--warning);
            color: white;
        }
        
        .badge-replay {
            background-color: var(--gray);
            color: white;
        }
        
        .stream-viewers {
            position: absolute;
            top: 8px;
            right: 8px;
            background-color: rgba(0,0,0,0.5);
            color: white;
            font-size: 10px;
            padding: 3px 6px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 3px;
        }
        
        .stream-info {
            padding: 12px;
        }
        
        .stream-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 42px;
        }
        
        .stream-host {
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .stream-host-avatar {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .stream-host-name {
            font-size: 12px;
            color: var(--gray);
        }
        
        /* Scheduled Streams */
        .scheduled-item {
            display: flex;
            gap: 15px;
            background-color: white;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: var(--card-shadow);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .scheduled-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.1);
        }
        
        .scheduled-date {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-width: 60px;
            padding: 10px;
            background-color: var(--primary-light);
            border-radius: 8px;
            color: var(--primary);
        }
        
        .date-day {
            font-size: 20px;
            font-weight: 700;
            line-height: 1;
        }
        
        .date-month {
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .scheduled-info {
            flex: 1;
        }
        
        .scheduled-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .scheduled-host {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 8px;
        }
        
        .scheduled-time {
            font-size: 12px;
            color: var(--gray);
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .scheduled-action {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .reminder-button {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: var(--primary-light);
            color: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .reminder-button:hover {
            background-color: var(--primary);
            color: white;
        }
        
        .reminder-button.active {
            background-color: var(--primary);
            color: white;
        }
        
        /* My Streams */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
        }
        
        .empty-icon {
            font-size: 60px;
            color: var(--gray);
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .empty-message {
            font-size: 14px;
            color: var(--gray);
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .empty-action {
            display: inline-block;
            padding: 12px 24px;
            background-color: var(--primary);
            color: white;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            border: none;
        }
        
        .empty-action:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
        }
        
        /* Go Live Button */
        .go-live-button {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: var(--danger);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
            cursor: pointer;
            transition: var(--transition);
            z-index: 90;
        }
        
        .go-live-button:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
        }
        
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: white;
            display: flex;
            justify-content: space-around;
            padding: 12px 0;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
            z-index: 100;
            max-width: 480px;
            margin: 0 auto;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            cursor: pointer;
            transition: var(--transition);
            padding: 5px 0;
            width: 60px;
        }
        
        .nav-item.active {
            color: var(--primary);
        }
        
        .nav-icon {
            font-size: 20px;
        }
        
        .nav-text {
            font-size: 12px;
            font-weight: 500;
        }
        
        /* Go Live Modal */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
        }
        
        .modal.active {
            opacity: 1;
            visibility: visible;
        }
        
        .modal-content {
            width: 90%;
            max-width: 400px;
            background-color: white;
            border-radius: 16px;
            overflow: hidden;
            transform: translateY(20px);
            transition: transform 0.3s ease-out;
        }
        
        .modal.active .modal-content {
            transform: translateY(0);
        }
        
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: var(--gray);
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--dark);
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border-radius: 8px;
            border: 1px solid #DFE3EA;
            font-family: 'Poppins', sans-serif;
            font-size: 14px;
            transition: all 0.2s;
            background-color: white;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(46, 125, 255, 0.15);
        }
        
        .form-input::placeholder {
            color: #B0B7C3;
        }
        
        textarea.form-input {
            min-height: 100px;
            resize: vertical;
        }
        
        .toggle-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .toggle-label {
            font-size: 14px;
            font-weight: 500;
        }
        
        .toggle-switch {
            position: relative;
            width: 50px;
            height: 26px;
        }
        
        .toggle-input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #E0E0E0;
            transition: .4s;
            border-radius: 34px;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 20px;
            width: 20px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        .toggle-input:checked + .toggle-slider {
            background-color: var(--primary);
        }
        
        .toggle-input:checked + .toggle-slider:before {
            transform: translateX(24px);
        }
        
        .toggle-description {
            font-size: 12px;
            color: var(--gray);
            margin-top: 5px;
        }
        
        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid rgba(0,0,0,0.05);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .modal-button {
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            border: none;
        }
        
        .btn-cancel {
            background-color: #F5F7FA;
            color: var(--gray);
        }
        
        .btn-cancel:hover {
            background-color: #E0E0E0;
        }
        
        .btn-go-live {
            background-color: var(--danger);
            color: white;
        }
        
        .btn-go-live:hover {
            background-color: #c0392b;
        }
        
        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="page-title">Live Streams</h1>
            <div class="header-action" id="searchButton">
                <i class="fas fa-search"></i>
            </div>
        </header>
        
        <div class="tabs">
            <div class="tab active" data-tab="discover">Discover</div>
            <div class="tab" data-tab="scheduled">Scheduled</div>
            <div class="tab" data-tab="following">Following</div>
            <div class="tab" data-tab="my-streams">My Streams</div>
        </div>
        
        <div class="main-content">
            <!-- Discover Tab -->
            <div class="tab-content active" id="discover-content">
                <div class="featured-stream" onclick="navigateTo('stream-details', {id: 1})">
                    <img src="https://images.unsplash.com/photo-1581858726788-75bc0f6a952d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="Featured Stream" class="featured-thumbnail">
                    <div class="live-badge">
                        <div class="dot"></div>
                        LIVE
                    </div>
                    <div class="play-button">
                        <i class="fas fa-play"></i>
                    </div>
                    <div class="featured-overlay">
                        <h2 class="featured-title">Kitchen Backsplash Installation Tips</h2>
                        <div class="featured-host">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Host" class="host-avatar">
                            <div class="host-name">Mike's Tile Co.</div>
                        </div>
                        <div class="featured-meta">
                            <div class="meta-item">
                                <i class="fas fa-eye"></i>
                                <span>128 watching</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-clock"></i>
                                <span>Started 45 min ago</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="categories">
                    <div class="category-chip active">All</div>
                    <div class="category-chip">Kitchen</div>
                    <div class="category-chip">Bathroom</div>
                    <div class="category-chip">Flooring</div>
                    <div class="category-chip">Painting</div>
                    <div class="category-chip">Electrical</div>
                    <div class="category-chip">Plumbing</div>
                    <div class="category-chip">Landscaping</div>
                </div>
                
                <div class="stream-grid">
                    <div class="stream-card" onclick="navigateTo('stream-details', {id: 2})">
                        <div class="stream-thumbnail-container">
                            <img src="https://images.unsplash.com/photo-1621905251189-08b45d6a269e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80" alt="Stream" class="stream-thumbnail">
                            <div class="stream-badge badge-live">
                                <div class="dot"></div>
                                LIVE
                            </div>
                            <div class="stream-viewers">
                                <i class="fas fa-eye"></i>
                                85
                            </div>
                        </div>
                        <div class="stream-info">
                            <h3 class="stream-title">DIY Deck Staining: Pro Techniques</h3>
                            <div class="stream-host">
                                <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="Host" class="stream-host-avatar">
                                <div class="stream-host-name">Lisa's Exteriors</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stream-card" onclick="navigateTo('stream-details', {id: 3})">
                        <div class="stream-thumbnail-container">
                            <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80" alt="Stream" class="stream-thumbnail">
                            <div class="stream-badge badge-live">
                                <div class="dot"></div>
                                LIVE
                            </div>
                            <div class="stream-viewers">
                                <i class="fas fa-eye"></i>
                                204
                            </div>
                        </div>
                        <div class="stream-info">
                            <h3 class="stream-title">Bathroom Remodel: Day 3 Progress</h3>
                            <div class="stream-host">
                                <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="Host" class="stream-host-avatar">
                                <div class="stream-host-name">Premier Remodeling</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stream-card" onclick="navigateTo('stream-details', {id: 4})">
                        <div class="stream-thumbnail-container">
                            <img src="https://images.unsplash.com/photo-1513694203232-719a280e022f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80" alt="Stream" class="stream-thumbnail">
                            <div class="stream-badge badge-replay">REPLAY</div>
                            <div class="stream-viewers">
                                <i class="fas fa-play"></i>
                                1.2K
                            </div>
                        </div>
                        <div class="stream-info">
                            <h3 class="stream-title">How to Install Luxury Vinyl Flooring</h3>
                            <div class="stream-host">
                                <img src="https://randomuser.me/api/portraits/men/36.jpg" alt="Host" class="stream-host-avatar">
                                <div class="stream-host-name">Floor Masters</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stream-card" onclick="navigateTo('stream-details', {id: 5})">
                        <div class="stream-thumbnail-container">
                            <img src="https://images.unsplash.com/photo-1556911220-bff31c812dba?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80" alt="Stream" class="stream-thumbnail">
                            <div class="stream-badge badge-replay">REPLAY</div>
                            <div class="stream-viewers">
                                <i class="fas fa-play"></i>
                                876
                            </div>
                        </div>
                        <div class="stream-info">
                            <h3 class="stream-title">Budget-Friendly Kitchen Updates</h3>
                            <div class="stream-host">
                                <img src="https://randomuser.me/api/portraits/women/42.jpg" alt="Host" class="stream-host-avatar">
                                <div class="stream-host-name">Modern Kitchens</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stream-card" onclick="navigateTo('stream-details', {id: 6})">
                        <div class="stream-thumbnail-container">
                            <img src="https://images.unsplash.com/photo-1595514535215-9a5e0e8e4f8c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80" alt="Stream" class="stream-thumbnail">
                            <div class="stream-badge badge-upcoming">UPCOMING</div>
                            <div class="stream-viewers">
                                <i class="fas fa-calendar"></i>
                                Jun 28
                            </div>
                        </div>
                        <div class="stream-info">
                            <h3 class="stream-title">Electrical Safety for DIY Projects</h3>
                            <div class="stream-host">
                                <img src="https://randomuser.me/api/portraits/men/22.jpg" alt="Host" class="stream-host-avatar">
                                <div class="stream-host-name">Safe Electric Co.</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stream-card" onclick="navigateTo('stream-details', {id: 7})">
                        <div class="stream-thumbnail-container">
                            <img src="https://images.unsplash.com/photo-1584622650111-993a426fbf0a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80" alt="Stream" class="stream-thumbnail">
                            <div class="stream-badge badge-live">
                                <div class="dot"></div>
                                LIVE
                            </div>
                            <div class="stream-viewers">
                                <i class="fas fa-eye"></i>
                                56
                            </div>
                        </div>
                        <div class="stream-info">
                            <h3 class="stream-title">Fixing Common Plumbing Issues</h3>
                            <div class="stream-host">
                                <img src="https://randomuser.me/api/portraits/men/52.jpg" alt="Host" class="stream-host-avatar">
                                <div class="stream-host-name">Quick Plumbing</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Scheduled Tab -->
            <div class="tab-content" id="scheduled-content">
                <div class="scheduled-item" onclick="navigateTo('stream-details', {id: 8})">
                    <div class="scheduled-date">
                        <div class="date-day">28</div>
                        <div class="date-month">Jun</div>
                    </div>
                    <div class="scheduled-info">
                        <h3 class="scheduled-title">Electrical Safety for DIY Projects</h3>
                        <div class="scheduled-host">
                            <img src="https://randomuser.me/api/portraits/men/22.jpg" alt="Host" class="stream-host-avatar">
                            <div class="stream-host-name">Safe Electric Co.</div>
                        </div>
                        <div class="scheduled-time">
                            <i class="fas fa-clock"></i>
                            <span>2:00 PM - 3:30 PM</span>
                        </div>
                    </div>
                    <div class="scheduled-action">
                        <button class="reminder-button" onclick="toggleReminder(event, 8)">
                            <i class="far fa-bell"></i>
                        </button>
                    </div>
                </div>
                
                <div class="scheduled-item" onclick="navigateTo('stream-details', {id: 9})">
                    <div class="scheduled-date">
                        <div class="date-day">30</div>
                        <div class="date-month">Jun</div>
                    </div>
                    <div class="scheduled-info">
                        <h3 class="scheduled-title">Modern Landscaping Ideas for Small Yards</h3>
                        <div class="scheduled-host">
                            <img src="https://randomuser.me/api/portraits/women/28.jpg" alt="Host" class="stream-host-avatar">
                            <div class="stream-host-name">Green Thumb Landscaping</div>
                        </div>
                        <div class="scheduled-time">
                            <i class="fas fa-clock"></i>
                            <span>10:00 AM - 11:00 AM</span>
                        </div>
                    </div>
                    <div class="scheduled-action">
                        <button class="reminder-button active" onclick="toggleReminder(event, 9)">
                            <i class="fas fa-bell"></i>
                        </button>
                    </div>
                </div>
                
                <div class="scheduled-item" onclick="navigateTo('stream-details', {id: 10})">
                    <div class="scheduled-date">
                        <div class="date-day">02</div>
                        <div class="date-month">Jul</div>
                    </div>
                    <div class="scheduled-info">
                        <h3 class="scheduled-title">Paint Like a Pro: Interior Painting Tips</h3>
                        <div class="scheduled-host">
                            <img src="https://randomuser.me/api/portraits/men/62.jpg" alt="Host" class="stream-host-avatar">
                            <div class="stream-host-name">Perfect Paint Co.</div>
                        </div>
                        <div class="scheduled-time">
                            <i class="fas fa-clock"></i>
                            <span>3:00 PM - 4:30 PM</span>
                        </div>
                    </div>
                    <div class="scheduled-action">
                        <button class="reminder-button" onclick="toggleReminder(event, 10)">
                            <i class="far fa-bell"></i>
                        </button>
                    </div>
                </div>
                
                <div class="scheduled-item" onclick="navigateTo('stream-details', {id: 11})">
                    <div class="scheduled-date">
                        <div class="date-day">05</div>
                        <div class="date-month">Jul</div>
                    </div>
                    <div class="scheduled-info">
                        <h3 class="scheduled-title">Bathroom Renovation on a Budget</h3>
                        <div class="scheduled-host">
                            <img src="https://randomuser.me/api/portraits/women/42.jpg" alt="Host" class="stream-host-avatar">
                            <div class="stream-host-name">Smart Renovations</div>
                        </div>
                        <div class="scheduled-time">
                            <i class="fas fa-clock"></i>
                            <span>1:00 PM - 2:30 PM</span>
                        </div>
                    </div>
                    <div class="scheduled-action">
                        <button class="reminder-button active" onclick="toggleReminder(event, 11)">
                            <i class="fas fa-bell"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Following Tab -->
            <div class="tab-content" id="following-content">
                <div class="stream-grid">
                    <div class="stream-card" onclick="navigateTo('stream-details', {id: 3})">
                        <div class="stream-thumbnail-container">
                            <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80" alt="Stream" class="stream-thumbnail">
                            <div class="stream-badge badge-live">
                                <div class="dot"></div>
                                LIVE
                            </div>
                            <div class="stream-viewers">
                                <i class="fas fa-eye"></i>
                                204
                            </div>
                        </div>
                        <div class="stream-info">
                            <h3 class="stream-title">Bathroom Remodel: Day 3 Progress</h3>
                            <div class="stream-host">
                                <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="Host" class="stream-host-avatar">
                                <div class="stream-host-name">Premier Remodeling</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stream-card" onclick="navigateTo('stream-details', {id: 5})">
                        <div class="stream-thumbnail-container">
                            <img src="https://images.unsplash.com/photo-1556911220-bff31c812dba?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=500&q=80" alt="Stream" class="stream-thumbnail">
                            <div class="stream-badge badge-replay">REPLAY</div>
                            <div class="stream-viewers">
                                <i class="fas fa-play"></i>
                                876
                            </div>
                        </div>
                        <div class="stream-info">
                            <h3 class="stream-title">Budget-Friendly Kitchen Updates</h3>
                            <div class="stream-host">
                                <img src="https://randomuser.me/api/portraits/women/42.jpg" alt="Host" class="stream-host-avatar">
                                <div class="stream-host-name">Modern Kitchens</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="scheduled-item" onclick="navigateTo('stream-details', {id: 9})">
                    <div class="scheduled-date">
                        <div class="date-day">30</div>
                        <div class="date-month">Jun</div>
                    </div>
                    <div class="scheduled-info">
                        <h3 class="scheduled-title">Modern Landscaping Ideas for Small Yards</h3>
                        <div class="scheduled-host">
                            <img src="https://randomuser.me/api/portraits/women/28.jpg" alt="Host" class="stream-host-avatar">
                            <div class="stream-host-name">Green Thumb Landscaping</div>
                        </div>
                        <div class="scheduled-time">
                            <i class="fas fa-clock"></i>
                            <span>10:00 AM - 11:00 AM</span>
                        </div>
                    </div>
                    <div class="scheduled-action">
                        <button class="reminder-button active" onclick="toggleReminder(event, 9)">
                            <i class="fas fa-bell"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- My Streams Tab -->
            <div class="tab-content" id="my-streams-content">
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-video-slash"></i>
                    </div>
                    <h3 class="empty-title">No Streams Yet</h3>
                    <p class="empty-message">You haven't created any live streams yet. Start sharing your expertise with the TICK community!</p>
                    <button class="empty-action" onclick="openGoLiveModal()">
                        <i class="fas fa-video"></i>
                        Start Your First Stream
                    </button>
                </div>
            </div>
        </div>
        
        <div class="go-live-button" onclick="openGoLiveModal()">
            <i class="fas fa-video"></i>
        </div>
        
        <nav class="bottom-nav">
            <div class="nav-item" onclick="navigateTo('dashboard')">
                <i class="fas fa-home nav-icon"></i>
                <span class="nav-text">Home</span>
            </div>
            <div class="nav-item" onclick="navigateTo('explore')">
                <i class="fas fa-compass nav-icon"></i>
                <span class="nav-text">Explore</span>
            </div>
            <div class="nav-item active">
                <i class="fas fa-video nav-icon"></i>
                <span class="nav-text">Live</span>
            </div>
            <div class="nav-item" onclick="navigateTo('projects')">
                <i class="fas fa-clipboard-list nav-icon"></i>
                <span class="nav-text">Projects</span>
            </div>
            <div class="nav-item" onclick="navigateTo('profile')">
                <i class="fas fa-user nav-icon"></i>
                <span class="nav-text">Profile</span>
            </div>
        </nav>
        
        <!-- Go Live Modal -->
        <div class="modal" id="goLiveModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title">Go Live</h2>
                    <button class="modal-close" onclick="closeGoLiveModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label" for="streamTitle">Stream Title</label>
                        <input type="text" id="streamTitle" class="form-input" placeholder="e.g., Kitchen Backsplash Installation Tips">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="streamDescription">Description</label>
                        <textarea id="streamDescription" class="form-input" placeholder="Describe what you'll be showing in this stream..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="streamCategory">Category</label>
                        <select id="streamCategory" class="form-input">
                            <option value="" disabled selected>Select a category</option>
                            <option value="kitchen">Kitchen</option>
                            <option value="bathroom">Bathroom</option>
                            <option value="flooring">Flooring</option>
                            <option value="painting">Painting</option>
                            <option value="electrical">Electrical</option>
                            <option value="plumbing">Plumbing</option>
                            <option value="landscaping">Landscaping</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    
                    <div class="toggle-wrapper">
                        <div>
                            <label class="toggle-label">Share to Social Media</label>
                            <p class="toggle-description">Automatically post to your connected accounts</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" class="toggle-input" id="shareToSocial" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    
                    <div class="toggle-wrapper">
                        <div>
                            <label class="toggle-label">AI Caption Generation</label>
                            <p class="toggle-description">Automatically generate captions during your stream</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" class="toggle-input" id="aiCaptions" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="modal-button btn-cancel" onclick="closeGoLiveModal()">Cancel</button>
                    <button class="modal-button btn-go-live" onclick="startLiveStream()">
                        <i class="fas fa-video"></i>
                        Go Live
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // DOM Elements
        const tabs = document.querySelectorAll('.tab');
        const tabContents = document.querySelectorAll('.tab-content');
        const categoryChips = document.querySelectorAll('.category-chip');
        const goLiveModal = document.getElementById('goLiveModal');
        
        // Initialize
        function init() {
            // Tab switching
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const tabId = tab.getAttribute('data-tab');
                    switchTab(tabId);
                });
            });
            
            // Category selection
            categoryChips.forEach(chip => {
                chip.addEventListener('click', () => {
                    categoryChips.forEach(c => c.classList.remove('active'));
                    chip.classList.add('active');
                });
            });
        }
        
        // Switch tab
        function switchTab(tabId) {
            // Update tab buttons
            tabs.forEach(tab => {
                if (tab.getAttribute('data-tab') === tabId) {
                    tab.classList.add('active');
                } else {
                    tab.classList.remove('active');
                }
            });
            
            // Update tab content
            tabContents.forEach(content => {
                if (content.id === `${tabId}-content`) {
                    content.classList.add('active');
                } else {
                    content.classList.remove('active');
                }
            });
        }
        
        // Toggle reminder
        function toggleReminder(event, streamId) {
            event.stopPropagation();
            const button = event.currentTarget;
            button.classList.toggle('active');
            
            const icon = button.querySelector('i');
            if (button.classList.contains('active')) {
                icon.className = 'fas fa-bell';
                showToast('Reminder set');
            } else {
                icon.className = 'far fa-bell';
                showToast('Reminder removed');
            }
        }
        
        // Show toast message
        function showToast(message) {
            // Create toast element
            const toast = document.createElement('div');
            toast.style.position = 'fixed';
            toast.style.bottom = '100px';
            toast.style.left = '50%';
            toast.style.transform = 'translateX(-50%)';
            toast.style.backgroundColor = 'rgba(0,0,0,0.7)';
            toast.style.color = 'white';
            toast.style.padding = '10px 20px';
            toast.style.borderRadius = '50px';
            toast.style.fontSize = '14px';
            toast.style.zIndex = '1000';
            toast.textContent = message;
            
            // Add to body
            document.body.appendChild(toast);
            
            // Remove after 2 seconds
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transition = 'opacity 0.5s';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 500);
            }, 2000);
        }
        
        // Open Go Live modal
        function openGoLiveModal() {
            goLiveModal.classList.add('active');
        }
        
        // Close Go Live modal
        function closeGoLiveModal() {
            goLiveModal.classList.remove('active');
        }
        
        // Start live stream
        function startLiveStream() {
            const streamTitle = document.getElementById('streamTitle').value;
            const streamDescription = document.getElementById('streamDescription').value;
            const streamCategory = document.getElementById('streamCategory').value;
            
            if (!streamTitle) {
                alert('Please enter a stream title');
                return;
            }
            
            if (!streamCategory) {
                alert('Please select a category');
                return;
            }
            
            // Close modal
            closeGoLiveModal();
            
            // Show loading toast
            showToast('Preparing your stream...');
            
            // Navigate to stream page after delay
            setTimeout(() => {
                navigateTo('stream-host', {
                    title: streamTitle,
                    description: streamDescription,
                    category: streamCategory
                });
            }, 2000);
        }
        
        // Initialize on page load
        init();
    </script>
</body>
</html>