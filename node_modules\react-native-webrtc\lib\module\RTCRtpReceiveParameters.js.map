{"version": 3, "names": ["RTCRtpParameters", "RTCRtpReceiveParameters", "constructor", "init"], "sources": ["RTCRtpReceiveParameters.ts"], "sourcesContent": ["import RTCRtpParameters, { RTCRtpParametersInit } from './RTCRtpParameters';\n\nexport default class RTCRtpReceiveParameters extends RTCRtpParameters {\n    constructor(init: RTCRtpParametersInit) {\n        super(init);\n    }\n}\n"], "mappings": "AAAA,OAAOA,gBAAgB,MAAgC,oBAAoB;AAE3E,eAAe,MAAMC,uBAAuB,SAASD,gBAAgB,CAAC;EAClEE,WAAWA,CAACC,IAA0B,EAAE;IACpC,KAAK,CAACA,IAAI,CAAC;EACf;AACJ"}