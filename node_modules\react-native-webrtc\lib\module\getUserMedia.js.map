{"version": 3, "names": ["NativeModules", "MediaStream", "MediaStreamError", "permissions", "RTCUtil", "WebRTCModule", "getUserMedia", "constraints", "arguments", "length", "undefined", "Promise", "reject", "TypeError", "audio", "video", "normalizeConstraints", "reqPermissions", "push", "request", "name", "resolve", "all", "then", "results", "audioPerm", "videoPerm", "error", "message", "success", "id", "tracks", "trackInfo", "c", "kind", "deepClone", "info", "streamId", "streamReactTag", "failure", "type"], "sources": ["getUserMedia.ts"], "sourcesContent": ["\nimport { NativeModules } from 'react-native';\n\n\nimport { MediaTrackConstraints } from './Constraints';\nimport MediaStream from './MediaStream';\nimport MediaStreamError from './MediaStreamError';\nimport permissions from './Permissions';\nimport * as RTCUtil from './RTCUtil';\n\nconst { WebRTCModule } = NativeModules;\n\nexport interface Constraints {\n    audio?: boolean | MediaTrackConstraints;\n    video?: boolean | MediaTrackConstraints;\n}\n\nexport default function getUserMedia(constraints: Constraints = {}): Promise<MediaStream> {\n    // According to\n    // https://www.w3.org/TR/mediacapture-streams/#dom-mediadevices-getusermedia,\n    // the constraints argument is a dictionary of type MediaStreamConstraints.\n    if (typeof constraints !== 'object') {\n        return Promise.reject(new TypeError('constraints is not a dictionary'));\n    }\n\n    if (\n        (typeof constraints.audio === 'undefined' || !constraints.audio) &&\n        (typeof constraints.video === 'undefined' || !constraints.video)\n    ) {\n        return Promise.reject(new TypeError('audio and/or video is required'));\n    }\n\n    // Normalize constraints.\n    constraints = RTCUtil.normalizeConstraints(constraints);\n\n    // Request required permissions\n    const reqPermissions: Promise<boolean>[] = [];\n\n    if (constraints.audio) {\n        reqPermissions.push(permissions.request({ name: 'microphone' }));\n    } else {\n        reqPermissions.push(Promise.resolve(false));\n    }\n\n    if (constraints.video) {\n        reqPermissions.push(permissions.request({ name: 'camera' }));\n    } else {\n        reqPermissions.push(Promise.resolve(false));\n    }\n\n    return new Promise((resolve, reject) => {\n        Promise.all(reqPermissions).then(results => {\n            const [ audioPerm, videoPerm ] = results;\n\n            // Check permission results and remove unneeded permissions.\n\n            if (!audioPerm && !videoPerm) {\n                // https://www.w3.org/TR/mediacapture-streams/#dom-mediadevices-getusermedia\n                // step 4\n                const error = {\n                    message: 'Permission denied.',\n                    name: 'SecurityError'\n                };\n\n                reject(new MediaStreamError(error));\n\n                return;\n            }\n\n            audioPerm || delete constraints.audio;\n            videoPerm || delete constraints.video;\n\n            const success = (id, tracks) => {\n                // Store initial constraints.\n                for (const trackInfo of tracks) {\n                    const c = constraints[trackInfo.kind];\n\n                    if (typeof c === 'object') {\n                        trackInfo.constraints = RTCUtil.deepClone(c);\n                    }\n                }\n\n                const info = {\n                    streamId: id,\n                    streamReactTag: id,\n                    tracks\n                };\n\n                resolve(new MediaStream(info));\n            };\n\n            const failure = (type, message) => {\n                let error;\n\n                switch (type) {\n                    case 'TypeError':\n                        error = new TypeError(message);\n                        break;\n                }\n\n                if (!error) {\n                    error = new MediaStreamError({ message, name: type });\n                }\n\n                reject(error);\n            };\n\n            WebRTCModule.getUserMedia(constraints, success, failure);\n        });\n    });\n}\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,cAAc;AAI5C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,KAAKC,OAAO,MAAM,WAAW;AAEpC,MAAM;EAAEC;AAAa,CAAC,GAAGL,aAAa;AAOtC,eAAe,SAASM,YAAYA,CAAA,EAAsD;EAAA,IAArDC,WAAwB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC9D;EACA;EACA;EACA,IAAI,OAAOD,WAAW,KAAK,QAAQ,EAAE;IACjC,OAAOI,OAAO,CAACC,MAAM,CAAC,IAAIC,SAAS,CAAC,iCAAiC,CAAC,CAAC;EAC3E;EAEA,IACI,CAAC,OAAON,WAAW,CAACO,KAAK,KAAK,WAAW,IAAI,CAACP,WAAW,CAACO,KAAK,MAC9D,OAAOP,WAAW,CAACQ,KAAK,KAAK,WAAW,IAAI,CAACR,WAAW,CAACQ,KAAK,CAAC,EAClE;IACE,OAAOJ,OAAO,CAACC,MAAM,CAAC,IAAIC,SAAS,CAAC,gCAAgC,CAAC,CAAC;EAC1E;;EAEA;EACAN,WAAW,GAAGH,OAAO,CAACY,oBAAoB,CAACT,WAAW,CAAC;;EAEvD;EACA,MAAMU,cAAkC,GAAG,EAAE;EAE7C,IAAIV,WAAW,CAACO,KAAK,EAAE;IACnBG,cAAc,CAACC,IAAI,CAACf,WAAW,CAACgB,OAAO,CAAC;MAAEC,IAAI,EAAE;IAAa,CAAC,CAAC,CAAC;EACpE,CAAC,MAAM;IACHH,cAAc,CAACC,IAAI,CAACP,OAAO,CAACU,OAAO,CAAC,KAAK,CAAC,CAAC;EAC/C;EAEA,IAAId,WAAW,CAACQ,KAAK,EAAE;IACnBE,cAAc,CAACC,IAAI,CAACf,WAAW,CAACgB,OAAO,CAAC;MAAEC,IAAI,EAAE;IAAS,CAAC,CAAC,CAAC;EAChE,CAAC,MAAM;IACHH,cAAc,CAACC,IAAI,CAACP,OAAO,CAACU,OAAO,CAAC,KAAK,CAAC,CAAC;EAC/C;EAEA,OAAO,IAAIV,OAAO,CAAC,CAACU,OAAO,EAAET,MAAM,KAAK;IACpCD,OAAO,CAACW,GAAG,CAACL,cAAc,CAAC,CAACM,IAAI,CAACC,OAAO,IAAI;MACxC,MAAM,CAAEC,SAAS,EAAEC,SAAS,CAAE,GAAGF,OAAO;;MAExC;;MAEA,IAAI,CAACC,SAAS,IAAI,CAACC,SAAS,EAAE;QAC1B;QACA;QACA,MAAMC,KAAK,GAAG;UACVC,OAAO,EAAE,oBAAoB;UAC7BR,IAAI,EAAE;QACV,CAAC;QAEDR,MAAM,CAAC,IAAIV,gBAAgB,CAACyB,KAAK,CAAC,CAAC;QAEnC;MACJ;MAEAF,SAAS,IAAI,OAAOlB,WAAW,CAACO,KAAK;MACrCY,SAAS,IAAI,OAAOnB,WAAW,CAACQ,KAAK;MAErC,MAAMc,OAAO,GAAGA,CAACC,EAAE,EAAEC,MAAM,KAAK;QAC5B;QACA,KAAK,MAAMC,SAAS,IAAID,MAAM,EAAE;UAC5B,MAAME,CAAC,GAAG1B,WAAW,CAACyB,SAAS,CAACE,IAAI,CAAC;UAErC,IAAI,OAAOD,CAAC,KAAK,QAAQ,EAAE;YACvBD,SAAS,CAACzB,WAAW,GAAGH,OAAO,CAAC+B,SAAS,CAACF,CAAC,CAAC;UAChD;QACJ;QAEA,MAAMG,IAAI,GAAG;UACTC,QAAQ,EAAEP,EAAE;UACZQ,cAAc,EAAER,EAAE;UAClBC;QACJ,CAAC;QAEDV,OAAO,CAAC,IAAIpB,WAAW,CAACmC,IAAI,CAAC,CAAC;MAClC,CAAC;MAED,MAAMG,OAAO,GAAGA,CAACC,IAAI,EAAEZ,OAAO,KAAK;QAC/B,IAAID,KAAK;QAET,QAAQa,IAAI;UACR,KAAK,WAAW;YACZb,KAAK,GAAG,IAAId,SAAS,CAACe,OAAO,CAAC;YAC9B;QACR;QAEA,IAAI,CAACD,KAAK,EAAE;UACRA,KAAK,GAAG,IAAIzB,gBAAgB,CAAC;YAAE0B,OAAO;YAAER,IAAI,EAAEoB;UAAK,CAAC,CAAC;QACzD;QAEA5B,MAAM,CAACe,KAAK,CAAC;MACjB,CAAC;MAEDtB,YAAY,CAACC,YAAY,CAACC,WAAW,EAAEsB,OAAO,EAAEU,OAAO,CAAC;IAC5D,CAAC,CAAC;EACN,CAAC,CAAC;AACN"}