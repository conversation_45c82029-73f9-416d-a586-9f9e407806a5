{"version": 3, "names": ["_index", "require", "_defineProperty", "obj", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "MediaStreamTrackEvent", "Event", "constructor", "type", "eventInitDict", "track", "exports", "default"], "sources": ["MediaStreamTrackEvent.ts"], "sourcesContent": ["import { Event } from 'event-target-shim/index';\n\nimport type MediaStreamTrack from './MediaStreamTrack';\n\ntype MEDIA_STREAM_EVENTS = 'addtrack'| 'removetrack'\n\ninterface IMediaStreamTrackEventInitDict extends Event.EventInit {\n  track: MediaStreamTrack;\n}\n\n/**\n * @eventClass\n * This event is fired whenever the MediaStreamTrack has changed in any way.\n * @param {MEDIA_STREAM_EVENTS} type - The type of event.\n * @param {IMediaStreamTrackEventInitDict} eventInitDict - The event init properties.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/MediaStream#events MDN} for details.\n */\nexport default class MediaStreamTrackEvent<TEventType extends MEDIA_STREAM_EVENTS> extends Event<TEventType> {\n    /** @eventProperty */\n    track: MediaStreamTrack;\n    constructor(type: TEventType, eventInitDict: IMediaStreamTrackEventInitDict) {\n        super(type, eventInitDict);\n        this.track = eventInitDict.track;\n    }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAAgD,SAAAC,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,QAAAD,GAAA,IAAAD,GAAA,IAAAG,MAAA,CAAAC,cAAA,CAAAJ,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAG,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAP,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAUhD;AACA;AACA;AACA;AACA;AACA;AACA;AACe,MAAMQ,qBAAqB,SAAiDC,YAAK,CAAa;EACzG;;EAEAC,WAAWA,CAACC,IAAgB,EAAEC,aAA6C,EAAE;IACzE,KAAK,CAACD,IAAI,EAAEC,aAAa,CAAC;IAACb,eAAA;IAC3B,IAAI,CAACc,KAAK,GAAGD,aAAa,CAACC,KAAK;EACpC;AACJ;AAACC,OAAA,CAAAC,OAAA,GAAAP,qBAAA"}