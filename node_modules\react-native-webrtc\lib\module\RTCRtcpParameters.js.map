{"version": 3, "names": ["RTCRtcpParameters", "constructor", "init", "_defineProperty", "cname", "reducedSize", "Object", "freeze", "toJSON"], "sources": ["RTCRtcpParameters.ts"], "sourcesContent": ["export interface RTCRtcpParametersInit {\n    cname: string;\n    reducedSize: boolean;\n}\n\nexport default class RTCRtcpParameters {\n    readonly cname: string;\n    readonly reducedSize: boolean;\n\n    constructor(init: RTCRtcpParametersInit) {\n        this.cname = init.cname;\n        this.reducedSize = init.reducedSize;\n\n        Object.freeze(this);\n    }\n\n    toJSON(): RTCRtcpParametersInit {\n        return {\n            cname: this.cname,\n            reducedSize: this.reducedSize\n        };\n    }\n}\n"], "mappings": ";AAKA,eAAe,MAAMA,iBAAiB,CAAC;EAInCC,WAAWA,CAACC,IAA2B,EAAE;IAAAC,eAAA;IAAAA,eAAA;IACrC,IAAI,CAACC,KAAK,GAAGF,IAAI,CAACE,KAAK;IACvB,IAAI,CAACC,WAAW,GAAGH,IAAI,CAACG,WAAW;IAEnCC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACvB;EAEAC,MAAMA,CAAA,EAA0B;IAC5B,OAAO;MACHJ,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,WAAW,EAAE,IAAI,CAACA;IACtB,CAAC;EACL;AACJ"}