{"version": 3, "names": ["Event", "RTCErrorEvent", "constructor", "type", "func", "message", "_defineProperty"], "sources": ["RTCErrorEvent.ts"], "sourcesContent": ["import { Event } from 'event-target-shim/index';\n\ntype RTCPeerConnectionErrorFunc =\n    | 'addTransceiver'\n    | 'getTransceivers'\n    | 'addTrack'\n    | 'removeTrack';\n\n/**\n * @brief This class Represents internal error happening on the native side as\n * part of asynchronous invocations to synchronous web APIs.\n */\nexport default class RTCErrorEvent<TEventType extends RTCPeerConnectionErrorFunc> extends Event<TEventType> {\n    readonly func: RTCPeerConnectionErrorFunc;\n    readonly message: string;\n    constructor(type: TEventType, func: RTCPeerConnectionErrorFunc, message: string) {\n        super(type);\n        this.func = func;\n        this.message = message;\n    }\n}"], "mappings": ";AAAA,SAASA,KAAK,QAAQ,yBAAyB;AAQ/C;AACA;AACA;AACA;AACA,eAAe,MAAMC,aAAa,SAAwDD,KAAK,CAAa;EAGxGE,WAAWA,CAACC,IAAgB,EAAEC,IAAgC,EAAEC,OAAe,EAAE;IAC7E,KAAK,CAACF,IAAI,CAAC;IAACG,eAAA;IAAAA,eAAA;IACZ,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;AACJ"}