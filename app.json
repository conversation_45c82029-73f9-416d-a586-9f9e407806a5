{"expo": {"name": "streaming-app", "slug": "streaming-app", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "light", "splash": {"resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.yourcompany.streamingapp"}, "android": {"package": "com.yourcompany.streamingapp", "permissions": ["CAMERA", "RECORD_AUDIO", "MODIFY_AUDIO_SETTINGS", "ACCESS_NETWORK_STATE", "INTERNET"]}, "extra": {"eas": {"projectId": "da2b840f-a5fd-4f33-ab39-8853ab905061"}}, "plugins": ["expo-dev-client", ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera for live streaming.", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone for live streaming.", "recordAudioAndroid": true}]], "platforms": ["ios", "android"]}}