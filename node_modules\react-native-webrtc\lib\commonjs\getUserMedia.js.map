{"version": 3, "names": ["_reactNative", "require", "_MediaStream", "_interopRequireDefault", "_MediaStreamError", "_Permissions", "RTCUtil", "_interopRequireWildcard", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "WebRTCModule", "NativeModules", "getUserMedia", "constraints", "arguments", "length", "undefined", "Promise", "reject", "TypeError", "audio", "video", "normalizeConstraints", "reqPermissions", "push", "permissions", "request", "name", "resolve", "all", "then", "results", "audioPerm", "videoPerm", "error", "message", "MediaStreamError", "success", "id", "tracks", "trackInfo", "c", "kind", "deepClone", "info", "streamId", "streamReactTag", "MediaStream", "failure", "type"], "sources": ["getUserMedia.ts"], "sourcesContent": ["\nimport { NativeModules } from 'react-native';\n\n\nimport { MediaTrackConstraints } from './Constraints';\nimport MediaStream from './MediaStream';\nimport MediaStreamError from './MediaStreamError';\nimport permissions from './Permissions';\nimport * as RTCUtil from './RTCUtil';\n\nconst { WebRTCModule } = NativeModules;\n\nexport interface Constraints {\n    audio?: boolean | MediaTrackConstraints;\n    video?: boolean | MediaTrackConstraints;\n}\n\nexport default function getUserMedia(constraints: Constraints = {}): Promise<MediaStream> {\n    // According to\n    // https://www.w3.org/TR/mediacapture-streams/#dom-mediadevices-getusermedia,\n    // the constraints argument is a dictionary of type MediaStreamConstraints.\n    if (typeof constraints !== 'object') {\n        return Promise.reject(new TypeError('constraints is not a dictionary'));\n    }\n\n    if (\n        (typeof constraints.audio === 'undefined' || !constraints.audio) &&\n        (typeof constraints.video === 'undefined' || !constraints.video)\n    ) {\n        return Promise.reject(new TypeError('audio and/or video is required'));\n    }\n\n    // Normalize constraints.\n    constraints = RTCUtil.normalizeConstraints(constraints);\n\n    // Request required permissions\n    const reqPermissions: Promise<boolean>[] = [];\n\n    if (constraints.audio) {\n        reqPermissions.push(permissions.request({ name: 'microphone' }));\n    } else {\n        reqPermissions.push(Promise.resolve(false));\n    }\n\n    if (constraints.video) {\n        reqPermissions.push(permissions.request({ name: 'camera' }));\n    } else {\n        reqPermissions.push(Promise.resolve(false));\n    }\n\n    return new Promise((resolve, reject) => {\n        Promise.all(reqPermissions).then(results => {\n            const [ audioPerm, videoPerm ] = results;\n\n            // Check permission results and remove unneeded permissions.\n\n            if (!audioPerm && !videoPerm) {\n                // https://www.w3.org/TR/mediacapture-streams/#dom-mediadevices-getusermedia\n                // step 4\n                const error = {\n                    message: 'Permission denied.',\n                    name: 'SecurityError'\n                };\n\n                reject(new MediaStreamError(error));\n\n                return;\n            }\n\n            audioPerm || delete constraints.audio;\n            videoPerm || delete constraints.video;\n\n            const success = (id, tracks) => {\n                // Store initial constraints.\n                for (const trackInfo of tracks) {\n                    const c = constraints[trackInfo.kind];\n\n                    if (typeof c === 'object') {\n                        trackInfo.constraints = RTCUtil.deepClone(c);\n                    }\n                }\n\n                const info = {\n                    streamId: id,\n                    streamReactTag: id,\n                    tracks\n                };\n\n                resolve(new MediaStream(info));\n            };\n\n            const failure = (type, message) => {\n                let error;\n\n                switch (type) {\n                    case 'TypeError':\n                        error = new TypeError(message);\n                        break;\n                }\n\n                if (!error) {\n                    error = new MediaStreamError({ message, name: type });\n                }\n\n                reject(error);\n            };\n\n            WebRTCModule.getUserMedia(constraints, success, failure);\n        });\n    });\n}\n"], "mappings": ";;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AAIA,IAAAC,YAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,iBAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,YAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,OAAA,GAAAC,uBAAA,CAAAN,OAAA;AAAqC,SAAAO,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAF,wBAAAM,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAAA,SAAAhB,uBAAAU,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAErC,MAAM;EAAEiB;AAAa,CAAC,GAAGC,0BAAa;AAOvB,SAASC,YAAYA,CAAA,EAAsD;EAAA,IAArDC,WAAwB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC9D;EACA;EACA;EACA,IAAI,OAAOD,WAAW,KAAK,QAAQ,EAAE;IACjC,OAAOI,OAAO,CAACC,MAAM,CAAC,IAAIC,SAAS,CAAC,iCAAiC,CAAC,CAAC;EAC3E;EAEA,IACI,CAAC,OAAON,WAAW,CAACO,KAAK,KAAK,WAAW,IAAI,CAACP,WAAW,CAACO,KAAK,MAC9D,OAAOP,WAAW,CAACQ,KAAK,KAAK,WAAW,IAAI,CAACR,WAAW,CAACQ,KAAK,CAAC,EAClE;IACE,OAAOJ,OAAO,CAACC,MAAM,CAAC,IAAIC,SAAS,CAAC,gCAAgC,CAAC,CAAC;EAC1E;;EAEA;EACAN,WAAW,GAAG3B,OAAO,CAACoC,oBAAoB,CAACT,WAAW,CAAC;;EAEvD;EACA,MAAMU,cAAkC,GAAG,EAAE;EAE7C,IAAIV,WAAW,CAACO,KAAK,EAAE;IACnBG,cAAc,CAACC,IAAI,CAACC,oBAAW,CAACC,OAAO,CAAC;MAAEC,IAAI,EAAE;IAAa,CAAC,CAAC,CAAC;EACpE,CAAC,MAAM;IACHJ,cAAc,CAACC,IAAI,CAACP,OAAO,CAACW,OAAO,CAAC,KAAK,CAAC,CAAC;EAC/C;EAEA,IAAIf,WAAW,CAACQ,KAAK,EAAE;IACnBE,cAAc,CAACC,IAAI,CAACC,oBAAW,CAACC,OAAO,CAAC;MAAEC,IAAI,EAAE;IAAS,CAAC,CAAC,CAAC;EAChE,CAAC,MAAM;IACHJ,cAAc,CAACC,IAAI,CAACP,OAAO,CAACW,OAAO,CAAC,KAAK,CAAC,CAAC;EAC/C;EAEA,OAAO,IAAIX,OAAO,CAAC,CAACW,OAAO,EAAEV,MAAM,KAAK;IACpCD,OAAO,CAACY,GAAG,CAACN,cAAc,CAAC,CAACO,IAAI,CAACC,OAAO,IAAI;MACxC,MAAM,CAAEC,SAAS,EAAEC,SAAS,CAAE,GAAGF,OAAO;;MAExC;;MAEA,IAAI,CAACC,SAAS,IAAI,CAACC,SAAS,EAAE;QAC1B;QACA;QACA,MAAMC,KAAK,GAAG;UACVC,OAAO,EAAE,oBAAoB;UAC7BR,IAAI,EAAE;QACV,CAAC;QAEDT,MAAM,CAAC,IAAIkB,yBAAgB,CAACF,KAAK,CAAC,CAAC;QAEnC;MACJ;MAEAF,SAAS,IAAI,OAAOnB,WAAW,CAACO,KAAK;MACrCa,SAAS,IAAI,OAAOpB,WAAW,CAACQ,KAAK;MAErC,MAAMgB,OAAO,GAAGA,CAACC,EAAE,EAAEC,MAAM,KAAK;QAC5B;QACA,KAAK,MAAMC,SAAS,IAAID,MAAM,EAAE;UAC5B,MAAME,CAAC,GAAG5B,WAAW,CAAC2B,SAAS,CAACE,IAAI,CAAC;UAErC,IAAI,OAAOD,CAAC,KAAK,QAAQ,EAAE;YACvBD,SAAS,CAAC3B,WAAW,GAAG3B,OAAO,CAACyD,SAAS,CAACF,CAAC,CAAC;UAChD;QACJ;QAEA,MAAMG,IAAI,GAAG;UACTC,QAAQ,EAAEP,EAAE;UACZQ,cAAc,EAAER,EAAE;UAClBC;QACJ,CAAC;QAEDX,OAAO,CAAC,IAAImB,oBAAW,CAACH,IAAI,CAAC,CAAC;MAClC,CAAC;MAED,MAAMI,OAAO,GAAGA,CAACC,IAAI,EAAEd,OAAO,KAAK;QAC/B,IAAID,KAAK;QAET,QAAQe,IAAI;UACR,KAAK,WAAW;YACZf,KAAK,GAAG,IAAIf,SAAS,CAACgB,OAAO,CAAC;YAC9B;QACR;QAEA,IAAI,CAACD,KAAK,EAAE;UACRA,KAAK,GAAG,IAAIE,yBAAgB,CAAC;YAAED,OAAO;YAAER,IAAI,EAAEsB;UAAK,CAAC,CAAC;QACzD;QAEA/B,MAAM,CAACgB,KAAK,CAAC;MACjB,CAAC;MAEDxB,YAAY,CAACE,YAAY,CAACC,WAAW,EAAEwB,OAAO,EAAEW,OAAO,CAAC;IAC5D,CAAC,CAAC;EACN,CAAC,CAAC;AACN"}