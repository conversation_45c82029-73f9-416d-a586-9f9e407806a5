{"version": 3, "names": ["Event", "MediaStreamTrackEvent", "constructor", "type", "eventInitDict", "_defineProperty", "track"], "sources": ["MediaStreamTrackEvent.ts"], "sourcesContent": ["import { Event } from 'event-target-shim/index';\n\nimport type MediaStreamTrack from './MediaStreamTrack';\n\ntype MEDIA_STREAM_EVENTS = 'addtrack'| 'removetrack'\n\ninterface IMediaStreamTrackEventInitDict extends Event.EventInit {\n  track: MediaStreamTrack;\n}\n\n/**\n * @eventClass\n * This event is fired whenever the MediaStreamTrack has changed in any way.\n * @param {MEDIA_STREAM_EVENTS} type - The type of event.\n * @param {IMediaStreamTrackEventInitDict} eventInitDict - The event init properties.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/MediaStream#events MDN} for details.\n */\nexport default class MediaStreamTrackEvent<TEventType extends MEDIA_STREAM_EVENTS> extends Event<TEventType> {\n    /** @eventProperty */\n    track: MediaStreamTrack;\n    constructor(type: TEventType, eventInitDict: IMediaStreamTrackEventInitDict) {\n        super(type, eventInitDict);\n        this.track = eventInitDict.track;\n    }\n}\n"], "mappings": ";AAAA,SAASA,KAAK,QAAQ,yBAAyB;AAU/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMC,qBAAqB,SAAiDD,KAAK,CAAa;EACzG;;EAEAE,WAAWA,CAACC,IAAgB,EAAEC,aAA6C,EAAE;IACzE,KAAK,CAACD,IAAI,EAAEC,aAAa,CAAC;IAACC,eAAA;IAC3B,IAAI,CAACC,KAAK,GAAGF,aAAa,CAACE,KAAK;EACpC;AACJ"}