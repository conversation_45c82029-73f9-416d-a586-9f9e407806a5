1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.streamingapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="33" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:5:3-75
11-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:5:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:3:3-64
12-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:3:20-62
13    <uses-permission android:name="android.permission.VIBRATE" /> <!-- These require runtime permissions on M -->
13-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:6:3-63
13-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:6:20-61
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:8:3-77
14-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:8:20-75
15    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- END OPTIONAL PERMISSIONS -->
15-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:9:3-78
15-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:9:20-76
16    <queries>
16-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:12:3-19:13
17
18        <!-- Support checking for http(s) links via the Linking API -->
19        <intent>
19-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:14:5-18:14
20            <action android:name="android.intent.action.VIEW" />
20-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:15:7-59
20-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:15:15-56
21
22            <category android:name="android.intent.category.BROWSABLE" />
22-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:16:7-68
22-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:16:17-65
23
24            <data android:scheme="https" />
24-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:17:7-38
24-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:17:13-35
25        </intent>
26        <!-- Query open documents -->
27        <intent>
27-->[:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:9-17:18
28            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
28-->[:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-79
28-->[:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:21-76
29        </intent>
30    </queries>
31
32    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
32-->[com.facebook.flipper:flipper:0.182.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd18304b20e6fdaf3e3425e5d35bd51e\transformed\jetified-flipper-0.182.0\AndroidManifest.xml:16:5-76
32-->[com.facebook.flipper:flipper:0.182.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd18304b20e6fdaf3e3425e5d35bd51e\transformed\jetified-flipper-0.182.0\AndroidManifest.xml:16:22-73
33    <uses-permission android:name="android.permission.CAMERA" />
33-->[:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-65
33-->[:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-62
34    <uses-permission android:name="android.permission.RECORD_AUDIO" />
34-->[:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-71
34-->[:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-68
35    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
35-->[:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-80
35-->[:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:22-77
36    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
36-->[:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:5-79
36-->[:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:22-76
37    <uses-permission android:name="android.permission.BLUETOOTH" />
37-->[:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:5-68
37-->[:react-native-agora] C:\Users\<USER>\.trae\more\node_modules\react-native-agora\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:22-65
38    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
38-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\808043cb4cf2ad49d6936bcf600e8cf8\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
38-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\808043cb4cf2ad49d6936bcf600e8cf8\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
39    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
39-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\808043cb4cf2ad49d6936bcf600e8cf8\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
39-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\808043cb4cf2ad49d6936bcf600e8cf8\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
40    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
40-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:7:5-77
40-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:7:22-74
41    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
41-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:8:5-94
41-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:8:22-91
42    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
42-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\75c6c7e98755245c38048373f05ae40b\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
42-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\transforms-3\75c6c7e98755245c38048373f05ae40b\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
43
44    <application
44-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:3-31:17
45        android:name="com.streamingapp.MainApplication"
45-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:16-47
46        android:allowBackup="false"
46-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:162-189
47        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
47-->[androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\01b98716bd18db7c75d98a3148ec3aaa\transformed\core-1.8.0\AndroidManifest.xml:24:18-86
48        android:debuggable="true"
49        android:icon="@mipmap/ic_launcher"
49-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:81-115
50        android:label="@string/app_name"
50-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:48-80
51        android:roundIcon="@mipmap/ic_launcher_round"
51-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:116-161
52        android:theme="@style/AppTheme"
52-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:21:190-221
53        android:usesCleartextTraffic="true" >
53-->C:\Users\<USER>\.trae\more\android\app\src\debug\AndroidManifest.xml:6:18-53
54        <meta-data
54-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:22:5-103
55            android:name="expo.modules.updates.EXPO_UPDATE_URL"
55-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:22:16-67
56            android:value="YOUR-APP-URL-HERE" />
56-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:22:68-101
57        <meta-data
57-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:23:5-112
58            android:name="expo.modules.updates.EXPO_SDK_VERSION"
58-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:23:16-68
59            android:value="YOUR-APP-SDK-VERSION-HERE" />
59-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:23:69-110
60
61        <activity
61-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:24:5-29:16
62            android:name="com.streamingapp.MainActivity"
62-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:24:15-43
63            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|uiMode"
63-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:24:77-154
64            android:exported="true"
64-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:24:276-299
65            android:label="@string/app_name"
65-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:24:44-76
66            android:launchMode="singleTask"
66-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:24:155-186
67            android:theme="@style/Theme.App.SplashScreen"
67-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:24:230-275
68            android:windowSoftInputMode="adjustResize" >
68-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:24:187-229
69            <intent-filter>
69-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:25:7-28:23
70                <action android:name="android.intent.action.MAIN" />
70-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:26:9-60
70-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:26:17-58
71
72                <category android:name="android.intent.category.LAUNCHER" />
72-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:27:9-68
72-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:27:19-66
73            </intent-filter>
74        </activity>
75        <activity
75-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:30:5-106
76            android:name="com.facebook.react.devsupport.DevSettingsActivity"
76-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:30:15-79
77            android:exported="false" />
77-->C:\Users\<USER>\.trae\more\android\app\src\main\AndroidManifest.xml:30:80-104
78
79        <provider
79-->[:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:9-30:20
80            android:name="expo.modules.filesystem.FileSystemFileProvider"
80-->[:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-74
81            android:authorities="com.streamingapp.FileSystemFileProvider"
81-->[:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-74
82            android:exported="false"
82-->[:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-37
83            android:grantUriPermissions="true" >
83-->[:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:13-47
84            <meta-data
84-->[:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-29:70
85                android:name="android.support.FILE_PROVIDER_PATHS"
85-->[:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:28:17-67
86                android:resource="@xml/file_system_provider_paths" />
86-->[:expo-file-system] C:\Users\<USER>\.trae\more\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-67
87        </provider>
88
89        <meta-data
89-->[:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-11:89
90            android:name="org.unimodules.core.AppLoader#react-native-headless"
90-->[:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-79
91            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
91-->[:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-86
92        <meta-data
92-->[:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-15:45
93            android:name="com.facebook.soloader.enabled"
93-->[:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-57
94            android:value="true" />
94-->[:expo-modules-core] C:\Users\<USER>\.trae\more\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-33
95
96        <provider
96-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:26:9-34:20
97            android:name="androidx.startup.InitializationProvider"
97-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:27:13-67
98            android:authorities="com.streamingapp.androidx-startup"
98-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:28:13-68
99            android:exported="false" >
99-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:29:13-37
100            <meta-data
100-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:31:13-33:52
101                android:name="androidx.emoji2.text.EmojiCompatInitializer"
101-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:32:17-75
102                android:value="androidx.startup" />
102-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9098b5419212ff6601713e861a89a2d7\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:33:17-49
103            <meta-data
103-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\37237e168ff21f74c2fa7449816f191f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
104                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
104-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\37237e168ff21f74c2fa7449816f191f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
105                android:value="androidx.startup" />
105-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\37237e168ff21f74c2fa7449816f191f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
106            <meta-data
106-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
107                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
108                android:value="androidx.startup" />
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
109        </provider>
110
111        <receiver
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
112            android:name="androidx.profileinstaller.ProfileInstallReceiver"
112-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
113            android:directBootAware="false"
113-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
114            android:enabled="true"
114-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
115            android:exported="true"
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
116            android:permission="android.permission.DUMP" >
116-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
117            <intent-filter>
117-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
118                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
118-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
118-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
119            </intent-filter>
120            <intent-filter>
120-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
121                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
121-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
121-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
122            </intent-filter>
123            <intent-filter>
123-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
124                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
124-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
124-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
125            </intent-filter>
126            <intent-filter>
126-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
127                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
127-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
127-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\78a26ccc04b8399851fb3c6d61540a03\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
128            </intent-filter>
129        </receiver>
130
131        <activity
131-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:11:9-14:63
132            android:name="io.agora.rtc2.extensions.MediaProjectionMgr$LocalScreenCaptureAssistantActivity"
132-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:11:19-89
133            android:configChanges="screenSize|orientation"
133-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:12:13-59
134            android:screenOrientation="unspecified"
134-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:13:13-52
135            android:theme="@android:style/Theme.Translucent" />
135-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:14:13-61
136
137        <service
137-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:16:9-19:19
138            android:name="io.agora.rtc2.extensions.MediaProjectionMgr$LocalScreenSharingService"
138-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:17:13-73
139            android:foregroundServiceType="mediaProjection" >
139-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\6fa9a7c236de8ee07ff13e2bbc103af4\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:18:13-60
140        </service>
141    </application>
142
143</manifest>
