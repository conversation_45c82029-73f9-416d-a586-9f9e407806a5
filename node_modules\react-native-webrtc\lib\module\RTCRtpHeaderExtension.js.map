{"version": 3, "names": ["RTCRtpHeaderExtension", "constructor", "init", "_defineProperty", "id", "uri", "encrypted", "Object", "freeze", "toJSON"], "sources": ["RTCRtpHeaderExtension.ts"], "sourcesContent": ["export interface RTCRtpHeaderExtensionInit {\n    id: number;\n    uri: string;\n    encrypted: boolean;\n}\n\nexport default class RTCRtpHeaderExtension {\n    readonly id: number;\n    readonly uri: string;\n    readonly encrypted: boolean;\n\n    constructor(init: RTCRtpHeaderExtensionInit) {\n        this.id = init.id;\n        this.uri = init.uri;\n        this.encrypted = init.encrypted;\n\n        Object.freeze(this);\n    }\n\n    toJSON(): RTCRtpHeaderExtensionInit {\n        return {\n            id: this.id,\n            uri: this.uri,\n            encrypted: this.encrypted\n        };\n    }\n}\n"], "mappings": ";AAMA,eAAe,MAAMA,qBAAqB,CAAC;EAKvCC,WAAWA,CAACC,IAA+B,EAAE;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACzC,IAAI,CAACC,EAAE,GAAGF,IAAI,CAACE,EAAE;IACjB,IAAI,CAACC,GAAG,GAAGH,IAAI,CAACG,GAAG;IACnB,IAAI,CAACC,SAAS,GAAGJ,IAAI,CAACI,SAAS;IAE/BC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACvB;EAEAC,MAAMA,CAAA,EAA8B;IAChC,OAAO;MACHL,EAAE,EAAE,IAAI,CAACA,EAAE;MACXC,GAAG,EAAE,IAAI,CAACA,GAAG;MACbC,SAAS,EAAE,IAAI,CAACA;IACpB,CAAC;EACL;AACJ"}