{"version": 3, "names": ["RTCRtpCodecParameters", "constructor", "init", "_defineProperty", "payloadType", "clockRate", "mimeType", "channels", "sdpFmtpLine", "Object", "freeze", "toJSON", "obj", "exports", "default"], "sources": ["RTCRtpCodecParameters.ts"], "sourcesContent": ["export interface RTCRtpCodecParametersInit {\n    payloadType: number;\n    clockRate: number;\n    mimeType: string;\n    channels?: number;\n    sdpFmtpLine?: string;\n}\n\nexport default class RTCRtpCodecParameters {\n    readonly payloadType: number;\n    readonly clockRate: number;\n    readonly mimeType: string;\n    readonly channels: number | null;\n    readonly sdpFmtpLine: string | null;\n\n    constructor(init: RTCRtpCodecParametersInit) {\n        this.payloadType = init.payloadType;\n        this.clockRate = init.clockRate;\n        this.mimeType = init.mimeType;\n\n        this.channels = init.channels ? init.channels : null;\n        this.sdpFmtpLine = init.sdpFmtpLine ? init.sdpFmtpLine : null;\n\n        Object.freeze(this);\n    }\n\n    toJSON(): RTCRtpCodecParametersInit {\n        const obj = {\n            payloadType: this.payloadType,\n            clockRate: this.clockRate,\n            mimeType: this.mimeType\n        };\n\n        if (this.channels !== null) {\n            obj['channels'] = this.channels;\n        }\n\n        if (this.sdpFmtpLine !== null) {\n            obj['sdpFmtpLine'] = this.sdpFmtpLine;\n        }\n\n        return obj;\n    }\n}\n"], "mappings": ";;;;;;;AAQe,MAAMA,qBAAqB,CAAC;EAOvCC,WAAWA,CAACC,IAA+B,EAAE;IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACzC,IAAI,CAACC,WAAW,GAAGF,IAAI,CAACE,WAAW;IACnC,IAAI,CAACC,SAAS,GAAGH,IAAI,CAACG,SAAS;IAC/B,IAAI,CAACC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;IAE7B,IAAI,CAACC,QAAQ,GAAGL,IAAI,CAACK,QAAQ,GAAGL,IAAI,CAACK,QAAQ,GAAG,IAAI;IACpD,IAAI,CAACC,WAAW,GAAGN,IAAI,CAACM,WAAW,GAAGN,IAAI,CAACM,WAAW,GAAG,IAAI;IAE7DC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACvB;EAEAC,MAAMA,CAAA,EAA8B;IAChC,MAAMC,GAAG,GAAG;MACRR,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,QAAQ,EAAE,IAAI,CAACA;IACnB,CAAC;IAED,IAAI,IAAI,CAACC,QAAQ,KAAK,IAAI,EAAE;MACxBK,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAACL,QAAQ;IACnC;IAEA,IAAI,IAAI,CAACC,WAAW,KAAK,IAAI,EAAE;MAC3BI,GAAG,CAAC,aAAa,CAAC,GAAG,IAAI,CAACJ,WAAW;IACzC;IAEA,OAAOI,GAAG;EACd;AACJ;AAACC,OAAA,CAAAC,OAAA,GAAAd,qBAAA"}