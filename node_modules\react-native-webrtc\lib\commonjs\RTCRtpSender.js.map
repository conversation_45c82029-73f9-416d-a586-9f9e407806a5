{"version": 3, "names": ["_reactNative", "require", "_RTCRtpSendParameters", "_interopRequireDefault", "obj", "__esModule", "default", "_defineProperty", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "WebRTCModule", "NativeModules", "RTCRtpSender", "constructor", "info", "_peerConnectionId", "peerConnectionId", "_id", "id", "_rtpParameters", "RTCRtpSendParameters", "rtpParameters", "track", "_track", "replaceTrack", "senderReplaceTrack", "e", "getCapabilities", "kind", "senderGetCapabilities", "getParameters", "setParameters", "parameters", "_params", "JSON", "parse", "stringify", "newParameters", "senderSetParameters", "getStats", "senderGetStats", "then", "data", "Map", "exports"], "sources": ["RTCRtpSender.ts"], "sourcesContent": ["import { NativeModules } from 'react-native';\n\nimport MediaStreamTrack from './MediaStreamTrack';\nimport RTCRtpCapabilities from './RTCRtpCapabilities';\nimport RTCRtpSendParameters, { RTCRtpSendParametersInit } from './RTCRtpSendParameters';\n\nconst { WebRTCModule } = NativeModules;\n\n\nexport default class RTCRtpSender {\n    _id: string;\n    _track: MediaStreamTrack | null = null;\n    _peerConnectionId: number;\n    _rtpParameters: RTCRtpSendParameters;\n\n    constructor(info: {\n        peerConnectionId: number,\n        id: string,\n        track?: MediaStreamTrack,\n        rtpParameters: RTCRtpSendParametersInit\n    }) {\n        this._peerConnectionId = info.peerConnectionId;\n        this._id = info.id;\n        this._rtpParameters = new RTCRtpSendParameters(info.rtpParameters);\n\n        if (info.track) {\n            this._track = info.track;\n        }\n    }\n\n    async replaceTrack(track: MediaStreamTrack | null): Promise<void> {\n        try {\n            await WebRTCModule.senderReplaceTrack(this._peerConnectionId, this._id, track ? track.id : null);\n        } catch (e) {\n            return;\n        }\n\n        this._track = track;\n    }\n\n    static getCapabilities(kind: 'audio' | 'video'): RTCRtpCapabilities {\n        return WebRTCModule.senderGetCapabilities(kind);\n    }\n\n    getParameters(): RTCRtpSendParameters {\n        return this._rtpParameters;\n    }\n\n    async setParameters(parameters: RTCRtpSendParameters): Promise<void> {\n        // This allows us to get rid of private \"underscore properties\"\n        const _params = JSON.parse(JSON.stringify(parameters));\n        const newParameters = await WebRTCModule.senderSetParameters(this._peerConnectionId, this._id, _params);\n\n        this._rtpParameters = new RTCRtpSendParameters(newParameters);\n    }\n\n    getStats() {\n        return WebRTCModule.senderGetStats(this._peerConnectionId, this._id).then(data =>\n            /* On both Android and iOS it is faster to construct a single\n            JSON string representing the Map of StatsReports and have it\n            pass through the React Native bridge rather than the Map of\n            StatsReports. While the implementations do try to be faster in\n            general, the stress is on being faster to pass through the React\n            Native bridge which is a bottleneck that tends to be visible in\n            the UI when there is congestion involving UI-related passing.\n            */\n            new Map(JSON.parse(data))\n        );\n    }\n\n    get track() {\n        return this._track;\n    }\n\n    get id() {\n        return this._id;\n    }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAIA,IAAAC,qBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAwF,SAAAE,uBAAAC,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,gBAAAH,GAAA,EAAAI,GAAA,EAAAC,KAAA,QAAAD,GAAA,IAAAJ,GAAA,IAAAM,MAAA,CAAAC,cAAA,CAAAP,GAAA,EAAAI,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAG,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAV,GAAA,CAAAI,GAAA,IAAAC,KAAA,WAAAL,GAAA;AAExF,MAAM;EAAEW;AAAa,CAAC,GAAGC,0BAAa;AAGvB,MAAMC,YAAY,CAAC;EAM9BC,WAAWA,CAACC,IAKX,EAAE;IAAAZ,eAAA;IAAAA,eAAA,iBAT+B,IAAI;IAAAA,eAAA;IAAAA,eAAA;IAUlC,IAAI,CAACa,iBAAiB,GAAGD,IAAI,CAACE,gBAAgB;IAC9C,IAAI,CAACC,GAAG,GAAGH,IAAI,CAACI,EAAE;IAClB,IAAI,CAACC,cAAc,GAAG,IAAIC,6BAAoB,CAACN,IAAI,CAACO,aAAa,CAAC;IAElE,IAAIP,IAAI,CAACQ,KAAK,EAAE;MACZ,IAAI,CAACC,MAAM,GAAGT,IAAI,CAACQ,KAAK;IAC5B;EACJ;EAEA,MAAME,YAAYA,CAACF,KAA8B,EAAiB;IAC9D,IAAI;MACA,MAAMZ,YAAY,CAACe,kBAAkB,CAAC,IAAI,CAACV,iBAAiB,EAAE,IAAI,CAACE,GAAG,EAAEK,KAAK,GAAGA,KAAK,CAACJ,EAAE,GAAG,IAAI,CAAC;IACpG,CAAC,CAAC,OAAOQ,CAAC,EAAE;MACR;IACJ;IAEA,IAAI,CAACH,MAAM,GAAGD,KAAK;EACvB;EAEA,OAAOK,eAAeA,CAACC,IAAuB,EAAsB;IAChE,OAAOlB,YAAY,CAACmB,qBAAqB,CAACD,IAAI,CAAC;EACnD;EAEAE,aAAaA,CAAA,EAAyB;IAClC,OAAO,IAAI,CAACX,cAAc;EAC9B;EAEA,MAAMY,aAAaA,CAACC,UAAgC,EAAiB;IACjE;IACA,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACJ,UAAU,CAAC,CAAC;IACtD,MAAMK,aAAa,GAAG,MAAM3B,YAAY,CAAC4B,mBAAmB,CAAC,IAAI,CAACvB,iBAAiB,EAAE,IAAI,CAACE,GAAG,EAAEgB,OAAO,CAAC;IAEvG,IAAI,CAACd,cAAc,GAAG,IAAIC,6BAAoB,CAACiB,aAAa,CAAC;EACjE;EAEAE,QAAQA,CAAA,EAAG;IACP,OAAO7B,YAAY,CAAC8B,cAAc,CAAC,IAAI,CAACzB,iBAAiB,EAAE,IAAI,CAACE,GAAG,CAAC,CAACwB,IAAI,CAACC,IAAI;IAC1E;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;IACY,IAAIC,GAAG,CAACT,IAAI,CAACC,KAAK,CAACO,IAAI,CAAC,CAC5B,CAAC;EACL;EAEA,IAAIpB,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EAEA,IAAIL,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACD,GAAG;EACnB;AACJ;AAAC2B,OAAA,CAAA3C,OAAA,GAAAW,YAAA"}